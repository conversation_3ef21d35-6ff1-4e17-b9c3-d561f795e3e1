kind: Deployment
apiVersion: apps/v1
metadata:
  name: alibaba-consumer-offline-dev
  namespace: app-crm
  labels:
    app: alibaba-consumer-offline-dev
    developer: PHP-CRM
    lang: php
  annotations:
    deployment.kubernetes.io/revision: '2'
    kubesphere.io/creator: evelynjiang
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alibaba-consumer-offline-dev
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: alibaba-consumer-offline-dev
        pod-template-hash: 88db7695c
        service: alibaba-consumer-offline
      annotations:
        kubectl.kubernetes.io/restartedAt: '2024-05-22T10:32:52+08:00'
        kubesphere.io/creator: evelynjiang
        kubesphere.io/restartedAt: '2024-11-25T01:45:42.206Z'
    spec:
      volumes:
        - name: volume-config
          configMap:
            name: sidecar-queue-configmap
            defaultMode: 420
        - name: volume-uds
          emptyDir: {}
        - name: volume-data
          persistentVolumeClaim:
            claimName: crm-codebase-pvc
      containers:
        - name: alibaba-consumer-offline-dev
          image: >-
            xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/php8:yii
          command:
            - /usr/local/php/bin/php
            - '-c/usr/local/php/etc/php-cli.ini'
            - >-
              /data/codebase/crm_root/dev/bugfix/1107824/php-crm/protected/yiic-test
            - alibaba
            - AlibabaOfflineConsumer
          env:
            - name: RUNTIME_ENV
              value: k8s
            - name: RUNTIME_APP
              value: alibaba-consumer
            - name: aliyun_logs_queue-service
              value: stdout
            - name: aliyun_logs_queue-service_tags
              value: app=queue
            - name: aliyun_logs_php-error
              value: /data/logs/php_errors.log
            - name: aliyun_logs_php-error_tags
              value: app=queue
            - name: CLI.grpc.enable_fork_support
              value: '0'
            - name: PHP.opcache.huge_code_pages
              value: '0'
            - name: CLI.opcache.huge_code_pages
              value: '0'
            - name: SIDECAR_ENABLE
              value: '1'
            - name: AUTH_CONSUMER_COUNT
              value: '1'
            - name: CHAT_CONSUMER_COUNT
              value: '10'
            - name: ORDER_CONSUMER_COUNT
              value: '10'
            - name: TASK_CONSUMER_COUNT
              value: '1'
            - name: DELAY_CONSUMER_COUNT
              value: '5'
            - name: CUSTOMER_CONSUMER_COUNT
              value: '20'
            - name: NOTE_CONSUMER_COUNT
              value: '15'
            - name: CUSTOM_CONSUMER_COUNT
              value: '40'
            - name: OFFLINE_CONSUMER_COUNT
              value: '20'
            - name: DISABLE_SINGLE_DB_CACHE
              value: '0'
            - name: OFFLINE_LIMIT
              value: '0'
          resources:
            limits:
              cpu: '2'
              ephemeral-storage: 1Gi
              memory: 8Gi
            requests:
              cpu: 20m
              ephemeral-storage: 100Mi
              memory: 68719476736m
          volumeMounts:
            - name: volume-uds
              mountPath: /var/run/sidecar
            - name: volume-data
              mountPath: /data/codebase
              mountPropagation: Bidirectional
          livenessProbe:
            exec:
              command:
                - sh
                - '-c'
                - pgrep -f AlibabaOfflineConsumer
            initialDelaySeconds: 10
            timeoutSeconds: 1
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - sh
                - '-c'
                - pgrep -f AlibabaOfflineConsumer
            initialDelaySeconds: 10
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          lifecycle:
            preStop:
              exec:
                command:
                  - sleep 10
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
          securityContext:
            capabilities:
              add:
                - SYS_ADMIN
                - SYS_PTRACE
            privileged: true
            allowPrivilegeEscalation: true
        - name: sidecar
          image: >-
            xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/pod_agent:stable
          env:
            - name: SENTINEL_APP_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: 'metadata.labels[''service'']'
            - name: SPRING_PROFILES_ACTIVE
              value: prod
            - name: aliyun_logs_infra-services
              value: stdout
          resources:
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 25m
              memory: 53687091200m
          volumeMounts:
            - name: volume-uds
              mountPath: /var/run/sidecar
            - name: volume-config
              readOnly: true
              mountPath: /app/config.yaml
              subPath: config.yaml
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: None
      securityContext: {}
      schedulerName: default-scheduler
      dnsConfig:
        nameservers:
          - 169.254.20.10
          - 192.168.96.10
        searches:
          - app-crm.svc.cluster.local
          - svc.cluster.local
          - cluster.local
        options:
          - name: ndots
            value: '3'
          - name: timeout
            value: '1'
          - name: attempts
            value: '2'
          - name: single-request-reopen
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
