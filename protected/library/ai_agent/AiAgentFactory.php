<?php

namespace common\library\ai_agent;

use AiAgent;
use common\library\ai_agent\proxy\AiAgentProxy;
use Exception;

class AiAgentFactory
{
    /**
     * @param int $sceneType
     * @param int $clientId
     * @param int $userId
     * @param int $businessType
     * @return BaseAiAgent
     */
    public static function createAgent(
        int $sceneType,
        int $clientId,
        int $userId,
        int $businessType = AiAgentConstants::BUSINESS_TYPE_CRM,
        string $question = '',
        array $params = []
    ): BaseAiAgent | AiAgentProxy
    {
        if ($businessType === AiAgentConstants::BUSINESS_TYPE_SHOP) {
            return self::createShopAgent($sceneType, $clientId, $userId);
        }

        if (in_array($sceneType, array_keys(AiAgentProxy::PROXY_SCENE_TYPE_MAP))) {
            return match ($sceneType) {
                AiAgent::AI_AGENT_SCENE_TYPE_DATA_ASSISTANT_PROXY => new DataAssistantProxy($clientId, $userId, $sceneType,$question,$params)
            };
        }

        return match ($sceneType) {

            AiAgent::AI_AGENT_SCENE_TYPE_DATA_ANALYSIS => new DataAnalysisAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE => new MailWriteAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH => new MailPolishAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_FAST_ARCHIVE => new FastArchiveAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP => new OpportunityFollowUpAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP => new CustomerFollowUpAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA => new ReportGenerateAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_QUALITY => new ChatQualityAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY => new NewChatReplyAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH => new NewChatCoachAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS => new AssetAnalysisAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_QUALITY => new MailQualityAiAgent($clientId,$userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY => new MailReplyAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY => new MailSummaryAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH => new ChatPolishAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_TEAM_ANALYSIS => new TeamAnalysisAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_STATISTICAL_ANALYSIS_INSIGHT => new InsightAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUBJECT => new MailSubjectAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_EDM_MAIL_SUBJECT => new EdmMailSubjectAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK => new CompanyQualityCheckAiAgentV2($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_ALI_BASIC_MAIL_WRITE => new AiBasicMailWriteAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_ALI_BASIC_MAIL_POLISH => new AiBasicMailPolishAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_INQUIRY => new InquiryAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_KNOWLEDGE_BASE => new KnowledgeBaseAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_SITE_QUALITY => new SiteQualityAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_DEPTH_ANALYSIS => new DepthAnalysisAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_COMMUNICATE_ANALYSIS => new CommunicateAnalysisAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MARKETING_AUTOMATION_MULTI_MAIL_CONTENT => new  MarketingAutomationMultiMailContentAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MARKETING_AUTOMATION_MULTI_MAIL_SUBJECT => new  MarketingAutomationMultiMailSubjectAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_REPORT_INTERPRET => new ReportInterpretAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_MAIL_AUTO_SUMMARY => new MailAutoSummaryAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_SALES_ASSISTANT_ANALYSIS => new SalesAssistantAnalysisAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_PRODUCT_USAGE_ANALYSIS => new ProductUsageAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS => new SdrLeadQualityAnalysisAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION => new AiSdrSellerProfileAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION => new AiSdrBuyerProfileAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_NET_GENERATION => new AiSdrSteamProfileAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH => new HomepageAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY => new AiSdrProductCategoryAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER => new SdrEdmWriteAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_LEADS_NOT_MATCH => new AiSdrLeadsNotMatchAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_REVIEW_PRODUCT_USAGE => new ReviewProductUsageAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE => new SdrSellerIndustryAnalyze($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_PRODUCT_USAGE_ANALYSIS_V2 => new ProductUsageV2Agent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_CRM_BUYER_PORTRAIT => new AiSdrCrmBuyerPortraitAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS => new SdrHutchLeadQualityAgent($clientId, $userId),
            default => throw new \RuntimeException("Invalid scene type"),
        };
    }

    protected static function createShopAgent(int $sceneType, int $clientId, int $userId): BaseAiAgent
    {
        $agent = match ($sceneType) {
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY => new ShopChatReplyAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH => new NewChatCoachAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_CHAT_ARCHIVE => new ChatArchiveAiAgent($clientId, $userId),
            AiAgent::AI_AGENT_SCENE_TYPE_SITE_QUALITY => new SiteQualityAiAgent($clientId, $userId),
            default => throw new \RuntimeException("Invalid scene type"),
        };

        $agent->setBusinessType(AiAgentConstants::BUSINESS_TYPE_SHOP);

        return $agent;
    }
}
