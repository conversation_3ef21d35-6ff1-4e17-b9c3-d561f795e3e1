<?php

namespace common\library\ai_agent\data_assistant\components;

use common\library\ai_agent\data_assistant\components\BaseComponent;
use common\library\ai_agent\data_assistant\DataAsistantConstant;
use common\library\ai_agent\data_assistant\params\BaseParams;
use common\library\ai_agent\data_assistant\params\GenerateSqlParams;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\Helper;
use common\library\ai_agent\trace\AiTraceInfo;
use common\library\ai_agent\trace\TraceConstant;
use common\library\google_ads\sync\Params;

class GenerateSqlComponent extends BaseComponent
{
    protected string $simpleSql = '';
    protected array $complexSqls = [];
    public function getParams() :GenerateSqlParams
    {
        return $this->params;
    }

    public function execute()
    {
        $this->retry(2, function () {
            // 构建Prompt
            $agent = $this->getObserver()->getAgent();
            $question = $agent->buildPrompt();
            $agent->llmService->setResponseFormat(['type' => 'json_object']);
            $response = $agent->callLlm($question);
            $answer = Helper::resolveResponse($response['answer']);

            if ($this->getParams()->isComplexProblemFlag) {
                $withSqlArr = $answer['WITH'];
                $selectSqlArr = explode(';', $answer['SELECT']);
                $titleArr = explode(';', $answer['title']);
                $result = ['withSql' => $withSqlArr, 'selectSqls' => $selectSqlArr, 'titles' => $titleArr];
                $this->complexSqls = $result;
            }else {
               $this->simpleSql = $answer;
            }

        });
    }

    public function response()
    {
        $this->getObserver()->getAgent()->simpleSql = $this->simpleSql;
        $this->getObserver()->getAgent()->complexSqlArr = $this->complexSqls;
        $this->setNextNode(DataAsistantConstant::GENERATE_DATA_COMPONENT);
    }

    public function retry($retries, callable $function)
    {
        // 重试次数
        $attempts = 0;

        // 重试逻辑
        while ($attempts++ < $retries)
        {
            try {
                $startTime = microtime(true);
                $res = $function();
                return $res;
            } catch (\Throwable $exception) {

                \LogUtil::error('GenerateDataSqlRetry', [
                    'question' => $this->getParams()->question,
                    'errMsg' => $exception->getMessage(),
                    'errCode' => $exception->getCode()
                ]);

                // 无相关数据记录，则不重试
                if (in_array($exception->getCode(),[AiAgentException::ERR_NOT_SUPPORT_QUESTION,AiAgentException::ERR_GENERATION_DATA_EMPTY]) || $attempts == $retries)
                {
                    if ($exception instanceof AiAgentException) {
                        throw new AiAgentException($exception->getMessage(), $exception->getCode());
                    } else {
                        throw $exception;
                    }
                }
            } finally {
                $endTime = microtime(true);
                $this->addTraceLogInfo(new AiTraceInfo(TraceConstant::GENERATE_DATA_GPT_REQUEST."_{$attempts}", ['logKeyName' => TraceConstant::TRACE_NAME_MAP[TraceConstant::GENERATE_DATA_GPT_REQUEST], 'costTime' => $endTime - $startTime, 'startTime' => $startTime, 'endTime' => $endTime]));
            }
        }
    }
}