<?php


namespace common\library\track\listener\cms;


use common\library\google_ads\GoogleConstants;
use common\library\google_ads\lead\SiteTrackEventApply;
use common\library\google_ads\track\GclidService;
use common\library\google_ads\track\Params;
use common\library\report\error\ErrorReport;
use common\library\track\event\cms\CmsEvent;

class TrackCms
{

    public function handle(CmsEvent $event)
    {
        $this->syncGclidInfo($event);
        $result = $this->apply($event);

        $result['cookie'] = [
            'identity_id'     => $event->getIdentityId(),
            'device_id'       => $event->getDeviceId(),
            'session_id'      => $event->getSessionId(),
            'site_session_id' => $event->getSiteSessionId(),
        ];

        return $result;
    }

    protected function apply(CmsEvent $event)
    {
        if (!$event->needApply()) {
            return false;
        }

        $apply = new SiteTrackEventApply($event);

        try {
            $apply->apply();
        } catch (\Exception $exception) {
            \LogUtil::error("lead apply error site_session_id: {$event->getSiteSessionId()} event_id: {$event->getEventId()}  msg:" . $exception->getMessage() . $exception->getTraceAsString());
            ErrorReport::phpError(new \CExceptionEvent(null, $exception), $exception->getTrace(), null);
            if (\Yii::app()->params['debugMode']) {
                throw $exception;
            }
        }

        $result = $apply->getResult();
        \LogUtil::info("lead apply  site_session_id: {$event->getSiteSessionId()} event_id: {$event->getEventId()} type:{$event->getParams()->getEventType()} res:" . json_encode($result));

        return $result;
    }

    protected function syncGclidInfo(CmsEvent $event)
    {
        $params = $event->getParams();
        if (empty($params->getGclid())) {
            return;
        }

        $attributionModel = '';
        if (in_array($params->getEventType(), [Params::EVENT_TYPE_EMAIL, Params::EVENT_TYPE_FORM])) {
            $attributionModel = empty($params->getExtractData()['email'] ?? '') ? '' : GoogleConstants::ATTRIBUTION_MODE_FORM;
        }

        //仅收集gclid信息, 定时脚本会完善信息
        $gclidService = new GclidService($params->getClientId());
        $gclidService->setGclid($params->getGclid());
        $gclidService->collectGclid($event->getSiteSessionId(), $params->getSiteId(), $attributionModel);
        $gclidInfo = $gclidService->getInfo();
        $params->setGclidInfo($gclidInfo);
    }

}