<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2017-12-20
 * Time: 4:23 PM
 */

namespace common\library\disk;

class DiskSetting
{

    public $ignorePrivilegeForReadOnly = false;
    public $attachUser = false;
    public $isApp = false;
    public $keyword = '';
    public $fileType = 0;

    public $resizeImage = false;

    /**
     * @param $fileType
     *
     * @return DiskSetting
     */
    public function setFileType($fileType)
    {
        $this->fileType = $fileType;
        return $this;
    }

    /**
     * @param bool $attachUser
     *
     * @return DiskSetting
     */
    public function setAttachUser(bool $attachUser)
    {
        $this->attachUser = $attachUser;
        return $this;
    }

    /**
     * @param bool $isApp
     *
     * @return DiskSetting
     */
    public function setIsApp(bool $isApp)
    {
        $this->isApp = $isApp;
        return $this;
    }

    /**
     * @param string $keyword
     *
     * @return DiskSetting
     */
    public function setKeyword(string $keyword)
    {
        $this->keyword = $keyword;
        return $this;
    }

    /**
     * @param bool $ignorePrivilegeForReadOnly
     */
    public function setIgnorePrivilegeForReadOnly(bool $ignorePrivilegeForReadOnly)
    {
        $this->ignorePrivilegeForReadOnly = $ignorePrivilegeForReadOnly;
        return $this;
    }

    public function setResizeImage(bool $resizeImage)
    {
        $this->resizeImage = $resizeImage;
        return $this;
    }

}