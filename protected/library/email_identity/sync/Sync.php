<?php

namespace common\library\email_identity\sync;

use common\library\email\Util;
use common\library\util\PgsqlUtil;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2018/6/15
 * Time: 上午11:10
 */
abstract class Sync
{
    protected $clientId;
    protected $pgDb;

    protected $emails = [];
    protected $userIds = [];
    protected $syncData = [];
    protected $conflictUserSql;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
        $this->pgDb = \PgActiveRecord::getDbByClientId($clientId);
        $this->init();
        return $this;
    }

    protected function init(){}

    abstract public function sync();

    protected function filterEmails(array $emails){
        $emailList = [];
        foreach ($emails as $email) {
            if(!\EmailUtil::isEmail($email))
            {
                continue;
            }

            $email = Util::getPureEmail($email);
            $emailList[] = trim(strtolower($email));
        }
        return $emailList;
    }
}