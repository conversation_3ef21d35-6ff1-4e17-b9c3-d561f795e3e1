<?php

namespace common\library\search\field;

use common\library\custom_field\company_field\duplicate\Helper;
use common\library\search\builder\BooleanBuilder;
use common\library\search\builder\MatchBuilder;
use common\library\search\builder\TermBuilder;
use common\library\search\SearchConstant;
use common\library\workflow\WorkflowConstant;

class Url<PERSON>ield extends AbstractField
{

    protected function getFilters()
    {
        return [
            'domain_stop_filter' => [
                'type' => 'stop',
                'stopwords' => ['http', 'https', 'http://', 'https://', 'www', 'www.', 'com', 'cn', 'net', 'org', 'com.cn'],
            ],
        ];
    }

    protected function getAnalyzer()
    {
        return [
            'url_path_analyzer' => [
                'tokenizer' => 'url_tokenizer',
                'char_filter' => ['url_host_char_filter'],
                'filter' => [
                    'lowercase',
                    'domain_stop_filter',
                ],
            ],
            'url_text_analyzer' => [
                'type' => 'custom',
                'tokenizer' => 'standard',
                'char_filter' => ['url_full_char_filter', 'url_to_mapping'],
                'filter' => [
                    'lowercase',
                    'domain_stop_filter',
                ],
            ],
        ];
    }

    protected function getCharFilters()
    {
        return [
            "url_host_char_filter" => [
                "type" => "pattern_replace",
                "pattern" => "(https?://)?(www.)?([^/]+)(.*)?",
                "replacement" => "$3",
            ],
            "url_full_char_filter" => [
                "type" => "pattern_replace",
                "pattern" => "(https?://)?(www.)?([^/]+)([^\\#^\\?]+)?(.*)?",
                "replacement" => "$3$4",
            ],
            'url_to_mapping' => [
                'type' => 'mapping',
                "mappings" => [
                    "_=>|",
                    ".=>|",
                ]
            ],
        ];
    }

    protected function getProperty()
    {
        return [
            'type' => SearchConstant::PROPERTY_TYPE_TEXT,
            'analyzer' => 'url_text_analyzer',
            'fields' => [
                'path' => [
                    'type' => SearchConstant::PROPERTY_TYPE_TEXT,
                    'analyzer' => 'url_path_analyzer',
                ],
                'keyword' => [
                    'type' => SearchConstant::PROPERTY_TYPE_KEYWORD,
                    'normalizer' => 'url_keyword_normalizer',
                ],
            ],
        ];
    }

    protected function getNormalizer()
    {
        return [
            'url_keyword_normalizer' => [
                'type' => 'custom',
                'char_filter' => ['url_full_char_filter'],
                'filter' => [
                    'lowercase',
                ]
            ],
        ];
    }

    protected function getTokenizer()
    {
        return [
            'url_tokenizer' => [
                'type' => 'path_hierarchy',
                "delimiter" => ".",
                "replacement" => ".",
                "reverse" => true,
                "skip" => 0
            ],
        ];
    }

    public function getBuilder($value, $operator, $valueType = null)
    {
    
        $builder = null;
        
        switch ($operator) {
            case WorkflowConstant::FILTER_OPERATOR_MATCH:
            case WorkflowConstant::FILTER_OPERATOR_PREFIX:
                $builder = new MatchBuilder($this->fieldId, $value);
//                $builder = new BooleanBuilder();
//                $builder->minimumShouldMatch(1);
//                $builder->should(new TermBuilder($this->fieldId . '.path', $value));
//                $builder->should(new TermBuilder($this->fieldId . '.keyword', $value));
                break;
            case WorkflowConstant::FILTER_OPERATOR_EQUAL:
                $value = Helper::fillUrl($value);
                //不规范url不处理
                if (filter_var($value, FILTER_VALIDATE_URL) === false) {

                    \LogUtil::error('urlField_return_null', ['valueType' => $valueType, 'operator' => $operator, 'value' => $value,]);
        
                    return null;
                }
                $value = parse_url($value)['host']??$value;
                $value = str_replace(['http://', 'https://', 'http://www.', 'www.', 'http:', 'http.', 'http'], '', $value);
                $value = strtolower($value);
                $builder = new TermBuilder($this->fieldId . '.path', $value);
                break;
            case WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL:
            case WorkflowConstant::FILTER_OPERATOR_IN:
            case WorkflowConstant::FILTER_OPERATOR_NOT_IN:
            case WorkflowConstant::FILTER_OPERATOR_NOT_NULL:
            case WorkflowConstant::FILTER_OPERATOR_IS_NULL:
                $builder = $this->byField(KeywordField::class, $this->fieldId . '.keyword', $value, $operator);
                break;
        }
    
        if (empty($builder)) {
        
            \LogUtil::error('urlField_return_null', ['valueType' => $valueType, 'operator' => $operator, 'value' => $value,]);
        }
    
        return $builder;
    }
}