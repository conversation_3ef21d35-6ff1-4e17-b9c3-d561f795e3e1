<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-03-13
 * Time: 2:26 PM
 */

namespace common\library\search;

class ESCriteria
{

    const BOOL_MUST = 'must';
    const BOOL_MUST_NOT = 'must_not';
    const BOOL_SHOULD = 'should';

    public $query;
    public $order = '';
    public $limit = 20;
    public $offset = 0;
    protected $sort = [];
    protected $filters = [];
    protected $matches = [];
    public $routing;

    public function __construct($clientId = 0)
    {
        if ($clientId) {
            $this->setRouting($clientId);
            $this->addFilter('client_id', $clientId);
        }
    }

    public function setQuery($query)
    {
        $this->query = $query;
    }

    /**
     * @param array $filters
     */
    public function setFilters($filters)
    {
        $this->filters = $filters;
    }


    /**
     * @return int
     */
    public function getRouting()
    {
        return $this->routing;
    }

    /**
     * @param int $routing
     */
    public function setRouting(int $routing)
    {
        $this->routing = $routing;
    }

    /**
     * @param        $conditions
     * @param string $matchType
     */
    public function addMatch($conditions, $matchType = 'match', $boolMethod = self::BOOL_MUST)
    {
        $this->matches[$boolMethod][] = [
            $matchType => $conditions
        ];
    }

    public function addShouldMatch($conditions, $matchType = 'match')
    {
        $this->addMatch($conditions, $matchType, self::BOOL_SHOULD);
    }

    public function addNotMatch($conditions, $matchType = 'match')
    {
        $this->addMatch($conditions, $matchType, self::BOOL_MUST_NOT);
    }

    public function addMultiMatch($conditions)
    {
        $this->addMatch($conditions, 'multi_match');
    }

    public function addTerm($conditions)
    {
        $this->addMatch($conditions, 'term');
    }

    public function addTerms($conditions)
    {
        $this->addMatch($conditions, 'terms');
    }

    /**
     * @return array
     */
    public function buildSort()
    {
        if (!$this->order) {
            return [];
        }
        $sorts = explode(',', $this->order);

        array_walk($sorts, function (&$sort) {
            list($field, $order, $mode) = array_pad(explode(' ', trim($sort)), 3, null);
            $sort = $order ? ($mode ? [$field => ['order' => $order, 'mode' => $mode]] : [$field => $order]) : $field;
        });

        $this->sort = $sorts;
        return $this->sort;
    }

    public function buildFilter()
    {
        $filtersCount = count($this->filters);
        if($filtersCount == 1) {
            return reset($this->filters);
        } elseif ($filtersCount > 1) {
            return [
                'bool' => [
                    'must' => $this->filters
                ]
            ];
        }

        return false;
    }

    public function addFilter($field, $value)
    {
        $this->filters[] = is_array($value) ? [
            'terms' => $value
        ] : [
            'term' => [$field => $value]
        ];
    }

    public function addRangeFilter($field, $value)
    {
        $this->filters[] = [
            'range' => [
                $field => $value
            ]
        ];
    }

    protected function toBoolQuery($conditions)
    {
        $count = count($conditions);
        if ($count == 1) {
            return reset($conditions);
        } elseif ($count > 1) {
            return [
                'bool' => [
                    'must' => $conditions
                ]
            ];
        }

        return false;
    }

    /**
     * @return mixed
     */
    public function buildQuery()
    {
        if ($this->query) {
            return $this->query;
        }

        $hasFilter = count($this->filters);
        $hasMatch = count($this->matches);

        if ($hasFilter && !$hasMatch) {
            return [
                'constant_score' => [
                    'filter' => $this->toBoolQuery($this->filters)
                ]
            ];
        }

        $query = [];
        if ($hasMatch) {
            $query['bool'] = $this->matches;
            if (!empty($query['bool']['should'])) {
                $query['bool']['minimum_should_match'] = 1;
            }
        }

        if ($hasFilter) {
            $query['bool']['filter'] = $this->filters;
        }

        return $query;
    }

}