<?php

namespace common\library\swoole\filter;

class MailConversationForMailTagAssocFilter extends BinlogFilter
{
    protected $listenField = ["client_id","user_id","mail_id","tag_id"];
    public function filter()
    {

        $allFilterData = [];
        foreach ($this->data as $datum) {

            $changeData = $datum['data']??[];
            $event = $datum['event']??'';
            $id = $datum['id']??0;

            if (empty($changeData) || empty($event)) {
                continue;
            }

            $filterData = [];
            foreach ($changeData as $changeDatum) {

                $new = $changeDatum['new']??[];
                $old = $changeDatum['old']??[];


                //对比listen field的字段在new old，有变更才需要处理
                $isChange = false;
                foreach ($this->listenField as $field) {
                    if (($new[$field]??'') != ($old[$field]??'')) {
                        $isChange = true;
                        break;
                    }
                }
                if (!$isChange) {
                    continue;
                }

                if (empty($new) && empty($old)) {
                    continue;
                }

                $filterData[] = [
                    'new' => $new,
                    'old' => $old
                ];
            }

            if (!empty($filterData)) {
                $allFilterData[] = [
                    'id' => $id,
                    'event' => $event,
                    'data' => $filterData,
                    'table' => $this->table
                ];
            }
        }

        return $allFilterData;
    }
}