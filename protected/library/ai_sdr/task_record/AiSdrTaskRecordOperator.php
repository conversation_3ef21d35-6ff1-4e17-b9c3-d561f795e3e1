<?php

namespace common\library\ai_sdr\task_record;

use xiaoman\orm\common\Operator;


/**
 * class AiSdrTaskRecordOperator
 * @package common\library\ai_sdr\task_record
 */
class AiSdrTaskRecordOperator extends Operator
{
    const TASK_LIST = [];

    public function create() {
        $data = $this->get(['task_id', 'detail_id', 'lead_id', 'type', 'data', 'estimate_time', 'executed_time', 'create_time', 'update_time', 'refer_type', 'refer_id']);
        return $this->batchInsert($data);
    }

    public function update(array $fields) {
        $fields['update_time'] = xm_function_now();
        return $this->execute($fields);
    }
}