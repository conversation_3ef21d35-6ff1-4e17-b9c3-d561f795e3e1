<?php
/**
 * Created by PhpStorm.
 * Author: <PERSON>(h<PERSON><PERSON>)
 * Date: 2025/4/3
 * Time: 16:40
 */
namespace common\library\ai_sdr;

use common\library\api\InnerApi;
use common\library\discovery\api\BaseInnerApi;

class AiBackgroundCheckService extends BaseInnerApi
{
    protected function getParamsConfig(): array
    {
        return [
            'client_id|require|integer|default:',
            'user_id|require|integer|default:',
        ];
    }

    protected $serviceName = 'ai_background_check';

    public function __construct(int $clientId, int $userId)
    {
        parent::__construct($clientId, $userId);
        $this->setParams([
            'client_id' => $clientId,
            'user_id' => $userId,
        ]);
    }

    public function getStandardResult(string $content, int $contentType, float $threshold = Constant::MIN_THRESHOLD, array $embeddings = [])
    {
        $api = $this->innerApiFactory($this->serviceName);
        $contentType = match ($contentType) {
            1 => 'industry',
            2 => 'product',
            default => throw new \InvalidArgumentException('Invalid content type'),
        };
        $params = [
            'content' => $content,
            'std_type' => $contentType,
            'threshold' => $threshold,
        ];
        if (!empty($embeddings)) {
           $params['vector'] = $embeddings;
        }
        $data = $this->innerCall($api, 'standardizeContent', $params);

        if (!$data) {
            return [];
        }

        return $data;
    }

    protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
    {
        $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
        try {
            return parent::innerCall($api, $interface, $params, $cacheKey, $processResult);
        } catch (\Exception $exception) {
            \LogUtil::info("ai-backgroundCheck API Error: {$exception->getMessage()}", [
                'client_id' => $this->clientId,
                'user_id' => $this->userId,
                'params' => $params,
                'interface' => $interface,
            ]);
            throw new \Exception(\Yii::t("common", "Failed to get data, please try again later"), $exception->getCode());
        }
    }
}