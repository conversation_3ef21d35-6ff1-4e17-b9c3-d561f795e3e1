<?php

namespace common\library\ai_sdr;
use AiAgent;
use AlibabaCloud\Dybaseapi\MNS\Constants;
use common\components\BaseObject;
use common\library\ai_agent\AgentWorkflowService;
use common\library\ai_agent\SdrLeadQualityAnalysisAgent;
use common\library\ai_agent\SdrSellerIndustryAnalyze;
use common\library\ai_agent\vector\EmbeddingService;
use common\library\ai_sdr\buyer_portrait\BuyerPortraitService;
use common\library\ai_sdr\dig_record\BatchAiSdrDigRecord;
use common\library\ai_sdr\industry\IndustryList;
use common\library\ai_sdr\product\ProductList;
use common\library\ai_sdr\profile\ClientProfile;
use common\library\ai_sdr\profile_task\SellerProfileTask;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task\BatchAiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use common\library\ai_sdr\usage_record\AiProductUsageRecord;
use common\library\ai_sdr\usage_record\AiProductUsageRecordFilter;
use common\library\ai_sdr\usage_record\BatchAiProductUsageRecord;
use common\library\compare\operator\In;
use common\library\lead_v3\LeadList;
use common\library\prompt\AiServiceProcessRecordList;
use common\library\queue_v2\job\AiSdrDigTaskJob;
use common\library\queue_v2\job\AiSdrProductUsageJob;
use common\library\queue_v2\QueueService;
use common\library\recommend_plaza\RecommendApi;
use common\library\util\Arr;
use xiaoman\orm\database\data\EGT;

class RecommendCompanyService {
    protected $clientId;
    /**
     * @var int|mixed
     */
    protected mixed $userId;

    public function __construct($clientId = 0, $userId = 0)
    {
        if ($clientId) {
            $this->clientId = $clientId;
            $this->userId = $userId;
        } else {
            $this->clientId = \User::getLoginUser()->getClientId();
            $this->userId = \User::getLoginUser()->getUserId();
        }
    }

    /**
     * 不存在系统级别 task
    */
    public function processProductGraph($taskId) :bool {
        $productUsageFilter = new AiProductUsageRecordFilter($this->clientId);
        $productUsageFilter->ai_sdr_task_id = $taskId;
        $productUsageFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $productUsageFilter->select(['ai_process_record_id', 'source_description', 'target_type', 'target_industry_id', 'target_product_id', 'purpose','reason','status','upstream_product', 'relative_flag']);
        if ($productUsageFilter->count()) {
            \LogUtil::info("ai sdr task {$taskId} product usage record exist, return");
            return true;
        }

        $clientProfile = SellerProfileTask::getExistedTaskObjectBySdrTaskId($this->clientId, $taskId);
        if ($clientProfile->isNew()) {
            throw new \RuntimeException("client profile task invalid");
        }

        $mainProducts = $clientProfile->getMainProducts();
        $industry = $clientProfile->industry;
        if (empty($mainProducts) || empty($industry)) {
            throw new \RuntimeException('Client profile is not complete');
        }

        $productCategory = $clientProfile->products_category;
        $productCategory = array_map(function ($product) {
            $list = explode('→', $product);
            if (count($list)) {
                return trim($list[count($list)-1]);
            } else {
                return '';
            }
        }, $productCategory);
        $formattedClientProfile = [
            'industry' => $clientProfile->industry,
            'product_category' => $productCategory,
            'company_type' => $clientProfile->types,
            'main_products' => $clientProfile->primary_products, // primary_products数量更多，给 ai 提供更多背景
            'profile' => $clientProfile->profile,
        ];


        $retryCount = 0;
        $maxRetries = 3; // 设置最大重试次数
        $agentWorkflowService = new AgentWorkflowService($this->clientId, $this->userId);
        $recommendService = new RecommendCompanyService($this->clientId, $taskId);
        \LogUtil::info("ai sdr {$taskId} graph not exist, start to generate");
        while ($retryCount <= $maxRetries) {
            try {
                $productUsage = $agentWorkflowService->run('generate_product_usage', ['client_profile' => json_encode($formattedClientProfile, JSON_UNESCAPED_UNICODE)]);
                // 数据标准化后流转到推荐状态
                $recommendService->saveProductUsage('', $productUsage, $taskId);
                break; // 成功处理后跳出重试循环
            } catch (\Exception $exception) {
                $retryCount++;
                \LogUtil::info("client_id {$this->clientId} process product usage failed " . print_r($taskId, true) . ", error: {$exception->getMessage()}");
                continue;
            }
        }

        $downstreamIndustry = $clientProfile->getTopBuyerIndustry();
        !empty($downstreamIndustry) && $recommendService->addProductUsageByIndustry($taskId, $downstreamIndustry);

        $taskFilter = new AiSdrTaskFilter($this->clientId);
        $taskFilter->task_id = $taskId;
        $batchFilter = $taskFilter->find();
        $batchFilter->getOperator()->update([
            'graph_status' => Constant::AI_SDR_GRAPH_STATUS_FINISHED,
        ]);

        // 删除标准化后重复的数据，避免重复推荐
        $productUsageFilter = new AiProductUsageRecordFilter($this->clientId);
        $productUsageFilter->ai_sdr_task_id = $taskId;
        $productUsageFilter->status = Constant::USAGE_STATUS_RECOMMENDING;
        $productUsageFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $productUsageFilter->select(['record_id', 'target_type', 'target_industry_id', 'target_product_id']);
        $batchProductUsage = $productUsageFilter->rawData();

        $uniqueKeys = [];
        $duplicateRecords = [];
        foreach ($batchProductUsage as $item) {
            $duplicateKey = $item['target_type']."-".$item['target_industry_id']."-".$item['target_product_id'];
            if (in_array($duplicateKey, $uniqueKeys)) {
                $duplicateRecords[] = ['record_id' => $item['record_id']];
                continue;
            }
            $uniqueKeys[] = $duplicateKey;
        }

        if (!empty($duplicateRecords)) {
            $batchProductUsage = new BatchAiProductUsageRecord($this->clientId);
            $batchProductUsage->initFromData($duplicateRecords);
            $batchProductUsage->getOperator()->update(['enable_flag' => BaseObject::ENABLE_FLAG_FALSE]);
        }

        return true;
    }

    public function processRecommend($sdrTaskId, $recommendOnly = true) {
        $usageFilter = new AiProductUsageRecordFilter($this->clientId);
        $usageFilter->ai_sdr_task_id = $sdrTaskId;
        $usageFilter->status = Constant::USAGE_STATUS_RECOMMENDING; // 没有标准化的数据不要进行推荐
        $recommendOnly && $usageFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
//        $usageFilter->relative_flag = new EGT(Constant::LEAD_QUALITY_MEDIUM);
        $usageFilter->select(['record_id', 'target_type', 'target_industry_id', 'target_product_id', 'upstream_product', 'source_description', 'purpose', 'reason', 'last_company_id']);
        $usageRecords = $usageFilter->rawData();

        if (empty($usageRecords)) {
            return [[], []];
        }

        $matchProducts = [];
        $matchIndustries = [];
        foreach ($usageRecords as $record) {
            $upstreamProduct = $record['upstream_product'];
            $record['seller_keyword'] = $upstreamProduct;
            if ($record['target_type'] == Constant::MATCH_TYPE_PRODUCT_USAGE) {
                if (isset($matchProducts[$upstreamProduct])) {
                    $matchProducts[$upstreamProduct][] = $record;
                } else {
                    $matchProducts[$upstreamProduct] = [$record];
                }
            } else {
                if (isset($matchIndustries[$upstreamProduct])) {
                    $matchIndustries[$upstreamProduct][] = $record;
                } else {
                    $matchIndustries[$upstreamProduct] = [$record];
                }
            }
        }

        if (empty($matchProducts) && empty($matchIndustries)) {
            return [[], []];
        }

        return [
            $matchProducts, $matchIndustries
        ];
    }

    /**
     * 只挖掘数据，不打标
    */
    public function getRecommendCompany($matchProducts, $matchIndustries) :array{
        if (empty($matchProducts) && empty($matchIndustries)) {
            \LogUtil::info("client_id {$this->clientId} cannot dig more company");
            throw new \RuntimeException(\Yii::t("ai", "no product usage records"));
        }
        $clientProfile = new ClientProfile($this->clientId);
        if ($clientProfile->isNew()) {
            throw new \RuntimeException(\Yii::t("ai", "Client profile not found. Please ensure the client profile is created before processing recommendations."));
        }
        $clientProfile->getFormatter()->setShowShortProductName(true);
        $clientProfile->getFormatter()->setNeedStrip(false);

        $matchRecord = [];
        array_walk($matchProducts, function ($products) use (&$matchRecord) {
            array_walk($products, function (&$product) {
                $product['match_type'] = Constant::MATCH_TYPE_PRODUCT_USAGE;
            });
            $matchRecord = array_merge($matchRecord, $products);
        });
        array_walk($matchIndustries, function ($industries) use (&$matchRecord) {
            array_walk($industries, function (&$industry) {
                $industry['match_type'] = Constant::MATCH_TYPE_INDUSTRY;
            });
            $matchRecord = array_merge($matchRecord, $industries);
        });


        $pageSize = 20; // 每次取少量数据，但是每个 record 取 100 条
        $recommendApi = new RecommendApi($this->clientId, $this->userId);
        $pg = \PgActiveRecord::getDbByClientId($this->clientId);
        $batchDigRecord = new BatchAiSdrDigRecord($this->clientId);
        $digCount = 0;
        foreach ($matchRecord as $record) {
            $matchType = $record['match_type'];
            $lastCompanyId = $record['last_company_id']??null;
            for ($i = 0; $i < 5; $i++) { // 最多挖掘 5 次
                try {
                    switch ($matchType) {
                        case Constant::MATCH_TYPE_INDUSTRY:
                            if (empty($record['target_industry_id'])) continue 2;
                            $result = $recommendApi->getMatchCompanyByProfile($matchType, [$record['target_industry_id']], beforePortraitIds: $lastCompanyId, pageSize: $pageSize);
                            break;
                        case Constant::MATCH_TYPE_PRODUCT_USAGE:
                        default:
                            if (empty($record['target_industry_id']) || empty($record['target_product_id'])) continue 2;
                            $result = $recommendApi->getMatchCompanyByProfile($matchType, [], ['industry_id' => $record['target_industry_id'], 'product_ids' => [$record['target_product_id']]],
                                beforePortraitIds: $lastCompanyId, pageSize: $pageSize);
                    }
                    $count = $result['count']??0;
                    $isDigFinished = isset($isDigFinished) ? $isDigFinished && !$count: false;
                    $result = $result['list']??[];
                    $usageRecordId = $record['record_id'];
                    $result = array_map(function ($item) use ($usageRecordId) {
                        $item['record_id'] = $usageRecordId;
                        return $item;
                    }, $result);

                    $digCount += count($result);
                    if(empty($result)) {
                        $status = Constant::USAGE_STATUS_FINISH_RECOMMEND; // 没有更多数据了，设置为推荐完成
                        $pg->createCommand("UPDATE tbl_ai_product_usage_record SET status={$status} WHERE record_id=".$record['record_id'])->execute();
                        continue;
                    }

                    $digRecords = [];
                    foreach ($result  as $item) {
                        if (empty($item['domain'])) continue;
                        $digRecords[] = [
                            'client_id' => $this->clientId,
                            'domain' => $item['domain'],
                            'usage_record_id' => $usageRecordId,
                        ];
                    }
                    $batchDigRecord->initFromData($digRecords);
                    $batchDigRecord->getOperator()->create();

                    $lastCompanyId = end($result)['id'];
                    $pg->createCommand("UPDATE tbl_ai_product_usage_record
SET last_company_id = '{$lastCompanyId}' WHERE record_id=".$record['record_id'])->execute();
                    \LogUtil::info("client_id {$this->clientId} recommend company success, record_id {$record['record_id']}");
                } catch (\Exception $exception) {
                    \LogUtil::info("client_id {$this->clientId} recommend company failed, record_id {$record['record_id']}, error: ".$exception->getMessage());
                }
            }
        }

        $isDigFinished = $isDigFinished??false;
        return [$isDigFinished, $digCount];
    }

    public function getProductGraph($taskId) : array {
        $graph = [
            'client_info' => [],
            'client_products' => [],
            'product_category' => [],
            'product_usage' => [],
            'industry_company_type' => [],
            'match_products' => [],
            'match_industries' => [],
            'company_list' => [],
            'relation' => [],
        ];

        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","jRSr"));
        }
        
        $graph['graph_status'] = $task->graph_status;
        if ($task->graph_status != Constant::AI_SDR_GRAPH_STATUS_FINISHED) {
            return $graph;
        }

        $clientProfile = new ClientProfile($this->clientId);
        $mainProducts = $clientProfile->getMainProducts();
        $clientName = $clientProfile->company_name;
        $graph['client_info'] = [
            'company_name' => $clientName,
            'industry' => $clientProfile->industry,
            'main_products' => $mainProducts,
            'element_id' => md5($clientName),
        ];
        $elementMap = [
            $clientName => md5($clientName),
        ];

        $usageFilter = new AiProductUsageRecordFilter($this->clientId);
        $usageFilter->ai_sdr_task_id = $taskId;
        $usageFilter->status  = Constant::USAGE_STATUS_RECOMMENDING;
        $usageFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $usageFilter->relative_flag = new EGT(Constant::LEAD_QUALITY_MEDIUM);
        $usageFilter->select([
            'upstream_products', 'source_description', 'purpose', 'reason', 'company_types','source_industry',
        ]);
        $usageRecords = $usageFilter->rawData();

        $usagePurposes = array_unique(array_column($usageRecords, 'purpose'));
        foreach ($usagePurposes as $usagePurpose) {
            $elementId = md5($usagePurpose);
            $graph['product_usage'][] = [
                'usage' => $usagePurpose,
                'element_id' => $elementId
            ];
            $elementMap[$usagePurpose] = $elementId;
            $graph['relation'][] = [
                'source' => $elementMap[$clientName],
                'target' => $elementId,
                'relation_type' => '',
            ];
        }

        foreach ($usageRecords as $usageRecord) {
            $purpose = $usageRecord['purpose'];
            $industry = $usageRecord['source_industry'];
            $elementId = md5($industry);

            $upstreamProducts = $usageRecord['upstream_products']??[];
            if (!isset($elementMap[$industry])) {
                $graph['match_industries'][] = [
                    'industry_name' => $industry,
                    'description' => "可能采买的产品：".implode(",", $upstreamProducts),
                    'element_id' => $elementId
                ];
                $elementMap[$industry] = $elementId;
            }
            $graph['relation'][] = [
                'source' => $elementMap[$purpose],
                'target' => $elementId,
                'relation_type' => '下游行业',
            ];

            $companyTypes = $usageRecord['company_types']??[];
            foreach ($companyTypes as $companyType) {
                $companyTypeId = md5($companyType);
                $graph['industry_company_type'][] = [
                    'company_type' => $companyType,
                    'element_id' => $companyTypeId,
                ];
                $elementMap[$companyType] = $companyTypeId;
                $graph['relation'][] = [
                    'source' => $elementMap[$industry],
                    'target' => $companyTypeId,
                    'relation_type' => '公司类型代表',
                ];
            }
        }

        return $graph;
    }

    protected function checkStandarization($matchType, $description) {
        if ($matchType == Constant::MATCH_TYPE_INDUSTRY) {
            $obj = new IndustryList();
            $obj->setIndustryIds([$description]);
        } else {
            $obj = new ProductList();
            $obj->setProductId([$description]);
        }
        $obj->setLimit(1);
        $result = $obj->find();
        if (!empty($result)) {
            $objId = $description;
            $sourceDesc = $result[0]['description']??'';
            return [$objId, $sourceDesc];
        } else {
            $recommendApi = new AiBackgroundCheckService($this->clientId, $this->userId);
            $result = $recommendApi->getStandardResult($description, $matchType, Constant::MIN_THRESHOLD);
            $objId = $result['std_id']??0;
            return [$objId, $description];
        }
    }

    public function addProductUsage($taskId,  $clientProduct, $matchType, $downstreamProduct = '', $downstreamIndustry = '', $relativeFlag = Constant::LEAD_QUALITY_HIGH) {
        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","jRSr"));
        }
        $clientProfile = new ClientProfile($this->clientId);
        $mainProducts = $clientProfile->getMainProducts();
        $clientProduct = trim($clientProduct);
        if (empty($clientProduct)) {
            throw new \RuntimeException(\Yii::t('ai', 'Client product cannot be empty'));
        }
        if (!in_array($clientProduct, $mainProducts)) {
            $clientProfile->addMainProduct($clientProduct);
        }

        list($standardIndustry, $downstreamIndustry) = $this->checkStandarization($matchType, $downstreamIndustry);

        $desc = $matchType == Constant::MATCH_TYPE_INDUSTRY?"行业匹配":"产品匹配";
        $downstreamProduct = trim($downstreamProduct);
        $downstreamIndustry = trim($downstreamIndustry);
        $productUsage = new AiProductUsageRecord($this->clientId);
        $productUsage->upstream_product = $clientProduct;
        $productUsage->purpose = $desc;
        $productUsage->reason = $desc;
        $productUsage->ai_sdr_task_id = $taskId;
        $productUsage->ai_process_record_id = 0;
        $productUsage->target_type = $matchType;
        $productUsage->target_industry_id = $standardIndustry;
        $productUsage->source_description = $downstreamIndustry;
        $status = $standardIndustry ? Constant::USAGE_STATUS_RECOMMENDING : Constant::USAGE_STATUS_STANDARDIZED_FAILED;
        if ($matchType == Constant::MATCH_TYPE_PRODUCT_USAGE) {
            list($standardProduct, $downstreamProduct) = $this->checkStandarization($matchType, $downstreamProduct);
            $productUsage->target_industry_id = $standardProduct;
            $productUsage->source_description = $downstreamProduct;
            $status = $standardProduct ? Constant::USAGE_STATUS_RECOMMENDING : Constant::USAGE_STATUS_STANDARDIZED_FAILED;
        }
        $productUsage->status = $status;
        $productUsage->relative_flag = $relativeFlag;
        $productUsage->create();
    }

    public function addProductUsageByIndustry($taskId, array $downstreamIndustry) {
        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","jRSr"));
        }

        $clientProfile = new ClientProfile($this->clientId);
        $productCategory = $clientProfile->products_category;
        $productCategory = array_map(function ($product) {
            $list = explode('→', $product);
            if (count($list)) {
                return trim($list[count($list)-1]);
            } else {
                return '';
            }
        }, $productCategory);
        $formattedClientProfile = [
            'industry' => $clientProfile->industry,
            'product_category' => $productCategory,
            'company_type' => $clientProfile->type,
            'main_products' => $clientProfile->getMainProducts(),
            'profile' => $clientProfile->profile,
        ];

        $agent = new SdrSellerIndustryAnalyze($this->clientId, $this->userId);
        $retryCount = 0;
        $industryStr = json_encode($downstreamIndustry, JSON_UNESCAPED_UNICODE);
        $api = new AiBackgroundCheckService($this->clientId, $this->userId);
        $batchProductUsage = new BatchAiProductUsageRecord($this->clientId);
        $batchData = [];
        while ($retryCount <= 3) {
            try {
                $industryMap = [];
                $result = $agent->process([
                    'client_profile' => json_encode($formattedClientProfile, JSON_UNESCAPED_UNICODE),
                    'buyer_profile' => $industryStr,
                ]);

                $industryUsage = $result['answer']??[];
                $aiServiceRecordId = $result['record_id']??0;
                if (empty($industryUsage) || empty($aiServiceRecordId)) {
                    throw new \RuntimeException('Industry product usage analysis failed');
                }
                foreach ($industryUsage as $usage) {
                    $industry = $usage['potential_buyer_industry']??'';
                    $clientProduct = $usage['seller_product']??'';
                    $purpose = mb_substr($usage['product_usage']??'', 0, 16);
                    $reason = $usage['reason']??'';
                    if (!isset($industryMap[$industry])) {
                        $shortenIndustries = explode("-", $industry);
                        $standardIndustry = $api->getStandardResult(end($shortenIndustries), Constant::MATCH_TYPE_INDUSTRY, Constant::MIN_THRESHOLD);
                        $industryMap[$industry] = $standardIndustry['std_id']??0;
                    }
                    $standardIndustry = $industryMap[$industry]??0;
                    $batchData[] = [
                        'ai_process_record_id' => $aiServiceRecordId,
                        'source_description' => $industry,
                        'target_type' => Constant::MATCH_TYPE_INDUSTRY,
                        'target_industry_id' => $standardIndustry,
                        'target_product_id' => 0,
                        'ai_sdr_task_id' => $taskId,
                        'purpose' => $purpose,
                        'reason' => $reason,
                        'status' => $standardIndustry?Constant::USAGE_STATUS_RECOMMENDING: Constant::USAGE_STATUS_STANDARDIZED_FAILED,
                        'upstream_product' => $clientProduct,
                        'relative_flag' => $usage['relative']??0,
                        'company_types' => [],
                        'upstream_products' => [$clientProduct],
                        'source_industry' => $industry,
                    ];
                }

                break;
            } catch (\Exception $exception) {
                \LogUtil::info("client_id {$this->clientId} process industry product usage relation failed error: {$exception->getMessage()}", [
                    'industry' => $industryStr,
                ]);
                $retryCount++;
            }
        }

        if (!empty($batchData)) {
            $batchProductUsage->initFromData($batchData);
            $batchProductUsage->getOperator()->create();
        }

        \LogUtil::info("client_id {$this->clientId} process industry product usage analysis success");
        return $aiServiceRecordId;
    }

    public function removeProductUsage($recordId) {
        $productUsage = new AiProductUsageRecord($this->clientId, $recordId);
        $productUsage->record_id = $recordId;
        if ($productUsage->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","Product usage record not found. Please check the record ID."));
        }
        $productUsage->enable_flag = BaseObject::ENABLE_FLAG_FALSE;
        $productUsage->update_time = xm_function_now();
        $productUsage->update(['enable_flag', 'update_time']);
    }

    public function saveProductUsage($clientProduct, $response, $taskId) {
        $buyerProfiles = $response['answer']??[];
        $recordId = $response['record_id']??0;
        if (empty($buyerProfiles) || empty($recordId)) {
            throw new \RuntimeException('Product usage analysis failed');
        }
        $api = new AiBackgroundCheckService($this->clientId, $this->userId);
        $industryMap = [];
        $productMap = [];
        $batchProductUsage = new BatchAiProductUsageRecord($this->clientId);
        $batchData = [];
        foreach ($buyerProfiles as $buyerProfile) {
            $industry = $buyerProfile['industry']??'';
            $products = $buyerProfile['potential_products']??[];
            $matchRule = $buyerProfile['match_rule']??'';
            if (!$this->validateBuyerProfile($matchRule, $industry, $products)) {
                continue;
            }
            $purpose = mb_substr($buyerProfile['purpose']??'', 0, 16);
            $description = $buyerProfile['description']??'';
            if (!isset($industryMap[$industry])) {
                $shortenIndustries = explode("-", $industry);
                $standardIndustry = $api->getStandardResult(end($shortenIndustries), Constant::MATCH_TYPE_INDUSTRY, Constant::MIN_THRESHOLD);
                $industryMap[$industry] = $standardIndustry['std_id']??0;
            }
            $standardIndustry = $industryMap[$industry]??0;
            if ($matchRule == Constant::MATCH_TYPE_INDUSTRY) {
                $item = [
                    'ai_process_record_id' => $recordId,
                    'source_description' => $industry,
                    'target_type' => $matchRule,
                    'target_industry_id' => $standardIndustry,
                    'target_product_id' => 0,
                    'ai_sdr_task_id' => $taskId,
                    'purpose' => $purpose,
                    'reason' => $description,
                    'status' => $standardIndustry?Constant::USAGE_STATUS_RECOMMENDING: Constant::USAGE_STATUS_STANDARDIZED_FAILED,
                    'upstream_product' => $clientProduct,
                    'relative_flag' => $buyerProfile['relative']??0,
                    'company_types' => $buyerProfile['company_types']??[],
                    'upstream_products' => $buyerProfile['upstream_products']??[],
                    'source_industry' => $industry,
                ];
                $batchData[] = $item;
            } else {
                foreach ($products as $product) {
                    if (!isset($productMap[$product])) {
                        $shortenProducts = explode("-", $product);
                        $standardProduct = $api->getStandardResult(end($shortenProducts), Constant::MATCH_TYPE_PRODUCT_USAGE, Constant::MIN_THRESHOLD);
                        $productMap[$product] = $standardProduct['std_id'] ?? 0;
                    }
                    $standardProduct = $productMap[$product] ?? 0;
                    $item = [
                        'ai_process_record_id' => $recordId,
                        'source_description' => $product,
                        'target_type' => $matchRule,
                        'target_industry_id' => $standardIndustry,
                        'target_product_id' => $standardProduct,
                        'ai_sdr_task_id' => $taskId,
                        'purpose' => $purpose,
                        'reason' => $description,
                        'status' => $standardProduct ? Constant::USAGE_STATUS_RECOMMENDING : Constant::USAGE_STATUS_STANDARDIZED_FAILED,
                        'upstream_product' => $clientProduct,
                        'relative_flag' => $buyerProfile['relative']??0,
                        'company_types' => $buyerProfile['company_types']??[],
                        'upstream_products' => $buyerProfile['upstream_products']??[],
                        'source_industry' => $industry,
                    ];
                    $batchData[] = $item;
                }
            }
        }

        if (!empty($batchData)) {
            $batchProductUsage->initFromData($batchData);
            $batchProductUsage->getOperator()->create();
        }
        return $recordId;
    }

    protected function validateBuyerProfile($matchRule, $industries = [], $products = []) {
        if (empty($matchRule)) {
            return false;
        }
        if ($matchRule == Constant::MATCH_RULE_INDUSTRY && empty($industries)) {
            return false;
        }
        if ($matchRule == Constant::MATCH_RULE_PRODUCT && empty($products)) {
            return false;
        }
        return true;
    }
}