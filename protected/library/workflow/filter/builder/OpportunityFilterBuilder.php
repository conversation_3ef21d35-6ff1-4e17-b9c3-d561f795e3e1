<?php


namespace common\library\workflow\filter\builder;


use common\library\custom_field\CustomFieldService;
use common\library\opportunity\OpportunityList;
use common\library\setting\item\Api;
use common\library\setting\library\stage\Stage;
use common\library\workflow\WorkflowConstant;

class OpportunityFilterBuilder extends BaseFilterBuilder
{

    public function getDefaultFilters()
    {
        return [
            [
                'field'      => 'enable_flag',
                'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                'value'      => 1,
            ],
        ];
    }

    public function customFieldMethodMap($valueType = '')
    {
    
        return array_merge($this->customCommonFieldFilterBuild(), $this->customFieldPinFlag(\Pin::TYPE_OPPORTUNITY), $this->customFieldUPinUserList(\Pin::TYPE_OPPORTUNITY), $this->customFieldFilterCashCollectionDate(), [
            'cash_collection_status' => function (\ListObject $list, $value, $operator, $fieldId) {
                list($where, $params) = $this->build('status', CustomFieldService::FIELD_TYPE_SELECT, $operator, $value);
                $where = str_replace($this->getAlias($this->referConfig->getReferType()).'status', 'tbl_cash_collection_status.status', $where);
                $sql = $this->referConfig->getReferPrimaryKey() . " in (select refer_id from tbl_cash_collection_status where refer_type = " . $list->getCashCollectionStatusReferType() . " AND client_id = " . $this->referConfig->getClientId() . " AND $where)";
                return [$sql, $params];
            },
            'cash_collection_collect_amount' => function (\ListObject $list, $value, $operator, $fieldId) {
                list($where, $params) = $this->build($this->getAmountField('collect_amount', $this->clientId), CustomFieldService::FIELD_TYPE_NUMBER, $operator, $value);
                $where = str_replace($this->getAlias($this->referConfig->getReferType()).'collect_amount', 'tbl_cash_collection_status.collect_amount', $where);
                $sql = $this->referConfig->getReferPrimaryKey() . " in (select refer_id from tbl_cash_collection_status where refer_type = " . $list->getCashCollectionStatusReferType() . " AND client_id = " . $this->referConfig->getClientId() . " AND $where)";
                return [$sql, $params];
            },
            'cash_collection_not_collect_amount' => function (\ListObject $list, $value, $operator, $fieldId) {
                list($where, $params) = $this->build($this->getAmountField('amount', $this->clientId) . ' - ' . $this->getAmountField('collect_amount',$this->clientId), CustomFieldService::FIELD_TYPE_NUMBER, $operator, $value);
                $where = str_replace($this->getAlias($this->referConfig->getReferType()).'not_collect_amount', 'tbl_cash_collection_status.not_collect_amount', $where);
                $sql = $this->referConfig->getReferPrimaryKey() . " in (select refer_id from tbl_cash_collection_status where refer_type = " . $list->getCashCollectionStatusReferType() . " AND client_id = " . $this->referConfig->getClientId() . " AND $where)";
                return [$sql, $params];
            },
            'success_rate' => function (OpportunityList $list, $value, $operator, $fieldId) {
                $params = [];
                $compare = new CompareFilterBuilder($this->referConfig->getReferType(), 'success_rate', CustomFieldService::FIELD_TYPE_NUMBER, $operator, $value, 0, 'stage_id', '');
                $stageList = Api::stage($this->referConfig->getClientId())->listAll();
                $stageIds = $compare->filter($stageList);
                if (count($stageIds)) {
                    $sql = "stage in (" . implode(',', $stageIds) . ")";
                } else {
                    $sql = "0=1";
                }

                return [$sql, $params];
            },
            'stage_stay_time' => function (OpportunityList $list, $value, $operator, $fieldId) {
                // 商机_当前阶段停留时间， 管理端【stage_stay_time】需要跟商机【stage_edit_time】一致，
                return $this->build('stage_edit_time', CustomFieldService::FIELD_TYPE_NUMBER, $operator, $value, false, null, '', '', '');
            },
            'stage_edit_time' => function (OpportunityList $list, $value, $operator, $fieldId) {
               
                $alias = $this->getAlias($this->referConfig->getReferType());
    
                [$where,
                 $params] = $this->build(" (timezone('Asia/Shanghai', CURRENT_TIMESTAMP)::DATE - {$alias}stage_edit_time::DATE) ", CustomFieldService::FIELD_TYPE_NUMBER, $operator, $value, false, null, '', '', '');
    
                $where .= ' and ' . $alias . 'stage_type IN (' . Stage::STAGE_ON_GOING_STATUS . ',' . Stage::STAGE_WIN_STATUS . ')';
    
                return [$where, $params];
            },
            'handler' => function (OpportunityList $list, $value, $operator, $fieldId) {
                return $this->build('user_id', CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT, $operator, $value, true);
            },
            'handler_user' => function (OpportunityList $list, $value, $operator, $fieldId) {
                return $this->build('handler', CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT, $operator, $value, true);
            },
            'department' => function (OpportunityList $list, $value, $operator, $fieldId) {
                $value = \common\library\department\Helper::getBatchDepartmentChildIds($this->clientId, $value);
                return $this->build($fieldId, CustomFieldService::FIELD_TYPE_SELECT, $operator, $value, true);
            },
            'users' => function (OpportunityList $list, $value, $operator, $fieldId) {
                list($sql, $params) = $this->build('coalesce(array_length(opportunity.handler, 1), 0)', CustomFieldService::FIELD_TYPE_NUMBER, $operator, $value, true);
                $sql = str_replace($this->getAlias($this->referConfig->getReferType()).'coalesce', 'coalesce', $sql);
                return [$sql, $params];
            },
            //商机类型
            'opportunity_type' => function (OpportunityList $list, $value, $operator, $fieldId) {
                return $this->build('type', CustomFieldService::FIELD_TYPE_SELECT, $operator, $value, true);
            },
        ]);
    }

    /**
     * @return OpportunityList
     */
    public function getObjectList()
    {
        $list = new OpportunityList($this->clientId);
        $list->setSkipPermissionCheck(true);

        return $list;
    }

}