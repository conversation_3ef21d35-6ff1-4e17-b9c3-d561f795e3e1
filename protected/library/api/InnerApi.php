<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2018/10/29
 * Time: 14:54
 */

namespace common\library\api;

use common\library\log\TraceHelper;
use <PERSON><PERSON>\Sidekick\Exceptions\AcquireException;
use <PERSON><PERSON>\Sidekick\Sentinel\Sentinel;
use <PERSON><PERSON>\Sidekick\Sentinel\SentinelOptions;

class InnerApi
{
    const HTTP_METHOD_POST_JSON = 'post_json';
    const HTTP_METHOD_POST = 'post';
    const HTTP_METHOD_GET = 'get';

    //灰度client key
    const GREY_CLIENT_REDIS_KEY_PREFIX = 'crm:inner_api:grey:';

    protected $header = [
    ];

    protected $resetHeader = false;

    protected $module;
    protected $interface;
    protected $processUrl;
    protected $config = [];
    protected $host;

    protected $httpMethod;

    protected $rspJsonCodeField = 'code';
    protected $checkRspHttpCode = false;
    protected $checkRspJsonCode = true;
    protected $jsonDecode = true;

    protected $timeout = 2;

    protected $paramLog = true; // error log 里面是否需要参数信息 如果接口参数很大 而且没有什么参考价值 可以关闭

    protected $accessLog = false;
    protected $timeLog = true; // access log 开启 time log才会生效
    protected $resultLog = true;

    protected $curlError;
    protected $curlErrorCode;
    protected $curlHttpCode;
    protected $curlInfo = [];

    protected $defaultHeader;

    protected $interfaceHandler;

    protected $errorInfoList = [
        'CURLINFO_TOTAL_TIME',
        'CURLINFO_NAMELOOKUP_TIME',
        'CURLINFO_CONNECT_TIME',
        'CURLINFO_PRETRANSFER_TIME',
        'CURLINFO_STARTTRANSFER_TIME',
        'CURLINFO_APPCONNECT_TIME'
    ];

    /**
     * @var callable
     */
    protected $timeoutHandler;
    /**
     * @var callable
     */
    protected $serverErrorHandler;
    /**
     * @var callable
     */
    protected $apiNoCodeHandler;
    /**
     * @var callable
     */
    protected $apiErrorHandler;
    /**
     * @var callable
     */
    protected $httpCodeHandler;

    public function __construct($module)
    {
        $this->module = $module;

        if (!isset(\Yii::app()->params['inner_api'][$module]))
            throw new \ProcessException("inner api $module not exist");

        $this->config = \Yii::app()->params['inner_api'][$module];

        if (!isset($this->config['host']) || empty($this->config['host']))
            throw new \ProcessException("inner api $module host not set");

        $apiConfig = $this->config['config'] ?? [];

        $this->host = $this->config['host'];

        if (isset($apiConfig['grey']) && ($user = \User::getLoginUser()))
        {
            $clientId = $user->getClientId();

            if (!empty($apiConfig['grey']['client']) && in_array($clientId, $apiConfig['grey']['client'])) {
                $this->host = $apiConfig['grey']['host'];
            }else if (!empty($apiConfig['grey']['client_last_number']) && in_array($clientId % 10, $apiConfig['grey']['client_last_number'])) {
                $this->host = $apiConfig['grey']['host'];
            }else if (!empty($apiConfig['grey']['client_from_redis'])) {
                $redis = \RedisService::getInstance('redis');
                $key = self::GREY_CLIENT_REDIS_KEY_PREFIX.$module;
                $grey_data = $redis->get($key);
                if(!empty($grey_data)){
                    $grey_info = json_decode($grey_data, true);
                    if(!empty($grey_info['client_tail']) && is_array($grey_info['client_tail'])){
                        if(in_array($clientId % 10, $grey_info['client_tail'])){
                            $this->host = $apiConfig['grey']['host'];
                        }
                    }
                    if(!empty($grey_info['client_id']) && is_array($grey_info['client_id'])){
                        if(in_array($clientId, $grey_info['client_id'])){
                            $this->host = $apiConfig['grey']['host'];
                        }
                    }
                }
            }

        }

        if (isset($apiConfig['timeout']))
            $this->timeout = $apiConfig['timeout'];

        if (isset($apiConfig['access_log']))
            $this->accessLog = $apiConfig['access_log'];

        if (isset($apiConfig['param_log']))
            $this->paramLog = $apiConfig['param_log'];

        if (isset($apiConfig['time_log']))
            $this->timeLog = $apiConfig['time_log'];

        if (isset($apiConfig['http_method']))
            $this->httpMethod = $apiConfig['http_method'];
    }

    public function setAccessLog($flag)
    {
        $this->accessLog = $flag;
    }

    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
    }

    public function setHttpMethod($method)
    {
        $this->httpMethod = $method;
    }

    public function setRespondJsonCodeField($field)
    {
        $this->rspJsonCodeField = $field;
    }

    public function setCheckRspHttpCode($flag)
    {
        $this->checkRspHttpCode = $flag;
    }

    public function setCheckRspJsonCode($flag)
    {
        $this->checkRspJsonCode = $flag;
    }

    public function setJsonDecode($flag)
    {
        $this->jsonDecode = $flag;
    }

    public function addHeader($header)
    {
        if (is_array($header))
            $this->header = array_merge($this->header, $header);
        else
            $this->header[] = $header;
    }

    public function resetHeader()
    {
        $this->resetHeader = true;
        $this->header = [];
    }

    /**
     * @return mixed
     */
    public function getDefaultHeader()
    {
        if( $this->defaultHeader === null)
        {
            $this->defaultHeader = TraceHelper::getInstance()->exportHeaders(TraceHelper::EXPORT_TYPE_HTTP);
        }

        return $this->defaultHeader;
    }

    /**
     * 带上client user信息
     */
    public function getRequestHeader()
    {
        return array_merge($this->getDefaultHeader(), $this->header);
    }

    public function setInterfaceHandler($handler)
    {
        $this->interfaceHandler = $handler;
    }

    final public function getUrl($interface)
    {
        if ($this->interfaceHandler)
            $interfacePath = ($this->interfaceHandler)($this->config['interface'][$interface]);
        else
            $interfacePath = $this->config['interface'][$interface];

        // TODO: 临时处理，上线后删除
        if ($this->module == 'discovery_v2') {
            $config = \Yii::app()->params['inner_api']['leads_service'] ?? [];
            if (in_array($interface, $config['enable_interface'] ?? [])) {
                $interface = $config['interface'][$interface];
                return $config['host'] . $interface;
            }
        }
        return $this->host . $interfacePath;
    }

    public function getModule()
    {
        return $this->module;
    }

    public function getInterface()
    {
        return $this->interface;
    }

    public function getDomain()
    {
        return parse_url($this->host, PHP_URL_HOST);
    }

    public function call($interface, $param)
    {
        $entryKey = 'inner_api:' . $this->getModule() . ':' . $interface;

        $loginUser = \User::getLoginUser();
        try {
            $opts = [
                SentinelOptions::TRAFFIC_TYPE => SentinelOptions::TRAFFIC_TYPE_OUTBOUND,
                SentinelOptions::RESOURCE_TYPE => SentinelOptions::RESOURCE_TYPE_RPC,
            ];
            // 热点限流
            if ($loginUser) {
                $opts[SentinelOptions::HOTSPOT_ARGS][] = $loginUser->getClientId();
                $opts[SentinelOptions::HOTSPOT_ARGS][] = $loginUser->getUserId();
            }

            return Sentinel::guard($entryKey, fn () => $this->innerCall($interface, $param), $opts);
        } catch (AcquireException $exception) {
            \LogUtil::warning("{$this->module} . {$interface} acquire lock failed");
            throw $exception;
        }
    }

    private function innerCall($interface, $param)
    {
        if (!isset($this->config['interface'][$interface]))
            throw new \ProcessException("inner api {$this->module} $interface not set");

        $url = $this->getUrl($interface);


        $this->interface = $interface;
        $this->processUrl = $url;
        $this->curlError = null;
        $this->curlErrorCode = null;
        $this->curlHttpCode = null;
        $this->curlInfo = [];

        if ($this->accessLog && $this->timeLog)
            $beginTime = microtime(true);

        if ($this->accessLog) {
            $paramLog = $this->paramLog ? (is_array($param) ? json_encode($param) : $param) : '';
            \LogUtil::info("{$this->module} {$url} {$paramLog}");
        }

        $monitoringKey = "{$this->module}:{$interface}";
        \Yii::beginMonitoring('innerApi', $monitoringKey, [
            'module' => $this->module,
            'interface' => $interface,
            'real_url' => $url,
        ]);

        switch ($this->httpMethod) {
            case self::HTTP_METHOD_POST:
                $result = $this->post($url, $param);
                break;
            case self::HTTP_METHOD_GET:
                $result = $this->get($url, $param);
                break;
            default:
                $result = $this->postJson($url, $param);
                break;
        }

        \Yii::endMonitoring('innerApi', $monitoringKey);


        if ($this->accessLog && $this->timeLog)
        {
            $endTime = microtime(true);
            \LogUtil::info('time spent: ' . ($endTime - $beginTime));
        }

        if ($result === false)
        {
            \LogUtil::error("{$this->module} $url ret false. error code: {$this->curlErrorCode} error desc: {$this->curlError}");
            \Yii::errorMonitoring('innerApi', $monitoringKey);

            $this->timeoutHandler ? ($this->timeoutHandler)($this->curlErrorCode, $this->curlError) : $this->handleCurlError();
        }

        if ($this->checkRspHttpCode)
        {
            if ($this->curlHttpCode !== 200)
            {
                \LogUtil::error("{$this->module} $url http code {$this->curlHttpCode}");
                \Yii::errorMonitoring('innerApi', $monitoringKey);
                $this->httpCodeHandler ? ($this->httpCodeHandler)($this->curlHttpCode, $result) : $this->handleHttpCode($result);
            }
        }

        if (!$this->jsonDecode)
            return $result;

        $resultJson = json_decode($result, true);

        if ($resultJson === null)
        {
            $msg = var_export($result, true);

            \LogUtil::error("{$this->module} $url json_decode null. return(var_export): {$msg}");
            \Yii::errorMonitoring('innerApi', $monitoringKey);
            $this->serverErrorHandler ? ($this->serverErrorHandler)($result) : $this->handleJsonDecodeFail($result);
        }

        if (!$this->checkRspJsonCode)
            return $resultJson;

        // 有些内部接口，错误码字段在请求正常时会不返回，可以通过apiNoCodeHandler短接
        if (!isset($resultJson[$this->rspJsonCodeField]))
        {
            $this->apiNoCodeHandler ? ($this->apiNoCodeHandler)($result) : $this->handleJsonNoCode($resultJson);
        }

        if (isset($resultJson[$this->rspJsonCodeField]) && $resultJson[$this->rspJsonCodeField] != 0)
        {
            $code = $resultJson[$this->rspJsonCodeField];
            $msg = $resultJson['msg'] ?? ($resultJson['message'] ?? '');

            \LogUtil::error("{$this->module} $url error. {$this->rspJsonCodeField}: {$code} msg: {$msg}");
            \Yii::errorMonitoring('innerApi', $monitoringKey);
            $this->apiErrorHandler ? ($this->apiErrorHandler)($resultJson) : $this->handleApiError($resultJson);
        }

        return $resultJson;
    }

    protected function handleCurlError()
    {
        $exception = new \InnerApiException("{$this->module} {$this->interface} curl error. code: {$this->curlErrorCode} msg: {$this->curlError} url: {$this->processUrl}", $this->curlErrorCode);
        $exception->setModule($this->getDomain());
        $exception->setErrorInfo(json_encode($this->curlInfo));
        throw $exception;
    }

    protected function handleJsonDecodeFail($result)
    {
        $exception = new \InnerApiException("{$this->module} {$this->interface} json decode fail. return msg: $result url: {$this->processUrl}");
        $exception->setModule($this->getDomain());
        throw $exception;
    }

    protected function handleJsonNoCode($json)
    {
        $exception = new \InnerApiException("{$this->module} {$this->interface} no {$this->rspJsonCodeField} in json. url: {$this->processUrl} ");
        $exception->setModule($this->getDomain());
        throw $exception;
    }

    protected function handleApiError($json)
    {
        $msg = $json['msg'] ?? ($json['message'] ?? '');
        $exception = new \InnerApiException("{$this->module} {$this->interface} code {$json[$this->rspJsonCodeField]} msg {$msg} url: {$this->processUrl}");
        $exception->setModule($this->getDomain());
        throw $exception;
    }

    protected function handleHttpCode($result)
    {
        $exception = new \InnerApiException("{$this->module} {$this->interface} http code: {$this->curlHttpCode} result: $result url: {$this->processUrl}");
        $exception->setModule($this->getDomain());
        throw $exception;
    }

    /**
     * 此函数是给一些单独接口需要快速定义不同的异常使用的，这个函数，一定要抛出异常，不然后续会出错
     * 异常内容随意，这个类在调用时遇到错误，一定会打日志
     * @param callable $handler
     */
    public function setTimeoutHandler(callable $handler)
    {
        $this->timeoutHandler = $handler;
    }

    /**
     * 此函数是给一些单独接口需要快速定义不同的异常使用的，这个函数，一定要抛出异常，不然后续会出错
     * @param callable $handler
     */
    public function setServerErrorHandler(callable $handler)
    {
        $this->serverErrorHandler = $handler;
    }

    /**
     * 此函数是给一些单独接口需要快速定义不同的异常使用的，这个函数，一定要抛出异常，不然后续会出错
     * @param callable $handler
     */
    public function setApiErrorHandler(callable $handler)
    {
        $this->apiErrorHandler = $handler;
    }

    public function setApiNoCodeHandler(callable $handler)
    {
        $this->apiNoCodeHandler = $handler;
    }

    public function setHttpCodeHandler(callable $handler)
    {
        $this->httpCodeHandler = $handler;
    }

    protected function get($url, $param)
    {
        if (!empty($param))
        {
            if (is_array($param))
                $url = $url . '?' . http_build_query($param);
            else
                $url = $url . '?' . $param;
        }

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);

        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeader());

        if( $this->timeout )
        {
            if(is_float($this->timeout))
                curl_setopt($ch, CURLOPT_TIMEOUT_MS, intval($this->timeout * 1000));
            else
                curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        }

        $ret = curl_exec($ch);

        if ($ret === false)
            $this->processErrorInfo($ch);

        $this->curlHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);
        return $ret;
    }

    protected function postJson($url, $param)
    {
        if (is_array($param))
            $param = json_encode($param);

        //http post 请求连接地址
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        if (!$this->resetHeader)
            $this->header[] = "content-type:application/json";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeader());

        if( $this->timeout )
        {
            if( $this->timeout < 1)
                throw new \ProcessException('php7 curl timeout less than 1000ms always fails');

            if(is_float($this->timeout))
                curl_setopt($ch, CURLOPT_TIMEOUT_MS, intval($this->timeout * 1000));
            else
                curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        }

        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $ret = curl_exec($ch);

        if ($ret === false)
            $this->processErrorInfo($ch);

        $this->curlHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);
        return $ret;
    }

    protected function post($url, $param)
    {
        //http post 请求连接地址
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeader());
        if( $this->timeout )
        {
            if( $this->timeout < 1)
                throw new \ProcessException('php7 curl timeout less than 1000ms always fails');

            if(is_float($this->timeout))
                curl_setopt($ch, CURLOPT_TIMEOUT_MS, intval($this->timeout * 1000));
            else
                curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        }

        curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($param) ? http_build_query($param) : $param);
        $ret = curl_exec($ch);

        if ($ret === false)
            $this->processErrorInfo($ch);

        $this->curlHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);
        return $ret;
    }

    protected function processErrorInfo($handle)
    {
        $this->curlError = curl_error($handle);
        $this->curlErrorCode = curl_errno($handle);
        $this->curlInfo = curl_getinfo($handle);
    }
}
