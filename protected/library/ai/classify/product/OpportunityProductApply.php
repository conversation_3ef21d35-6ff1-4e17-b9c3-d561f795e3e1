<?php
/**
 * Created by PhpStorm.
 * User: tony
 * Date: 2020-04-27
 * Time: 10:38
 */


namespace common\library\ai\classify\product;


use common\library\ai\classify\ai_field_data\OpportunityAIFieldData;
use common\library\ai\classify\capture\CaptureCard;
use common\library\ai\classify\capture\CaptureCardLog;
use common\library\ai\classify\mail\Apply;
use common\library\ai\classify\mail\Classify;
use common\library\opportunity\Opportunity;
use common\library\report\error\ErrorReport;

class OpportunityProductApply extends Apply
{
    protected $templateInfo;

    public function apply(Classify $classify, $processResult)
    {
        $this->classify = $classify;
        $this->processResult = $processResult;

        $productResult = $this->classify->getProductResult();
        if (empty($productResult)) {
            return false;
        }

        $extractFileId = $productResult['file_id'];
        $currency = $productResult['currency'];
        $templateId = $productResult['templateId'];
        $opportunityProductList = $productResult['products'];

        if (!empty($opportunityProductList) && $this->getSetting()->opportunityProductEnableUpdate) {
            $failList = [];
            $successList = [];
            $successListNames = [];

            $opportunityIds = array_unique($this->processResult['create_trail_opportunity_ids'] ?? []);
            $opportunityIds = $this->filterOpportunity($opportunityIds, $currency, $failList);

            foreach ($opportunityIds as $opportunityId) {
                $opportunity = new Opportunity($this->clientId, $opportunityId);
                $opportunity->setUserId($this->userId);
                $opportunity->setFieldEditType(OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_AI);
                $opportunity->setFieldEditReferMailId($this->classify->getLastMail()->mail_id);
                $opportunity->extendAttributes()->auto_update_product_time = $this->classify->getLastMail()->receive_time;
                $opportunity->setEditInfo(OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_AI, $this->classify->getLastMail()->receive_time);
                $opportunity->classifySetProduct($opportunityProductList, $this->getTemplate($templateId));
                $opportunity->save();

                $successList[] = $opportunity->opportunity_id;
                $successListNames[$opportunity->opportunity_id] = $opportunity->name;
            }

            $log = [
                'opportunity_product_filter' => $failList ?? [],
                'opportunity_product_success' => $successList ?? [],
            ];

            $classifyLog = $this->classify->getLastClassifyLog();
            $data = [];
            if (!empty($classifyLog->product_data)) {
                $data = json_decode(snappy_uncompress($classifyLog->product_data), true) ?? [];
            }
            $data = array_merge($data, $log);
            $classifyLog->product_data = snappy_compress(json_encode($data));
            $classifyLog->save();

            if (count($log['opportunity_product_success']) && $this->skipCaptureLog === false) {
                if (\common\library\ai\classify\capture\Helper::hasCardInfo($this->clientId, $extractFileId)) {
                    $capture = new CaptureCardLog($this->clientId, $extractFileId);
                    foreach ($log['opportunity_product_success'] as $opportunityId) {
                        $capture->setResult([
                            'refer_type' => CaptureCard::REFER_TYPE_OPPORTUNITY_PRODUCT,
                            'method' => CaptureCard::METHOD_UPDATE,
                            'name' => $successListNames[$opportunityId] ?? '',
                            'opportunity_id' => $opportunityId
                        ]);
                    }
                    $capture->save();
                }
            }
        }

        return true;
    }

    /**
     * @param $templateId
     * @return array
     * 获取产品模板设置保存到 opportunity->externalAttributes
     */
    protected function getTemplate($templateId)
    {
        if (!$templateId) {
            return [];
        }

        if (isset($this->templateInfo[$templateId])) {
            return $this->templateInfo[$templateId];
        }

        try {
            $setting = $this->getSetting()->getApplySettings()->getProductTemplateSetting($templateId);
        } catch (\Exception $e) {
            \LogUtil::info("mailId:{$this->classify->getLastMail()->mail_id} getProductTemplateSetting fail! [$templateId]");
            ErrorReport::phpError(new \CExceptionEvent(null,$e), $e->getTrace());
            return [];
        }

        if (empty($setting)) {
            $this->templateInfo[$templateId] = [];
            return $this->templateInfo[$templateId];
        }

        $data = [
            'tmplId' => $setting['tmplId'],
            'tmplNo' => $setting['tmplNo']
        ];

        foreach ($setting['selected'] as $key => $item) {
            $data[$key] = $item ?? '';
        }

        $this->templateInfo[$templateId] = $data;
        return $this->templateInfo[$templateId];
    }

    protected function filterOpportunity(array $opportunityIds, $currency, &$failList)
    {
        if (empty($opportunityIds))
            return [];

        $returnIds = [];
        $autoUpdateEnableMap = [];
        $updateProductTimeMap = [];

        $idString = implode(",", $opportunityIds);
        $db = \PgActiveRecord::getDbByClientId($this->clientId);

        //货币单位需一致
        $currency = strtoupper($currency);
        $sql = "select opportunity_id, currency from tbl_opportunity where client_id = :clientId and opportunity_id IN ($idString) and enable_flag = 1";
        $list = $db->createCommand($sql)->queryAll(true, [':clientId' => $this->clientId]);
        $currencyMap = array_column($list,'currency','opportunity_id');


        //单个商机产品自动化开关、自动化产品更新时间
        $sql = "select opportunity_id, key, value from tbl_opportunity_external where client_id = :clientId and opportunity_id IN ($idString) and key in ('auto_update_product','auto_update_product_time')";
        $list = $db->createCommand($sql)->queryAll(true, [':clientId' => $this->clientId]);
        foreach ($list as $item) {
            if ($item['key'] == 'auto_update_product') {
                $autoUpdateEnableMap[$item['opportunity_id']] = $item['value'];
                continue;
            }

            if ($item['key'] == 'auto_update_product_time') {
                $updateProductTimeMap[$item['opportunity_id']] = $item['value'];
                continue;
            }
        }

        foreach ($opportunityIds as $opportunityId) {
            if (!isset($currencyMap[$opportunityId])) {
                $failList[$opportunityId] = "opportunityId not found!";
                continue;
            }

            if ($currencyMap[$opportunityId] != $currency) {
                $failList[$opportunityId] = "currency: {$currencyMap[$opportunityId]} != $currency";
                continue;
            }

            if (isset($autoUpdateEnableMap[$opportunityId]) && $autoUpdateEnableMap[$opportunityId] == 0) {
                $failList[$opportunityId] = "auto_update_product:{$autoUpdateEnableMap[$opportunityId]}";
                continue;
            }

            if (isset($updateProductTimeMap[$opportunityId]) && strtotime($updateProductTimeMap[$opportunityId])  > strtotime($this->classify->getLastMail()->receive_time)) {
                $failList[$opportunityId] = "auto_update_product_time:{$updateProductTimeMap[$opportunityId]} > {$this->classify->getLastMail()->receive_time}";
                continue;
            }

            $returnIds[] = $opportunityId;
        }

        return $returnIds;
    }
}