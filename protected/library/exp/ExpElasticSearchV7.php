<?php


namespace common\library\exp;


use Elasticsearch\Common\Exceptions\Missing404Exception;
use LogUtil;
use Yii;

class ExpElasticSearchV7
{

    protected $modelClassList;

    protected $expOnlineIndexPostfix;
    /** @var \Elasticsearch_5\Client $exp */
    public $exp;
    /** @var  \Elasticsearch_5\Client $exp_online */
    public $exp_online;

    public function __construct($exp, $exp_online)
    {
        $this->exp = $exp;
        $this->exp_online = $exp_online;
        $this->expOnlineIndexPostfix = Yii::app()->params['exp_elastic_search_index_postfix'];
        if (empty($this->expOnlineIndexPostfix)) {
            throw  new ProcessException('config  exp_elastic_search_index_postfix param error!');
        }
    }

    protected function log($info)
    {
        echo $info . "\n";
        LogUtil::info($info);
    }

    public function deleteIndex()
    {
        $modelClassList = $this->getModelClassList();
        foreach ($modelClassList as $modelClass) {
            /** @var \common\components\ElasticSearchActiveRecord $model */
            $model = new $modelClass;

            $indexExpName = $model->index() . '_' . $this->expOnlineIndexPostfix;
            try {
                $this->exp_online->indices()->delete([
                    'index' => $indexExpName,
                ]);
                $this->log('clear exp index :' . $indexExpName);
            } catch (\Elasticsearch_5\Common\Exceptions\Missing404Exception $e) {
                LogUtil::error('es exp online index not exist' . $indexExpName);
            } catch (Missing404Exception $e) {
                LogUtil::error('es exp online index not exist' . $indexExpName);
            }
        }
    }

    public function createIndex()
    {
        $modelClassList = $this->getModelClassList();
        foreach ($modelClassList as $modelClass) {
//            if (!strpos($modelClass, 'Product')) {
//                continue;
//            }
            /** @var \common\components\ElasticSearchActiveRecord $model */
            $model = new $modelClass;
            $indexExpName = $model->index() . '_' . $this->expOnlineIndexPostfix;

            $setting = $model->setting();
            $setting['number_of_shards'] = 1;
            $setting['number_of_replicas'] = 0;
            $params = [
                'index' => $indexExpName,
                'body'  => [
                    'settings' => $setting,
                    'mappings' => $model->mapping(),
                    //'warmers' => [ /* ... */ ],
                    //'aliases' => [ /* ... */ ],
                    //'creation_date' => '...'
                ],
            ];
            try {
                $this->exp_online->indices()->create($params);
                $this->log('create exp index :' . $indexExpName . ' from  model class :' . $modelClass . 'time:' . time());
            } catch (\Throwable $exception) {
                $this->log('fail to create exp index :' . $indexExpName . ' from  model class :' . $modelClass . ' msg:' . $exception->getMessage());
            }
        }
    }

    public function reindex($templateIndexPostFix = 'exp')
    {
        $modelClassList = $this->getModelClassList();
        foreach ($modelClassList as $modelClass) {
            /** @var \common\components\ElasticSearchActiveRecord $model */
            $model = new $modelClass;

            $source = $model->index() . '_' . $templateIndexPostFix;
            $dest = $model->index() . '_' . $this->expOnlineIndexPostfix;
            $params = [
                'body' => [
                    'source' => [
                        'index' => $source,
                    ],
                    'dest'   => [
                        'index' => $dest,
                    ]
                ]
            ];
            try {
                $this->log('reindex:' . json_encode($params));
                $response = $this->exp_online->reindex($params);
                $this->log('reindex result: ' . json_encode($response));
            } catch (\Exception $e) {
                \LogUtil::error($e->getMessage());
            }

        }
    }


    protected function getModelClassList()
    {
        if ($this->modelClassList == null) {
            $modelList = [];
            $path = \Yii::getPathOfAlias('application.models.es_search');
            $fileList = scandir($path);
            foreach ($fileList as $file) {
                if ($file == '.' || $file == '..') {
                    continue;
                }
                if (strpos($file, 'QuotationSearch') !== false) {
                    $this->log("skip $file for ES7");
                    continue;
                }
                $modelList[] = "\\common\\models\\es_search\\" . basename($file, '.php');
            }

            $this->modelClassList = $modelList;
        }

        return $this->modelClassList;
    }


    public function sync(array $clientIds)
    {
        $modelClassList = $this->getModelClassList();
        foreach ($clientIds as $clientId) {
            foreach ($modelClassList as $modelClass) {
                /** @var \common\components\ElasticSearchActiveRecord $model */
                $model = new $modelClass;
                $indexName = $model->index();
                $indexExpName = $indexName . '_' . $this->expOnlineIndexPostfix;
                $this->log('sync index :' . $indexName . ' clientId:' . $clientId . '  from to ' . $indexExpName . '  model class :' . $modelClass);

                $params = [
                    'index'   => $indexName,
                    "scroll"  => "300s",
                    "size"    => 5000,
                    'routing' => intval($clientId),
                    'body'    => [
                        'query' => [
                            'term' => [
                                'client_id' => intval($clientId)
                            ],
                        ]
                    ],
                ];

                $response = $this->exp->search($params);

                $insertCount = 0;
                while (isset($response['hits']['hits']) && count($response['hits']['hits']) > 0) {
                    $insertCount += count($response['hits']['hits']);
                    $this->insert($indexExpName, $response['hits']['hits']);
                    $scrollId = $response['_scroll_id'];
                    $response = $this->exp->scroll([
                            "scroll_id" => $scrollId,
                            "scroll"    => "30s"
                        ]
                    );
                }
                $this->log('done sync index :' . $indexName . ' clientId:' . $clientId . '  from  model class :' . $modelClass . " count: " . $insertCount);
            }
        }
    }

    protected function insert($indexName, $list)
    {
        $bulk = [];
        foreach ($list as $item) {
            $bulk[] = [
                'index' => [
                    '_index'   => $indexName,
                    '_id'      => $item['_id'],
                    'routing' => $item['_routing'],
                ]
            ];

            $bulk[] = $item['_source'];
        }

        $params = [
            'index' => $indexName,
            'body'  => $bulk
        ];

        if (!empty($bulk)) {
            $res = $this->exp_online->bulk($params);
        }
    }
}