<?php
/**
 * This file is part of the xiaoman/crm.
 *
 * (c) sevenshi <<EMAIL>>
 *
 */

namespace common\library\statistics\query\custom;


use common\library\department\DepartmentPermission;
use common\library\performance_v2\goal\PerformanceV2GoalList;
use common\library\performance_v2\goal_config\PerformanceV2GoalConfig;
use common\library\performance_v2\goal_config\PerformanceV2ProcessService;
use common\library\performance_v2\goal_config\NewPerformanceV2ProcessService;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\record\PerformanceV2RecordList;
use common\library\performance_v2\rule\PerformanceV2Rule;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\statistics\data_adapter\AbstractDataAdapter;
use common\library\statistics\data_adapter\Arr;
use common\library\statistics\Locator;

class DepartmentPerformanceV2FinishQuery extends AbstractPerformanceV2CustomQuery
{

    /**
     * @return AbstractDataAdapter
     */
    public function query()
    {
        $locator = Locator::instance();
        $clientId = $locator->getClientId();
        $userId = $locator->getUserId();

        $params = $locator->getReportConfig()->getQueryConfig()->getParams(1);
        $startDate = $this->startDate;
        $endDate = $this->endDate;

        //无效参数user_ids就置为空，走查看范围的最大值
        if ($params['common.visible']->getType() == 'select_visible_department_id') {
            $departmentId = $params['common.visible']->getValue();
        } else {
            $departmentId = [];
        }

        $scopeDepId = (new DepartmentPermission($locator->getClientId(), $locator->getUserId()))->permission(PrivilegeConstants::PRIVILEGE_CRM_PERFORMANCE_VIEW)->departmentIds();

        // 未分配部门直接返回空值
        if (!is_array($departmentId) && $departmentId < 0) {
            return new Arr([]);
        }

        //可见范围为我的企业，但是没有可查看的部门id
        if ($departmentId == 0 && empty($scopeDepId)) {
            return new Arr([]);
        }

        //默认没传部门id，没有可查看的部门id
        if (is_array($departmentId) && empty($departmentId) && empty($scopeDepId)) {
            return new Arr([]);
        }

        //没传部门id， 可查看部门id有值，则取可查看部门最小值
        if (is_array($departmentId) && empty($departmentId) && !empty($scopeDepId)) {
            $departmentId = min($scopeDepId);
        }

        $departmentId = is_array($departmentId) ? current($departmentId) : $departmentId;

        if ($this->ruleId == 0 || (!$this->noReferFlag && empty($this->referObjectIds)) ) {
            return new Arr([]);
        }

        $ruleConfig          = (new PerformanceV2GoalConfig($clientId, $this->ruleId))->loadFirstItemByRuleId($this->ruleId);
        $ruleInfo            = new PerformanceV2Rule($clientId,$this->ruleId);
        $calculateRule       = strtolower($ruleInfo->calculate_rule);
        $distinctWithOwnerId = \common\library\performance_v2\Helper::checkNeedDistinctWithOwnerId($ruleInfo->getAttributes());
        $recordModel         = new PerformanceV2RecordList($clientId, $userId);
        $recordModel->setRuleId($this->ruleId);
        $recordModel->setOwnerId($departmentId);
        $recordModel->setStartAccountDate($startDate);
        $recordModel->setEndAccountDate($endDate);
        $recordModel->setRecordType(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);

        if($distinctWithOwnerId){
            $recordModel->setDistinctField('client_id,rule_id,refer_id,owner_id');
        } else {
            $recordModel->setDistinctField('client_id,rule_id,refer_id');
        }

        if($calculateRule == 'count'){
            $recordModel->setFields(' count(1) as cnt ');
        } else {
            $recordModel->setFields(' sum(indicator_value) as indicator_value ');
        }
        if (!$this->noReferFlag) {
            $recordModel->setReferId($this->referObjectIds);
        }

        $totalAmount = $recordModel->distinctRecordCount();

        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_PROCESS_GOAL)
        {
            // 重构后代码

            $performanceV2ProcessService = new NewPerformanceV2ProcessService($clientId, $userId);
            $performanceV2ProcessService->setRuleIds([$this->ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($departmentId);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            if (!$this->noReferFlag) {
                $performanceV2ProcessService->setReferObjectIds($this->referObjectIds);
            }
            $processResult = $performanceV2ProcessService->getNewProcessRuleFinishDetail();

            $totalInfo = [
                'department_performance.target_amount'           => $processResult['total']['amount'] ?? 0.00,
                'department_performance.finish_amount'           => $processResult['total']['indicator_value'] ?? 0.00,
                'department_performance.last_year_finish_amount' => $processResult['total']['constrast_indicator_value'] ?? 0.00,
            ];

            $data = [];
            foreach (($processResult['list'] ?? []) as $item) {
                $data[] = [
                    'department_performance.department_id'           => $item['department_id'] ?? 0,
                    'department_performance.cycle'                   => $item['cycle'] ?? '',
                    'department_performance.year'                    => $item['year'] ?? '',
                    'department_performance.cycle_num'               => $item['cycle_num'] ?? '',
                    'department_performance.target_amount'           => $item['amount'] ?? 0.00,
                    'department_performance.finish_amount'           => $item['indicator_value'] ?? 0.00,
                    'department_performance.last_year_finish_amount' => $item['constrast_indicator_value'] ?? 0.00,
                    'show_raw_year'                                  => $item['show_raw_year'] ?? true,
                ];
            }
            unset($processResult, $processTotal);

            // 重构前代码

            /*
            empty($ruleConfig->time_granularity) && $ruleConfig->time_granularity = \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;
            $timeType = PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity] ?? \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;

            $performanceV2ProcessService = new PerformanceV2ProcessService($clientId, $userId);
            $performanceV2ProcessService->setFilterDisplaySetting(false);
            $performanceV2ProcessService->setRuleIds([$this->ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setReferId($departmentId);
            $performanceV2ProcessService->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            $performanceV2ProcessService->setShowByEachTimeType(true);
            $performanceV2ProcessService->setShowGoals(true);
            $performanceV2ProcessService->setTimeType($timeType);
            $performanceV2ProcessService->setShowConstrast(1);
            $performanceV2ProcessService->setShowIndicatorValue(1);
            $processGoalList    = $performanceV2ProcessService->getProcessRuleList();
            $processGoalList    = empty($processGoalList) ? [] : array_pop($processGoalList);
            $referTimeTypeList = $processGoalList[$performanceV2ProcessService->getScopeName()] ?? [];
            ksort($referTimeTypeList);
            $data               = [];
            // 对于设置了目标值为日目标的情况 在下面的循环会把去年的目标值也拿出来计算 因此 取最后一个周期的目标值累加 其余的丢弃
            $referListKeys = array_keys($referTimeTypeList);
            $endCycle = end($referListKeys);

            $totalInfo = [
                'department_performance.target_amount'           => 0,
                'department_performance.finish_amount'           => 0,
                'department_performance.last_year_finish_amount' => 0,
            ];

            if(!empty($referTimeTypeList)){
                switch ($ruleConfig->time_granularity){
                    // 天
                    case \PerformanceV2GoalsConfig::TIME_GRANULARITY_DAY:
                        foreach ($referTimeTypeList as $year => $monthList){
                            foreach ($monthList as $month => $dayList){
                                foreach ($dayList as $dayIndex => $referList){
                                    if (strtotime($dayIndex) < strtotime($startDate)) {
                                        continue;
                                    }
                                    foreach ($referList as $referId => $referInfo){
                                        $timeStart = date("Y-m-d", strtotime($referInfo['time_start']));
                                        $targetAmount = $referInfo['amount'] > 0 ? round($referInfo['amount'], 2) : 0;
                                        $targetAmount = $year == $endCycle ?  $targetAmount : 0;
                                        $data[]    = [
                                            'department_performance.department_id'           => $referId,
                                            'department_performance.cycle'                   => 0,
                                            'department_performance.year'                    => $timeStart,
                                            'department_performance.cycle_num'               => "{$year}.{$dayIndex}",
                                            'show_raw_year'                                  => true,
                                            'department_performance.target_amount'           => $targetAmount,
                                            'department_performance.finish_amount'           => round($referInfo['indicator_value'], 2),
                                            'department_performance.last_year_finish_amount' => round($referInfo['constrast_indicator_value'], 2),
                                        ];

                                        $totalInfo['department_performance.target_amount']           += $targetAmount;
                                        $totalInfo['department_performance.finish_amount']           += $referInfo['indicator_value'];
                                        $totalInfo['department_performance.last_year_finish_amount'] += $referInfo['constrast_indicator_value'];
                                    }
                                }
                            }
                        }

                        break;
                    // 周
                    case \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK:
                        foreach ($referTimeTypeList as $year => $weekList){
                            foreach ($weekList as $weekIndex => $referList){
                                foreach ($referList as $referId => $referInfo){
                                    $timeStart = date("Y-m-d", strtotime($referInfo['time_start']));
                                    $timeEnd   = date("Y-m-d", strtotime($referInfo['time_end']));
                                    $data[]    = [
                                        'department_performance.department_id'           => $referId,
                                        'department_performance.cycle'                   => 1,
                                        'department_performance.year'                    => "{$timeStart}~{$timeEnd}",
                                        'department_performance.cycle_num'               => $weekIndex,
                                        'department_performance.target_amount'           => $referInfo['amount'] > 0 ? round($referInfo['amount'], 2) : 0,
                                        'department_performance.finish_amount'           => round($referInfo['indicator_value'], 2),
                                        'department_performance.last_year_finish_amount' => round($referInfo['constrast_indicator_value'], 2),
                                        'show_raw_year'                                  => true,
                                    ];

                                    $totalInfo['department_performance.target_amount']           += $referInfo['amount'];
                                    $totalInfo['department_performance.finish_amount']           += $referInfo['indicator_value'];
                                    $totalInfo['department_performance.last_year_finish_amount'] += $referInfo['constrast_indicator_value'];
                                }
                            }
                        }

                        break;
                    // 月
                    case \PerformanceV2GoalsConfig::TIME_GRANULARITY_MONTH:
                        foreach ($referTimeTypeList as $year => $monthList){
                            foreach ($monthList as $month => $referList){
                                foreach ($referList as $referId => $referInfo){
                                    $data[] = [
                                        'department_performance.department_id'           => $referId,
                                        'department_performance.cycle'                   => 1,
                                        'department_performance.year'                    => $year . '/' .(($month < 10) ? "0{$month}" : $month),
                                        'department_performance.cycle_num'               => $month,
                                        'department_performance.target_amount'           => $referInfo['amount'] > 0 ? round($referInfo['amount'], 2) : 0,
                                        'department_performance.finish_amount'           => round($referInfo['indicator_value'], 2),
                                        'department_performance.last_year_finish_amount' => round($referInfo['constrast_indicator_value'], 2),
                                        'show_raw_year'                                  => true,
                                    ];

                                    $totalInfo['department_performance.target_amount']           += $referInfo['amount'];
                                    $totalInfo['department_performance.finish_amount']           += $referInfo['indicator_value'];
                                    $totalInfo['department_performance.last_year_finish_amount'] += $referInfo['constrast_indicator_value'];
                                }
                            }
                        }
                        break;
                }
            }
            */

        }else{
            $list = new PerformanceV2GoalList($clientId,$userId,$this->ruleId);
            $list->setReferIds($departmentId,PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            $list->setStartAccountDate($startDate);
            $list->setEndAccountDate($endDate);
            $list->setStartYearMonth(date("Y",strtotime($startDate)),date("m",strtotime($startDate)));
            $list->setEndYearMonth(date("Y",strtotime($endDate)),date("m",strtotime($endDate)));
            $list->setFillWithEmpty(true);
            $list->setWithSubDepartment(true, true);

            $fields = ["extract(month from account_date) as cycle_num","extract(year from account_date) as year","sum(indicator_value) as sum_amount"];
            $groupBy = [];

            $targetField = \common\library\performance_v2\Helper::getTargetField($clientId, $this->ruleId);

            // 公式统计没有设置department_agg_type,根据聚合方式决定是否去重
            if (empty($targetField)) {
                if ($ruleInfo->calculate_rule == \common\library\performance_v2\PerformanceV2Constant::CALCULATE_RULE_SUM) {
                    $targetField['department_agg_type'] = PerformanceV2Constant::DEPARTMENT_AGG_TYPE_DEPARTMENT;
                } elseif ($ruleInfo->calculate_rule == \common\library\performance_v2\PerformanceV2Constant::CALCULATE_RULE_COUNT) {
                    $targetField['department_agg_type'] = PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER;
                }
            }
            if ($targetField['department_agg_type'] == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_DEPARTMENT) {
                $groupBy = ['owner_id','record_type','year', 'cycle_num'];
                $fields[] = 'owner_id';
            } else {
                $groupBy = ['year', 'cycle_num'];
            }

            if($distinctWithOwnerId){
                $list->getFormatter()->setRecordDistinctField('client_id, rule_id, refer_id, owner_id');
            } else {
                $list->getFormatter()->setRecordDistinctField('client_id, rule_id, refer_id');
            }

            $list->getFormatter()->setRecordGroupBy($groupBy);
            $list->getFormatter()->setRecordFields($fields);
            $list->getFormatter()->setShowFinish(true);
            $list->getFormatter()->setStartAccountDate($startDate);
            $list->getFormatter()->setEndAccountDate($endDate);
            $list->getFormatter()->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            $list->getFormatter()->setOwnerId($departmentId);
            $list->getFormatter()->setRuleId($this->ruleId);
            if (!$this->noReferFlag) {
                $list->getFormatter()->setReferObjectIds($this->referObjectIds);
            }
            $list->getFormatter()->setDepth(2);
            $goals = $list->find();

            $data = [];
            $totalInfo = [
                'department_performance.target_amount' => 0,
                'department_performance.finish_amount' => 0,
                'department_performance.last_year_finish_amount' => 0,
            ];
            foreach ($goals as $goal) {
                $targetAmount = $goal['amount'] > 0 ? round($goal['amount'], 2) : 0;
                if ($goal['refer_id'] == $departmentId) {
                    $cycleGoalList = array_filter($goals, function($item) use ($goal) {
                        return $goal['year'] == $item['year'] && $goal['month'] == $item['month'];
                    });
                    $finishAmount = round(array_sum(array_column($cycleGoalList, 'finish_amount')), 2);
                    $lastYearFinishAmount = round(array_sum(array_column($cycleGoalList, 'last_year_finish_amount')), 2);
                    $totalInfo['department_performance.target_amount'] += $targetAmount;
                    $totalInfo['department_performance.finish_amount'] += $finishAmount;
                    $totalInfo['department_performance.last_year_finish_amount'] += $lastYearFinishAmount;
                } else {
                    $finishAmount = round(floatval($goal['finish_amount']), 2);
                    $lastYearFinishAmount = round($goal['last_year_finish_amount'], 2);
                }

                $data[] = [
                    'department_performance.department_id' => $goal['refer_id'],
                    'department_performance.cycle' => 1,
                    'department_performance.cycle_num' => $goal['month'],
                    'department_performance.year' => $goal['year'],
                    'department_performance.target_amount' => $targetAmount,
                    'department_performance.finish_amount' => $finishAmount,
                    'department_performance.last_year_finish_amount' => $lastYearFinishAmount,
                ];
            }
        }

        $summates = $locator->getReportConfig()->getFormatConfig()->getSummate();
        foreach ( $summates as $summate )
        {
            if ($summate->getMethod() == 'sum'
                && in_array($summate->getField(), [
                    'department_performance.target_amount',
                    'department_performance.finish_amount',
                    'department_performance.last_year_finish_amount'
                ])) {
                $summate->setTotal(round($totalInfo[$summate->getField()] ?? 0, 2));
            } elseif ($summate->getMethod() == 'percent' && $summate->getField() == 'department_performance.finish_amount') {
                if ($totalInfo['department_performance.target_amount'] == 0) {
                    $percent = '-';
                } else {
                    $percent = round(($totalInfo['department_performance.finish_amount'] / $totalInfo['department_performance.target_amount']) * 100,
                            2);
                }
                $summate->setTotal($percent);
            } elseif($summate->getMethod() == 'sum' && $summate->getField() == 'department_performance.finish_amount'){
                $summate->setTotal(round($totalAmount, 2));
            }
        }

        $result = new Arr(array_values($data));
        $result->setCount($totalAmount);
        return $result;
    }

}
