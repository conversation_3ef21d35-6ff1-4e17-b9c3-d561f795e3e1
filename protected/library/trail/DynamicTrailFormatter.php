<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: Tony
 * Date: 18/8/2
 * Time: 上午10:48
 */

namespace common\library\trail;

use common\library\account\UserList;
use common\library\auto_market\handler\MarketingAutomationHandlerFilter;
use common\components\BaseObject;
use common\library\auto_market\plan\MarketingAutomationPlanApi;
use common\library\auto_market\plan\MarketingAutomationPlanFilter;
use common\library\cms\conversation\CmsConversationService;
use common\library\cms\conversation\MessageList;
use common\library\cms\inquiry\form_mapfield\FormFieldMapList;
use common\library\cms\inquiry\InquiryService;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer\business_card\BusinessCard;
use common\library\facebook\page\FacebookPage;
use common\library\facebook\page\FacebookPageList;
use common\library\facebook\page\form\FacebookPageForm;
use common\library\facebook\page\form_lead\FacebookFormLead;
use common\library\google_ads\lead\SiteSessionLead;
use common\library\customer_v3\customer\orm\Customer;
use common\library\invoice\Order;
use common\library\invoice\OrderList;
use common\library\invoice\Quotation;
use common\library\invoice\QuotationList;
use common\library\lead\LeadCustomerList;
use common\library\invoice\status\InvoiceStatusService;
use common\library\lead\LeadCustomer;
use common\library\lead_v2\Lead;
use common\library\mail\Mail;
use common\library\mail\MailList;
use common\library\opportunity\OpportunityList;
use common\library\schedule\Schedule;
use common\library\setting\library\origin\Origin;
use common\library\sns\Constants;
use common\library\track\TrackHelper;
use common\library\trail\comment\DynamicTrailComment;
use common\library\trail\comment\DynamicTrailCommentList;
use common\library\trail\entities\DynamicTrailData;
use common\library\util\IP;
use common\library\util\PgsqlUtil;
use function AlibabaCloud\Client\json;

class DynamicTrailFormatter extends \ListItemFormatter
{
    use DynamicTrailFormatterMap;

    protected $operatorUser = null;
    protected $simpleData = false;
    protected $showNodeTypeActionName = true;
    protected $showTrackFlag = false;
    protected $showCaptureCard = false;
    protected $showAdjustEmailDynamic = false;
    protected $showInvalidInquiryFlag = false;

    protected $showOpportunityInfo = false;
    protected $showCreateUserInfo = false;

    protected $showCustomerInfo = false;

    protected $showNodeTypeName = false;

    protected $showLeadInfo = false;

	protected $showCommentList = false;

	protected $showMarketingAutomationPlan = false;

    protected $showCompanyId = 0;

    protected $shops = [];
    protected $countries = [];
    protected $highlightData = [];

//	动态关键字搜索(客户)
	protected $keywordSearch = [];


    /**
     * @param bool $showOpportunityInfo
     */
    public function setShowOpportunityInfo(bool $showOpportunityInfo)
    {
        $this->showOpportunityInfo = $showOpportunityInfo;
    }

    /**
     * @param $showCompanyId
     */
    public function setShowCompanyId($showCompanyId)
    {
        $this->showCompanyId = $showCompanyId;
    }

    public function setShowCreateUserInfo(bool $showCreateUserInfo)
    {
        $this->showCreateUserInfo = $showCreateUserInfo;
    }

    public function setShowCustomerInfo(bool $showCustomerInfo)
    {
        $this->showCustomerInfo = $showCustomerInfo;
    }


    public function setHighlightData($data)
    {
        if (empty($this->highlightData)) {
            $this->highlightData = $data;
        } else {
            $this->highlightData += $data;
        }
    }

    protected $emptyDataDefaultValue = false;

    public function setOperator($user_id)
    {
        $this->operatorUser = \User::getUserObject($user_id);
    }

    public function setSimpleData($flag)
    {
        $this->simpleData = $flag;
    }

    public function setShowInvalidInquiryFlag($flag)
    {
        $this->showInvalidInquiryFlag = $flag;
    }

    public function setShowNodeTypeActionName($flag)
    {
        $this->showNodeTypeActionName = $flag;
    }

    public function setShowTrackFlag($showTrackFlag)
    {
        $this->showTrackFlag = $showTrackFlag;
    }

    public function setEmptyDataDefaultValue($emptyDataDefaultValue)
    {
        $this->emptyDataDefaultValue = $emptyDataDefaultValue;
    }

    public function setShowCaptureCard($flag)
    {
        $this->showCaptureCard = $flag;
    }

    public function setShowAdjustEmailDynamic($flag)
    {
        $this->showAdjustEmailDynamic = $flag;
    }

    /**
     * @param bool $showNodeTypeName
     */
    public function setShowNodeTypeName(bool $showNodeTypeName): void
    {
        $this->showNodeTypeName = $showNodeTypeName;
    }

    /**
     * @param bool $showLeadInfo
     */
    public function setShowLeadInfo(bool $showLeadInfo): void
    {
        $this->showLeadInfo = $showLeadInfo;
    }


	/**
	 * @param bool $showCommentList
	 */
	public function setShowCommentList(bool $showCommentList): void {

		$this->showCommentList = $showCommentList;
	}

    /**
     * @param bool showMarketingAutomationPlan
     */
    public function setShowMarketingAutomationPlan(bool $showMarketingAutomationPlan): void {

        $this->showMarketingAutomationPlan = $showMarketingAutomationPlan;
    }

	/**
	 * @return array
	 */
	public function getKeywordSearch(): array {

		return $this->keywordSearch;
	}

	/**
	 * @param array $keywordSearch
	 */
	public function setKeywordSearch(array $keywordSearch): void {

		$this->keywordSearch = $keywordSearch;
	}


    protected function getData($client_id, $trail_id)
    {
        if ($this->simpleData) {
            $data = $this->getMapData('trailData', $trail_id);
            return $data ? json_decode($data, true) : [];
        } else {
            $data = new DynamicTrailData($client_id, $trail_id);

            if (!$data) {
                \LogUtil::error($trail_id . " trail data empty");
                return [];
            } else {
                return json_decode($data->data, true);
            }
        }
    }

    protected function getFollowUpList()
    {
        return [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111];
    }

    protected function getCustomerInfo($client_id, $customer_id)
    {
        $c = \common\library\customer\Helper::getCustomer($client_id, $customer_id);

        return  [
            'customer_id' => $c->isExist() ? $c->customer_id : $customer_id,
            'name' => $c->isExist() ? $c->name : '',
            'email' => $c->isExist() ? $c->email : '',
            'tel_list' => $c->isExist() ? $c->tel_list : [],
            'contact' => $c->isExist() ? $c->contact : []
        ];
    }

    protected function getLeadCustomerInfo($client_id, $customer_id)
    {
        $c = \common\library\lead\Helper::getCustomer($client_id, $customer_id);
        return  [
            'customer_id' => $c->isExist() ? $c->customer_id : $customer_id,
            'name' => $c->isExist() ? $c->name : '',
            'email' => $c->isExist() ? $c->email : '',
            'tel_list' => $c->isExist() ? $c->tel_list : [],
            'contact' => $c->isExist() ? $c->contact : []
        ];
    }

    protected function getLeadInfo($client_id, $lead_id)
    {
        $lead = new Lead($client_id);
        $lead->loadById($lead_id);
        return  [
            'lead_id' => $lead->lead_id ?: $lead_id,
            'name' => $lead->name ?: '',
        ];
    }

    protected function getCompanyInfo($client_id, $company_id)
    {
        $company = new Company($client_id, $company_id);

        return  [
            'company_id' => $company_id,
            'name' => $company->name ?? '',
        ];
    }

    public function format($item)
    {

        $item['company_info'] = null;
        $item['create_user_info'] = null;
        $item['customer_info'] = null;
        $item['lead_customer_info'] = null;
        $item['lead_info'] = null;
        $item['_id'] = $item['trail_id'];
        $item['status'] = 0;
        $item['comment_list'] = [];
        $item['mail_map'] = [];
        $item['plan_map'] = [];

        if (!$this->simpleData || $this->showCreateUserInfo) {
            $item['create_user_info'] = $this->getMapData('user', $item['create_user']);
        }

        if ($this->simpleData == false) {

            if (!empty($item['customer_id'])) {
                $customer_id = PgsqlUtil::trimArray($item['customer_id']);
                if (!is_array($customer_id)) {
                    if ($customer_id !== 0) {
                        $customer_id = [$customer_id];
                    } else {
                        $customer_id = [];
                    }
                }

                //处理$item['customer_id'] = [0] 的情况
                $customer_id = array_diff($customer_id, [0]);

                foreach ($customer_id as $value) {
                    $item['customer_info'][] = $this->getCustomerInfo($item['client_id'], $value);
                }
            }

            if (!empty($item['lead_customer_id'])) {
                $lead_customer_id = PgsqlUtil::trimArray($item['lead_customer_id']);
                if (!is_array($lead_customer_id)) {
                    if ($lead_customer_id !== 0) {
                        $lead_customer_id = [$lead_customer_id];
                    } else {
                        $lead_customer_id = [];
                    }
                }

                //处理$item['customer_id'] = [0] 的情况
                $lead_customer_id = array_diff($lead_customer_id, [0]);

                foreach ($lead_customer_id as $value) {
                    $item['lead_customer_info'][] = $this->getLeadCustomerInfo($item['client_id'], $value);
                }
            }

            if ($this->showLeadInfo && !empty($item['lead_id'])) {
                $item['lead_info'] = $this->getLeadInfo($item['client_id'], $item['lead_id']);
            }

            if (!empty($item['company_id'])) {
                $item['company_info'] = $this->getCompanyInfo($item['client_id'], $item['company_id']);
            }
        } else {
            if ($this->showCustomerInfo) {
                if ($customerInfo = $this->getMapData('customer_info', $item['trail_id'])) {
                    $item['customer_info'] = [$customerInfo];
                }
                if ($leadCustomerInfo = $this->getMapData('lead_customer_info', $item['trail_id'])) {
                    $item['lead_customer_info'] = [$leadCustomerInfo];
                }
            }
        }

        $item['node_type'] = $item['type'] = intval($item['type']);
        $item['module'] = floor($item['type'] / 100);
        $type = $item['type'];

        $customer_id = PgsqlUtil::trimArray($item['customer_id']);
        $item['customer_id'] = count($customer_id) ? $customer_id : [0];

        $lead_customer_id = PgsqlUtil::trimArray($item['lead_customer_id']);
        $item['lead_customer_id'] = count($lead_customer_id) ? $lead_customer_id : [0];

        if (array_key_exists($type, static::$map)) {
            $function = static::$map[$type];
            $item = call_user_func(array($this, $function), $item);
        } else {
            $item['data'] = $this->getData($item['client_id'], $item['trail_id']);
        }

        if ($this->showNodeTypeActionName && !isset($item['node_type_name'])) {
            $item['node_type_name'] = $this->productNodeTypeNode($type, $item);
        }

        if ($this->showNodeTypeName && !isset($item['node_type_name'])) {
            $item['node_type_name'] = \Yii::t('trail', (static::$nodeTypeName[$type] ?? ''));
        }

        if ($this->showOpportunityInfo && isset($item['opportunity_id'])) {
            $item['opportunity_info'] = $this->getMapData('opportunity', $item['opportunity_id']);
        }

        if ($this->showCaptureCard && $captureCard = $this->getMapData('captureCard', $item['trail_id']) ?? null) {
            $item['capture_card'] = $captureCard;
        }

        if ($this->showInvalidInquiryFlag) {
            $data = $this->getData($item['client_id'], $item['trail_id']);
            $item['data']['invalid_inquiry'] = intval($data['invalid_inquiry'] ?? 0);
            $item['data']['invalid_inquiry_reason'] = $data['invalid_inquiry_reason'] ?? '';
        }

        if($this->showAdjustEmailDynamic){

            //用户关联商机
            if($item['opportunity_id']){
                $item['opportunity_dynamic_info']['switch'] = 1; //已管理商机
                //是商机主跟进人，或者拥有商机编辑权限
                if(isset($item['opportunity_info']['main_user_info']['user_id']) && $item['opportunity_info']['main_user_info']['user_id'] == $this->operatorUser->getUserId()
                ||  \common\library\opportunity\Helper::checkOpportunityEditPrivilege($this->operatorUser->getClientId(), $this->operatorUser->getUserId(), $item['opportunity_info']['handler_info'],$item['opportunity_info']['user_id'])){

                    $item['opportunity_dynamic_info']['permissions'] = true; //可以解绑关联商机
                }else{
                    $item['opportunity_dynamic_info']['permissions'] = false;//无权限解绑关联商机
                }
            }else{
                $item['opportunity_dynamic_info'] = ['switch' => 0,'permissions' => true]; //没有关联商机，提示可以关联
            }
        }

		if ($this->showCommentList) {

		    $item['comment_list'] = $this->mapData['comment_map'][$item['trail_id']] ?? [];
	    }

        if ($this->showMarketingAutomationPlan) {
            if (in_array($type, [TrailConstants::TYPE_MAIL_SEND,TrailConstants::TYPE_MAIL_RECEIVER])) {
                $referId = $this->mapData['mail_map'][$item['refer_id']] ?? 0;
            } elseif (in_array($type, [TrailConstants::TYPE_EDM_SEND])) {
                $referId = $item['refer_id'] ?? 0;
            } else {
                $referId = 0;
            }
            $planItem = $this->mapData['plan_map'][$referId] ?? [];
            $item['plan_id'] = $planItem['plan_id'] ?? 0;
            $item['plan_name'] = $planItem['plan_name'] ?? 0;
        }


        if (!empty($item['data']['source']['seller_account_id'])) {
            $sellerAccountId = $item['data']['source']['seller_account_id'];
            $aliStoreAccountModel = \AlibabaStoreAccount::model();
            $aliAccount = $aliStoreAccountModel->find('client_id = :cid and seller_account_id = :sid', [
                ':cid' => $item['client_id'],
                ':sid' => $sellerAccountId,
            ]);
            if ($aliAccount && $aliAccount->store_id) {
                $adminAccount = \Yii::app()->db->createCommand()
                    ->select('seller_account_id')
                    ->from('tbl_alibaba_account')
                    ->where('client_id=:client_id AND store_id=:store_id AND is_admin=1', [
                        ':client_id' => $item['client_id'],
                        ':store_id' => $aliAccount->store_id
                    ])
                    ->queryRow();
                if ($adminAccount) {
                    $item['data']['source']['admin_seller_account_id'] = $adminAccount['seller_account_id'];
                }
            }
        }

        if (!empty($item['data']['seller_account_id'])) {
            $sellerAccountId = $item['data']['seller_account_id'];

            $aliStoreAccountModel = \AlibabaStoreAccount::model();
            $aliAccount = $aliStoreAccountModel->find('client_id = :cid and seller_account_id = :sid', [
                ':cid' => $item['client_id'],
                ':sid' => $sellerAccountId,
            ]);

            if ($aliAccount && $aliAccount->store_id) {
                $adminAccount = \Yii::app()->db->createCommand()
                    ->select('seller_account_id')
                    ->from('tbl_alibaba_account')
                    ->where('client_id=:client_id AND store_id=:store_id AND is_admin=1', [
                        ':client_id' => $item['client_id'],
                        ':store_id' => $aliAccount->store_id
                    ])
                    ->queryRow();

                if ($adminAccount) {
                    $item['data']['admin_seller_account_id'] = $adminAccount['seller_account_id'];
                }
            }
        }


        //如果是app，data为空的时候，需要设置格式为null.module 还有node_type 也需要设置为0
        if($this->emptyDataDefaultValue && (!isset($item['data']) || !$item['data'])){
            $item['data'] = null;
            $item['module'] = 0;
            $item['node_type'] = 0;
        }
        return $item;
    }

    protected function productNodeTypeNode($type, $item, array $email = [])
    {
        if (!empty($item['customer_info'])) {
            if (!isset($item['customer_info']['name'])) {
                $name = [];

                foreach ($item['customer_info'] as $oneInfo) {
                    if ($oneInfo['customer_id'] && (empty($email) || in_array($oneInfo['email'], $email))) {
                        $name[] = $oneInfo['name'] ? $oneInfo['name'] : $oneInfo['email'];
                    }
                }

                $name = implode('、', $name);
            } else {
                $name = $item['customer_info']['name'] ? $item['customer_info']['name']
                    : $item['customer_info']['email'];
            }
        } else {
            $name = $item['data']['company_name'] ?? ($item['data']['customer_name'] ?? '');
        }

        $name = $name != '' ? $name : \Yii::t('trail', '客户');
        $nodeTypeAction = str_replace('{$name}', $name, \Yii::t('trail', static::$nodeTypeActionMap[$type] ?? ''));
        $nodeTypeName = $nodeTypeAction . \Yii::t('trail', (static::$nodeTypeName[$type] ?? '') );
        if (in_array($type, $this->getFollowUpList())) {
            $nodeTypeName .= \Yii::t('trail', '跟进');
        }

        return $nodeTypeName;
    }

    protected function formatCustomer($item)
    {
        $data = $this->getData($item['client_id'], $item['trail_id']);

        if (!isset($data['company_id'])) {
            $data['company_id'] = $data['company_ids'] ?? 0;

            if (is_array($data['company_id'])) {
                $data['company_id'] = $data['company_id'][0];
            }
        }

        $item['data'] = $data;
        return $item;
    }

    protected function formatBusinessCard($item)
    {
        if (!empty($item['refer_id']))
        {
            try {
                if (!$this->simpleData) {
                    //加载名片夹图片
                    $businessCard = new BusinessCard($item['client_id'], $item['refer_id']);
                    $businessCardInfo = $businessCard->getAttributes();

                    $item['data']['file_list'] = [];
                    if(!empty($businessCardInfo['customer_image_list'])) {
                        $objArray = \UploadFile::findByIds($businessCardInfo['customer_image_list']);

                        foreach ($objArray as $obj) {
                            $upload = new \AliyunUpload();
                            $upload->loadByObject($obj);
                            $item['data']['file_list'][] = array(
                                'url' => $upload->getFileUrl(),
                                'name' => $upload->getFileName(),
                                'size' => $upload->getFileSize(),
                                'id' => $obj->file_id,
                                'preview_url' => $upload->getPreview(),
                                'download_url' => $upload->generatePresignedUrl(),
                            );
                        }
                    }
                }
            } catch (\RuntimeException $e) {
                \LogUtil::info($item['refer_id'].$e->getMessage());
                $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
            }
            
        }

        return $item;
    }

    protected function formatMail($item)
    {
        $item['data'] = [];
        $item['delete_flag'] = TrailConstants::DISABLE_FLAG;

        if (!empty($item['refer_id'])) {
            try {
                if (!$this->simpleData) {
                    // todo Tony 兼容代码，PDCA上线要替换
                    $followup = \Followup::model()->find('follow_up_id=:follow_up_id AND client_id=:client_id AND enable_flag=:enable_flag', [':follow_up_id' => $item['refer_id'], ':client_id' => $item['client_id'], ':enable_flag' => 1]);
                    $item['refer_id'] = $followup && !empty($followup->refer_id) ? $followup->refer_id : $item['refer_id'];

                    $mail = new Mail($item['refer_id']);

                    $mail->getFormatter()->trailInfoSetting();
                    if ($this->showCaptureCard) {
                        $mail->getFormatter()->setShowCaptureCard(true);
                    }
                    $item['data'] = $mail->getAttributes();
                    $mailUserId = $mail->getUserId();
                    $item['delete_flag'] = $mail->folder_id == \Mail::FOLDER_TRASH_ID ? 1 : $mail->delete_flag;
                } else {
                    $mailData = $this->getMapData('formatMail', $item['refer_id']);
                    $item['data'] = ['subject' => $mailData['subject'] ?? ''];
                    $mailUserId = $mailData['user_id'] ?? 0;
                    $item['delete_flag'] = ($mailData['folder_id'] ?? \Mail::FOLDER_TRASH_ID) == \Mail::FOLDER_TRASH_ID ? 1 : $mailData['delete_flag'];
                }
                $item['data']['is_yours'] = false;
                if ($this->operatorUser != null && $this->operatorUser->hasInfo()) {
                    $item['data']['is_yours'] = $this->operatorUser->getUserId() == $mailUserId;
                }

                if (isset($this->highlightData[$item['refer_id']])) {
                    $item['data']['highlight'] = $this->highlightData[$item['refer_id']];
                }

                $item['data']['remark_data'] =  $this->getData($item['client_id'], $item['trail_id']) ?? null;

                if(isset($item['data']['remark_data'])) {
                    $item['data']['remark_data']['file_list'] = [];
                    if (!empty($item['data']['remark_data']['file_ids'])) {

                        $objArray = \UploadFile::findByIds($item['data']['remark_data']['file_ids']);

                        foreach ($objArray as $obj) {
                            $upload = new \AliyunUpload();
                            $upload->loadByObject($obj);

                            $item['data']['remark_data']['file_list'][] = array(
                                'url' => $upload->getFileUrl(),
                                'name' => $upload->getFileName(),
                                'size' => $upload->getFileSize(),
                                'id' => $obj->file_id,
                                'preview_url' => $upload->getPreview(),
                                'download_url' => $upload->generatePresignedUrl(),
                            );
                        }
                    }
                }

            } catch (\RuntimeException $e) {
                \LogUtil::info($item['refer_id'].$e->getMessage());
                $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
            }
        }

        return $item;
    }

    protected function formatSchedule($item)
    {
        $item['delete_flag'] = 0;
        if (! empty($item['refer_id'])) {
            $referId = $item['refer_id'];
            if (!$this->simpleData) {
                // todo Tony 兼容代码，PDCA上线要替换
                $followup = \Followup::model()->find('follow_up_id=:follow_up_id AND client_id=:client_id AND enable_flag=:enable_flag', [':follow_up_id' => $item['refer_id'], ':client_id' => $item['client_id'], ':enable_flag' => 1]);
                $item['refer_id'] = $followup && !empty($followup->refer_id) ? $followup->refer_id : $item['refer_id'];
            } else {
                $item['refer_id'] = $this->getMapData('followup', $item['refer_id']) ?: $item['refer_id'];
            }

            $trailData = $this->getData($item['client_id'], $item['trail_id']);
            try {
                $schedule = new Schedule($item['client_id'], $item['refer_id']);
                $schedule->setOperator($this->operatorUser->getUserId());
                $schedule->getFormatter()->ListInfoSetting();
                $schedule = $schedule->getAttributes();
                $schedule['remark'] = $trailData['remark'] ?? '';
            } catch (\Throwable $exception) {
                $schedule = [
                    'enable_flag' => Schedule::ENABLE_FLAG_FALSE
                ];
            }
            $item['data'] = $schedule;
            $item['delete_flag'] = $item['data']['enable_flag'] == Schedule::ENABLE_FLAG_TRUE ? 0 : 1;

            if (isset($this->highlightData[$referId])) {
                $item['data']['highlight'] = $this->highlightData[$referId];
            }
        }
        return $item;
    }

    protected function formatQuotation($item)
    {
        $quotationId = $item['refer_id'];
        $createUserId = empty($item['create_user']) ? $this->operatorUser->getUserId() : $item['create_user'];

        if (!$this->simpleData) {
            try {
                $model = new Quotation($createUserId, $quotationId);
            } catch (\Exception $e){
                $model = null;
            }

            $trailData = $this->getData($item['client_id'], $item['trail_id']);

            try {
                $clientId = empty($model->client_id) ? $this->operatorUser->getClientId() : $model->client_id;
                $statusName = $model ?
                    (new InvoiceStatusService($clientId, \Constants::TYPE_QUOTATION))
                        ->getName($trailData['status'] ?? 0)
                    : '';
                $oldStatusName = $model ?
                    (new InvoiceStatusService($clientId, \Constants::TYPE_QUOTATION))
                        ->getName($trailData['old_status'] ?? 0)
                    : '';

            } catch (\Exception $e) {
                $statusName = $statusName ?? '';
                $oldStatusName = $oldStatusName ?? '';
            }

            $trailData['quotation_id'] = isset($trailData['_id']) ? $trailData['_id'] : $trailData['quotation_id'];

            $item['data'] = [
                'quotation_id' => $trailData['quotation_id'] ?? 0,
                'quotation_no' => $trailData['quotation_no'] ?? '',
                'quotation_name' => $trailData['name'] ?? '',
                'quotation_amount' => $trailData['amount'] ?? '',
                'currency' => $trailData['currency'] ?? '',
                'old_status' => $oldStatusName ?? '',
                'status' => $statusName,
                'customer_id' => $trailData['customer_id'] ?? 0,
                'company_id' => $trailData['company_id'] ?? 0,
                'product_total_count' => count(is_string($trailData['product_list'] ?? '{}') ? json_decode($trailData['product_list'] ?? '{}', true) : $trailData['product_list']),
                'create_time' => $trailData['create_time'] ?? '',
                'update_time' => $trailData['update_time'] ?? '',
                'delete_flag' => $trailData['delete_flag'] ?? ''
            ];

            $item['data']['app_view_url'] = \Yii::app()->params['host']['app_url'] . '/app/page/quotationView?id=' . $quotationId;

            if ($model) {
                $item['data']['quotation_name'] = $model->name;
                $item['data']['total_price'] = round($model->amount, 4);
                $item['data']['currency'] = $model->currency;

                //历史问题，为app兼容保证这两数据存在
                $productList = $model->product_list ?? [];
                $productList = array_fill(0, count($productList), 1);
                $item['data']['product_field'] = $productList;
                $item['data']['product_data'] =  $productList;
            } else {
                $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
            }

            return $item;
        }

        $item['data'] = [];


        if ($name = $this->getMapData('formatQuotation', $quotationId)) {
            $item['data'] = ['quotation_id' => $quotationId, 'quotation_name' => $name];
        } else {
            $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
        }

        return $item;
    }

    protected function formatPi($item)
    {
        $piId = $item['refer_id'];

        if (!$this->simpleData) {
            $trailData = $this->getData($item['client_id'], $item['trail_id']);
            $item['data']['app_view_url'] = \Yii::app()->params['host']['app_url'] . '/app/page/piView?id=' . $piId;
            $item['data']['pi_name'] = $trailData['pi_name'] ?? '';
            $item['data']['total_price'] = $trailData['total_price'] ?? 0;
            $item['data']['currency'] = $trailData['currency'] ?? '';

            //历史问题，为app兼容保证这两数据存在
            $productList = json_decode(
                $item['data']['product_data'] ?? ($item['data']['product_field'] ?? '[]'),
                true
            );

            $productList = array_fill(0, count($productList), 1);
            $item['data']['product_data'] = $productList;
            $item['data']['product_field'] = $productList;
            return $item;
        }

        $item['data'] = ['pi_id' => $piId, 'pi_name' => ''];
        return $item;
    }

    protected function formatOrder($item)
    {
        $orderId = $item['refer_id'];
        $createUserId = empty($item['create_user']) ? $this->operatorUser->getUserId() : $item['create_user'];


        if (!$this->simpleData) {
            try {
                $model = new Order($createUserId, $orderId);
            } catch (\Exception $e) {
                $model = null;
            }

            $trailData = $this->getData($item['client_id'], $item['trail_id']);

            try {
                $clientId = empty($model->client_id)?$this->operatorUser->getClientId():$model->client_id;
                $statusName = $model ?
                    (new InvoiceStatusService($clientId, \Constants::TYPE_ORDER))
                        ->getName($trailData['status'] ?? 0)
                    : '';
                $oldStatusName = $model ?
                    (new InvoiceStatusService($clientId, \Constants::TYPE_ORDER))
                        ->getName($trailData['old_status'] ?? 0)
                    : '';
            } catch (\Exception $e) {
                $statusName = $statusName ?? '';
                $oldStatusName = $oldStatusName ?? '';
            }

            $trailData['order_id'] = $trailData['_id'] ?? $trailData['order_id'] ?? 0;

            $item['data'] = [
                'order_id' => $trailData['order_id'],
                'order_no' => $trailData['order_no'] ?? '',
                'order_name' => $trailData['name'] ?? '',
                'order_amount' => $trailData['amount'] ?? '',
                'currency' => $trailData['currency'] ?? '',
                'old_status' => $oldStatusName ?? '',
                'status' => $statusName,
                'customer_id' => $trailData['customer_id'] ?? 0,
                'company_id' => $trailData['company_id'] ?? 0,
                'product_total_count' => count(is_string($trailData['product_list'] ?? '{}') ? json_decode($trailData['product_list'] ?? '{}', true) : $trailData['product_list']),
                'create_time' => $trailData['create_time'] ?? '',
                'update_time' => $trailData['update_time'] ?? '',
                'ali_status_name' => \Yii::t('invoice', $trailData['ali_status_name'] ?? ''),
                'delete_flag' => $trailData['delete_flag'] ?? ''
            ];

            $item['data']['app_view_url'] = \Yii::app()->params['host']['app_url'].'/app/page/orderView?id='.$orderId;

            if ($model) {
                $item['data']['order_name'] = $model->name;
                $item['data']['order_no'] = $model->order_no;
                $item['data']['total_price'] = round($model->amount, 10);
                $item['data']['currency'] = $model->currency;
                $item['data']['source_type'] = $model->source_type;

                //历史问题，为app兼容保证这两数据存在
                $productList = $model->product_list ?? [];
                $productList = array_fill(0, count($productList), 1);
                $item['data']['product_field'] = $productList;
                $item['data']['product_data'] =  $productList;
            } else {
                $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
            }

            return $item;
        }

        $item['data'] = [];
        if ($orderName = $this->getMapData('formatOrder', $orderId)) {
            $item['data'] = ['order_id' => $orderId, 'order_name' => $orderName];
        } else {
            $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
        }

        return $item;
    }

    protected function formatEdm($item)
    {
        $item['data'] = [];
        $taskId = $item['refer_id'];

        $customerId = $item['customer_id'][0] ?? 0;
        $leadCustomerId = $item['lead_customer_id'][0] ?? 0;

        if (!empty($customerId) || !empty($leadCustomerId)) {
            if ($this->simpleData) {
                $item['data'] = array_merge($item['data'], $this->getMapData('formatEdmMail', $taskId) ?: []);
            } else {
                try {
                    $customer = !empty($customerId) ? (new Customer($item['client_id'], $customerId)) : (new LeadCustomer($item['client_id'], $leadCustomerId));
                    $email = $customer->email;

                    if (!empty($email)) {
                        $model = \GroupMail::model()->find(
                            'task_id=:t and send_to=:s',
                            [':t' => $taskId, ':s' => $email]
                        );
                        $item['data'] = array_merge($item['data'], $model ? $model->getAttributes() : []);
                    }
                } catch (\ProcessException $e) {
                    $item['data'] = [];
                }
            }
        }

        if (empty($item['data']) && $task = $this->getMapData('formatEdm', $taskId)) {
            $item['data'] = [
                'status' => $task['status'] ?? '',
                'view_count' => $task['view_count'] ?? 0,
                'subject' => isset($task['mail_subject']) && $task['mail_subject'] ?
                    str_replace(\Constants::MAIL_SPLIT_SUBJECT, " ", $task['mail_subject']) : '',
                'create_time' => $task->create_time ?? '',
            ];
        }

        return $item;
    }


    protected function formatAlibabaTrade($item)
    {
        $item['data'] =  $this->getData($item['client_id'], $item['trail_id']);
        if(isset($item['data']['buyer_account_id']) && $item['data']['buyer_account_id'] &&
            isset($item['data']['seller_account_id']) && $item['data']['seller_account_id']
            && isset($item['data']['buyer_account_encrypt']) && $item['data']['buyer_account_encrypt']){

            $item['data']['url'] =  \common\library\alibaba\Constant::ICBU_SERVICE_DOMAIN.\common\library\alibaba\Constant::ALI_PWD_URL
                .'?activeAccountId='.$item['data']['buyer_account_id'].'&relevanceAccountId='.
                $item['data']['seller_account_id'].'&activeAccountIdEncrypt='.$item['data']['buyer_account_encrypt'].'&from=xiaoman';

        }
		$item['data']['store_name'] = $item['data']['store_name'] ?? '';
		$item['data']['store_alias'] = $item['data']['store_alias'] ?? '';
		if (!$this->simpleData) {
			$alibabaStoreModel = \AlibabaStore::model();
			$alibabaStore = $alibabaStoreModel->find('client_id = :cid and seller_account_id = :sid', [
				':cid' => $item['client_id'],
				':sid' => $item['data']['seller_account_id'],
			]);
			$item['data']['store_alias'] = $alibabaStore->store_alias ?? '';
		} else if (empty($item['data']['store_alias']) && $aliAccount = $this->getMapData('formatAlibabaTradeStore', $item['data']['seller_account_id'] ?? '')) {
			$item['data']['store_alias'] = $aliAccount['store_alias'] ?? '';
		}
        $item['data']['content'] = str_replace(['买家', '给', '发来了一条询盘', '发送了一个询盘', '回复了', '的询盘', '发送了一个营销询盘'], [\Yii::t('trail', '买家'), \Yii::t('trail', '给'), \Yii::t('trail', '发来了一条询盘'), \Yii::t('trail', '发送了一个询盘'), \Yii::t('trail', '回复了'), \Yii::t('trail', '的询盘'), \Yii::t('trail', '发送了一个营销询盘')], $item['data']['content'] ?? '');
        return $item;
    }
    protected function formatRemark($item)
    {
        $item['data'] =  $this->getData($item['client_id'], $item['trail_id']);

        if ($item['client_id'] != \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT) {
            if (!$this->simpleData) {
                $followup = \Followup::model()->find('follow_up_id=:follow_up_id AND client_id=:client_id AND enable_flag=:enable_flag', [':follow_up_id' => $item['refer_id'], ':client_id' => $item['client_id'], ':enable_flag' => 1]);
                $item['refer_id'] = $followup && !empty($followup->refer_id) ? $followup->refer_id : $item['refer_id'];
            } else {
                $item['refer_id'] = $this->getMapData('followup', $item['refer_id']) ?: $item['refer_id'];
            }
        }

        if ($item['data']) {
            $item['data']['plain_content'] = \Util::plainText($item['data']['content']);

            if (isset($this->highlightData[$item['refer_id']])) {
                $item['data']['highlight'] = $this->highlightData[$item['refer_id']];
            }

            if ($this->simpleData) {
                return $item;
            }

            $item['data']['file_list'] = [];
            $item['data']['ali_account'] = '';
            if (isset($item['data']['source']) && ($item['data']['source']['type'] ?? 0) == TrailConstants::SOURCE_TYPE_TM) {
                $item['data']['ali_account'] = $item['data']['source']['seller_name'] . '<' . $item['data']['source']['seller_email'] . '>';
                $sourceTip = "来源于国际站同步：{$item['data']['source']['seller_name']}<{$item['data']['source']['seller_email']}>";
                if (isset($item['create_user_info']['name'])) {
                    $item['create_user_info']['name'] = "{$item['create_user_info']['name']}（${sourceTip}）";
                } else {
                    $item['create_user_info']['name'] = $sourceTip;
                }
            }

            if (isset($item['data']['buyer_account_id']) && $item['data']['buyer_account_id'] &&
                isset($item['data']['seller_account_id']) && $item['data']['seller_account_id']
                && isset($item['data']['buyer_account_encrypt']) && $item['data']['buyer_account_encrypt']) {

                $item['data']['url'] = \common\library\alibaba\Constant::ICBU_SERVICE_DOMAIN . \common\library\alibaba\Constant::ALI_PWD_URL
                    . '?activeAccountId=' . $item['data']['buyer_account_id'] . '&relevanceAccountId=' .
                    $item['data']['seller_account_id'] . '&activeAccountIdEncrypt=' . $item['data']['buyer_account_encrypt'] . '&from=xiaoman';

            }
            if (!empty($item['data']['file_ids'])) {
                $objArray = \UploadFile::findByIds($item['data']['file_ids']);

                foreach ($objArray as $obj) {
                    $upload = new \AliyunUpload();
                    $upload->loadByObject($obj);

                    $item['data']['file_list'][] = array(
                        'url' => $upload->getFileUrl(),
                        'name' => $upload->getFileName(),
                        'size' => $upload->getFileSize(),
                        'id' => $obj->file_id,
                        'preview_url' => $upload->getPreview(),
                        'download_url' => $upload->generatePresignedUrl(),
                    );
                }
            }

            if (!empty($item['data']['at_list']))
            {
                foreach ($item['data']['at_list'] as $key => $datum) {
                    if ($key == 'at_users')
                    {
                        $item['data']['at_list']['at_users'] = array_map(function ($userId) {
                            return ['nickname' => trim($this->getMapData('user_info', $userId)['nickname'] ?? ''), 'user_id' => $userId];
                        }, $datum);
                    }
                }
            }

            if($item['node_type'] == TrailConstants::TYPE_REMARK_TEL) {
                $item['data']['content'] = str_replace(['发起了语音/视频通话'], [\Yii::t('trail', '发起了语音/视频通话'), \Yii::t('trail', '给')], $item['data']['content'] ?? '');
            }

        }

        return $item;
    }

    protected function formatPayPhone($item)
    {
        $payPhoneTask = \PayPhoneTask::findByTaskId($item['create_user'], $item['refer_id']);

        if ($payPhoneTask) {
            $item['data']['call_start_time'] = $payPhoneTask->call_start_time;
            $item['data']['call_end_time'] = $payPhoneTask->call_end_time;
            $item['data']['price'] = $payPhoneTask->price / 100;
            $item['data']['target_tel'] = $payPhoneTask->target_tel;
            $item['data']['seconds'] = $payPhoneTask->seconds;
            $item['data']['call_id'] = $payPhoneTask->call_id;
            $item['data']['role_type'] = (string)$payPhoneTask->role_type;
            $item['data']['record_url'] = $payPhoneTask->record_url;
            $item['data']['file_ext'] = '.mp3';
            $item['data']['call_outs_in'] = (string)$payPhoneTask->call_outs_in;
            $item['data']['type'] = (string)$payPhoneTask->type;
        } else {
            $item['data'] = [];
            $item['delete_flag'] = TrailConstants::ENABLE_FLAG;
        }

        return $item;
    }

    protected function formatMarketingEdm($item)
    {
        $item['data'] = $this->getData($item['client_id'], $item['trail_id']);

        if (empty($item['data']['subject'])) {
            $item['data']['subject'] = '通过小满营销发送了一封邮件';
        }

        return $item;
    }

    public function formatOpportunity($item)
    {
        $item['data'] =  $this->getData($item['client_id'], $item['trail_id']);
        $item['data']['account_date'] = $item['data']['account_date'] ?? '';
        $item['data']['content'] = str_replace(['商机销售阶段从', '变更为'], [\Yii::t('trail', '商机销售阶段从'), \Yii::t('trail', '变更为')], \Yii::t('trail', $item['data']['content'] ?? ''));
        $item['data']['stage'] = \Yii::t('field', $item['data']['stage'] ?? '');

        return $item;
    }

    public function formatSiteTrack($item)
    {
        $item['data'] =  $this->getData($item['client_id'], $item['trail_id']);
        $item = $this->formatAmesSiteTrack($item);

        $title = $item['data']['title'] ?? '';
        if ($title === '') {
            $title = $item['data']['url'] ?? '';
            mb_strlen($title) > 15 && $title = mb_substr($title, 0, 15) . '...';
        }

        if( $this->showNodeTypeActionName )
        {
            $product = '';
            if (!empty($item['data']['products'])) {
                $product = implode(\Yii::t('trail', '、'), array_column($item['data']['products'], 'product_name'));
                $product = \Yii::t('trail', '浏览了产品：{product_name}，', ['{product_name}' => $product]);
            }

            $senderType = current($item['data']['message_list'] ?? [])['senderType'] ?? MessageList::SENDER_TYPE_OF_VISITOR;
            $senderTitle = $senderType == MessageList::SENDER_TYPE_OF_STAFF ? '客服 ' : '';
            $nodeTypeAction = str_replace(['{$create_time}', '{$title}', '{$keyword}','{$count}', '{$nickname}', '{$senderTitle}', '{$product}', '{$site_name}', '{$file_name}'],[$item['create_time'],$title, $item['data']['keyword']??'', $item['data']['count']??'', $item['data']['nickname'] ?? '', $senderTitle, $product, $item['data']['shop_name'] ?? '',$item['data']['download_data_file_name'] ?? ''], \Yii::t('trail', static::$nodeTypeActionMap[$item['type']]));
            $item['node_type_name'] = $nodeTypeAction;
        }

        // 没有线索模块，需要从询盘提交的表单中获取到对应email
        if(empty($item['lead_customer_info']) && !empty($item['inquiry_id'])) {
            $item['lead_customer_info'][] = $this->getInquiryCustomerInfo($item['client_id'], $item['inquiry_id']);
        }

        if (!empty($item['data']['message_list'])) {
            $messageList = new MessageList($item['client_id'], current($item['data']['message_list'])['conversationId']);
            foreach ($item['data']['message_list'] as &$message) {
                $message = $messageList->format($message);
            }
        }
        // 映射表单名称
        if (isset($item['data']['extract_data']) && !empty($item['data']['extract_data'])) {
            // 表单的公司规模字段是选项，提交值是key，需要格式化
            if (isset($item['data']['extract_data']['Company Size'])) {
                $item['data']['extract_data']['Company Size'] = \common\library\lead\Lead::SCALE_MAP[$item['data']['extract_data']['Company Size']]??$item['data']['extract_data']['Company Size'];
            }
            if (!empty($item['lead_id'])){
                $lead = new \common\library\lead\Lead($item['client_id'], $item['lead_id']);
                !empty($lead->origin_list ?? []) && $leadOrigin = [$item['lead_id'] => current($lead->origin_list)];
            }
            $inquiry = $this->getMapData('inquiry', (!empty($item['inquiry_id']) ? $item['inquiry_id'] : $item['lead_id']));
            $ipCountry = !empty($inquiry['guest_country']) ? $inquiry['guest_country'] : '';
            $ipCountry = strtolower($ipCountry) == 'zz' ? '(Unknown)' : $ipCountry;

            if (isset($leadOrigin[$item['lead_id']])){
                switch ($leadOrigin[$item['lead_id']]){
                    case Origin::SYS_ORIGIN_FACEBOOK_LEAD:
                        $facebookLead = (new FacebookFormLead($item['client_id']))->loadByLeadIdField($item['lead_id'] ?? 0)->getAttributes(['facebook_ad_id', 'facebook_ad_name', 'facebook_page_id', 'field_data', 'facebook_page_name']);
                        $pageInfo = (new FacebookPage())->loadByPageId($facebookLead['facebook_page_id'] ?? 0)->getAttributes(['page_name']);
                        $item['data']['extract_data']['AD ID'] = strval($facebookLead['facebook_ad_id'] ?? ''); //ad id, NOTE: 转string，避免整型过大前端溢出
                        $item['data']['extract_data']['AD Name'] = $facebookLead['facebook_ad_name'] ?? ''; //ad name
                        $item['data']['extract_data']['Page Name'] = $pageInfo['page_name'] ?? ''; //ad name
                        if (!empty($facebookForm['field_data'])){
                            foreach ($facebookForm['field_data'] as $question){
                                $item['data']['extract_data'][$question['question']] = $question['answer'] ?? '';
                            }
                        }
                        break;
                    case Origin::SYS_ORIGIN_ID_OKKI_SHOP:
                        $item['data']['extract_data']['Inquiry NO'] = !empty($item['inquiry_id']) ? $item['inquiry_id'] : $item['lead_id']; //询盘编号
                        $item['data']['extract_data']['Page Title'] = $inquiry['page_title'] ?? ''; //来源页面标题
                        $item['data']['extract_data']['Page URL'] = empty($inquiry['page_link']) || '--'==$inquiry['page_link'] ? [] : ['text'=>$inquiry['page_title'] ?? '', 'url'=>$inquiry['page_link'] ?? '']; //来源页面
                        $item['data']['extract_data']['IP'] = $inquiry['guest_ip'] ?? ''; //访客IP(国家)
                        $item['data']['extract_data']['Platform'] = $inquiry['guest_agent'] ?? ''; //访客设备
                        break;
//                    case Origin::SYS_ORIGIN_WEBSITE:
//                        $item['data']['extract_data']['Inquiry NO'] = $inquiry['trail_id'] ?? ''; //询盘编号
//                        $item['data']['extract_data']['Refer URL'] = ['text'=>$inquiry['refer_url'] ?? '', 'url'=>$inquiry['refer_url'] ?? '']; //提交网站
//                        $item['data']['extract_data']['Web Site'] = ['text' => $inquiry['web_site_name'] ?? '', 'url' => $inquiry['web_site_url'] ?? '']; //提交页面
//                        $item['data']['extract_data']['Submit Entry'] = InquiryService::INQUIRY_TYPE_MAP[$inquiry['type'] ?? 0] ?? ''; //提交入口
//                        $item['data']['extract_data']['IP'] = $inquiry['guest_ip'] ?? ''; //访客IP(国家)
//                        $item['data']['extract_data']['Origin'] = InquiryService::INQUIRY_ORIGIN_TYPE_MAP[$inquiry['origin'] ?? 0] ?? ''; //访问来源
//                        $item['data']['extract_data']['Platform'] = $inquiry['guest_agent'] ?? ''; //访客设备
//                        break;
                }
            }
            $numericIds = array_filter(array_keys($item['data']['extract_data']), 'is_numeric');

            if (!empty($numericIds)) {
                $customFieldList = new FieldList($item['client_id']);
                $customFieldList->setId($numericIds);
                $customFieldList->setEnableFlag(null);
                $customFieldList->setFields(['id','name']);
                $list = $customFieldList->find();

                $map = array_column($list,'name', 'id');
                foreach ($item['data']['extract_data'] as $key => $val) {
                    if (is_numeric($key) && isset($map[$key])) {
                        $item['data']['extract_data'][$map[$key]] = $val;
                        unset($item['data']['extract_data'][$key]);
                    }

                }
                if (!empty($item['data']['from']) && Constants::SNS_CLIENT_FACEBOOK_PAGE_LEAD == $item['data']['from']) {
                    $tmpExtractData = [];
                    !empty($item['data']['extract_data']['姓名']) && $tmpExtractData['姓名'] = $item['data']['extract_data']['姓名'];
                    !empty($item['data']['extract_data']['邮箱']) && $tmpExtractData['邮箱'] = $item['data']['extract_data']['邮箱'];
                    !empty($item['data']['extract_data']['电话']) && $tmpExtractData['电话'] = $item['data']['extract_data']['电话'];
                    $item['data']['extract_data'] = $tmpExtractData + $item['data']['extract_data'];
                }
            }
        }
        return $item;
    }

    public function formatTrackChatConversation($item)
    {
        $item['data'] =  $this->getData($item['client_id'], $item['trail_id']);

        $title = $item['data']['title'] ?? '';
        if ($title === '') {
            $title = $item['data']['url'] ?? '';
            mb_strlen($title) > 15 && $title = mb_substr($title, 0, 15) . '...';
        }

        if( $this->showNodeTypeActionName )
        {
            $product = '';
            if (!empty($item['data']['products'])) {
                $product = implode(\Yii::t('trail', '、'), array_column($item['data']['products'], 'product_name'));
                $product = \Yii::t('trail', '浏览了产品：{product_name}，', ['{product_name}' => $product]);
            }

            $senderType = current($item['data']['message_list'] ?? [])['form'] ?? MessageList::FROM_VISITOR;
            $senderTitle = $senderType == MessageList::FROM_VISITOR_STAFF ? '客服 ' : '';
            $nodeTypeAction = str_replace(['{$create_time}', '{$title}', '{$keyword}','{$count}', '{$nickname}', '{$senderTitle}', '{$product}', '{$site_name}', '{$file_name}'],[$item['create_time'],$title, $item['data']['keyword']??'', $item['data']['count']??'', $item['data']['nickname'] ?? '', $senderTitle, $product, $item['data']['shop_name'] ?? '',$item['data']['download_data_file_name'] ?? ''], \Yii::t('trail', static::$nodeTypeActionMap[$item['type']]));
            $item['node_type_name'] = $nodeTypeAction;
        }

        foreach ($item['data']['message_list']??[] as $key => $message) {
            if(!empty($message['user_id'])){
                $item['data']['message_list'][$key]['user_info']['nickname'] = \User::getUserObject($message['user_id'])->getNickname();
            }

        }
        return $item;
    }

    public function formatAmesSiteTrack($item)
    {
        if (!in_array($item['type'],  \common\library\ames\AmesConstants::$amesSiteTrackMap)) {
            return $item;
        }

        if (!empty($item['data']['shop_id'])) {
            $shopInfo = $this->getShopInfo($item['client_id'], $item['data']['shop_id']);
            $item['data']['shop_name'] = $shopInfo['web_site_name'] ?? '';
            $item['data']['shop_url'] = $shopInfo['web_site_url'] ?? '';
        }

        foreach ($item['data']['products'] ?? [] as $k => $product) {
            $item['data']['products'][$k]['url'] = !empty($shopInfo['web_site_url']) ?
                sprintf('%s/products/%d', rtrim($shopInfo['web_site_url'], '/'), $product['product_id']) : '';
        }

        foreach ($item['data']['form_data'] ?? [] as $k => $v) {
            if ($v['key'] == 'countryselect') {
                $item['data']['form_data'][$k]['value'] = $this->getCountryByAlpha2($v['value']);
            }
        }

        //if ($item['type'] == TrailConstants::TYPE_SITE_TRACK_AMES_FORM || $item['type'] == TrailConstants::TYPE_SITE_TRACK_AMES_MKT_FORM) {
        if (!in_array($item['type'],  \common\library\ames\AmesConstants::$eventFormMap)) {
            $formData = array_column($item['data']['form_data'] ?? [], null, 'key');
            $extractData = [];
            foreach ($formData as $v) {
                $key = isset($v['desc']) && $v['desc'] !== '' ? $v['desc'] : ($v['key'] ?? '');
                $extractData[$key] = $v['value'] ?? '';
            }
            $item['data']['extract_data'] = $extractData;

        }

        return $item;
    }

    public function formatContactMessage($item)
    {
        $item['data'] = [];
        if (!$this->simpleData) {
            $userCustomerContact = \common\library\sns\customer\CustomerContactHelper::getTrailUserCustomerContact($item['client_id'], [$item['refer_id']], $this->showCompanyId);
            $item['data'] = $userCustomerContact[$item['refer_id']] ?? [];
        }
        else {
            if (!empty($item['refer_id']) && $userCustomerContact = $this->getMapData('formatContactMessage', $item['refer_id'])) {
                $item['data'] = $userCustomerContact;
            }
        }

        if (!empty($item['data'])) {
            $item['data'] = $item['data'] + [
                'start_time' => date('Y-m-d 00:00:00', strtotime($item['create_time'])),
                 'end_time' => date('Y-m-d 23:59:59', strtotime($item['create_time'])),
            ];
        }

        return $item;
    }

    public function formatTmMessage($item)
    {
        $item['data'] = $this->getData($item['client_id'], $item['trail_id']);
        if (!$this->simpleData) {
            $aliStoreAccountModel = \AlibabaStoreAccount::model();
            $aliAccount = $aliStoreAccountModel->find('client_id = :cid and seller_account_id = :sid', [
                ':cid' => $item['client_id'],
                ':sid' => $item['data']['seller_account_id'],
            ]);
            $item['data']['seller_name'] = isset($aliAccount->first_name) ? "{$aliAccount->first_name} {$aliAccount->last_name}" : '';
            $item['data']['seller_login_id'] = $aliAccount->login_id ?? '';
            $item['data']['seller_email'] = $aliAccount->seller_email ?? '';

            $aliCustomerModel = \common\models\client\AlibabaCustomerRelation::model();
            $aliCustomer = $aliCustomerModel->find('client_id = :cid and buyer_account_id = :bid  and alibaba_company_id > 0', [
                ':cid' => $item['client_id'],
                ':bid' => $item['data']['buyer_account_id'],
            ]);
            $item['data']['buyer_name'] = $aliCustomer->alibaba_customer_name ?? '';

            $alibabaStoreModel = \AlibabaStore::model();
            $alibabaStore = $alibabaStoreModel->find('client_id = :cid and store_id = :sid', [
                ':cid' => $item['client_id'],
                ':sid' => $item['data']['store_id'],
            ]);
            $item['data']['store_name'] = $alibabaStore->store_name ?? '';
			$item['data']['store_alias'] = $alibabaStore->store_alias ?? '';
        }
        else {
            if (!empty($item['data']['seller_account_id']) && $aliAccount = $this->getMapData('formatTmMessageSeller', $item['data']['seller_account_id'])) {
                $item['data']['seller_name'] = $aliAccount['seller_name'];
                $item['data']['seller_login_id'] = $aliAccount['login_id'];
            }

            if (!empty($item['data']['buyer_account_id']) && $aliCustomer = $this->getMapData('formatTmMessageBuyer', $item['data']['buyer_account_id'])) {
                $item['data']['buyer_name'] = $aliCustomer['buyer_name'];
                $item['data']['sec_token'] = $aliCustomer['sec_token'] ?? '';
            }

            if (!empty($item['data']['store_id']) && $alibabaStore = $this->getMapData('formatTmMessageStore', $item['data']['store_id'])) {
                $item['data']['store_name'] = $alibabaStore['store_name'];
				$item['data']['store_alias'] = $alibabaStore['store_alias'];
            }
        }

        if (!empty($item['data'])) {
            $item['data'] = $item['data'] + [
                    'start_time' => strtotime(date('Y-m-d 00:00:00', strtotime('-7 day', strtotime($item['create_time'])))).'000',
                    'end_time' => strtotime(date('Y-m-d 23:59:59', strtotime($item['create_time']))).'000',
                ];
        }

        return $item;
    }

    private function getShopInfo($clientId, $shopId)
    {
        if (isset($this->shops[$shopId])) {
            return $this->shops[$shopId];
        }
        $this->shops[$shopId] = TrackHelper::getSiteInfoByAmesShopId($shopId, $clientId, true);

        return $this->shops[$shopId];
    }

    private function getCountryByAlpha2($alpha2)
    {
        if (empty($this->countries)) {
            $this->countries = \CountryService::getCountryNameMap('en');
        }

        return $this->countries[$alpha2] ?? $alpha2;
    }


    public function buildMapData()
    {
        $map = [];

        $trails = $this->batchFlag ? $this->listData : [$this->data];
        $clientId = $trails[0]['client_id']?? ($this->operatorUser?$this->operatorUser->getUserId():0);

        $inquiryData = [];
        $leadIds = array_column($trails, 'lead_id');
        $leadIds && $inquiryInfo = array_column(\LeadInquiryDataModel::model()->findAll('lead_id in (' . implode(',', $leadIds) . ')'), null, 'lead_id');
        foreach ($inquiryInfo ?? [] as $leadId => $item){
            $inquiryData[$leadId] = json_decode($item['inquiry_data']??'{}',true);
        }
        $map['inquiry'] = [];

        if (array_column($trails, 'inquiry_id')){
            foreach ($trails as $trail){
                $inquiry = (new \common\library\cms\inquiry\Inquiry($clientId, $trail['inquiry_id'] ?? 0))->getAttributes();
                $site = (new \common\library\google_ads\ga\GaSite($clientId, $inquiry['site_id'] ?? 0))->getAttributes();
                $inquiryDetail = $inquiryData[$trail['lead_id']] ?? [];
                $inquiry = array_merge($inquiryDetail, [
                    'web_site_url' => $site['web_site_url'] ?? '',
                    'web_site_name' => $site['web_site_name'] ?? '',
                ]);
                $map['inquiry'][!empty($trail['inquiry_id']) ? $trail['inquiry_id'] : $trail['lead_id']] = $inquiry;
            }
        }
        if ($this->showOpportunityInfo) {
            $opportunityIds = array_filter(array_column($trails, 'opportunity_id'));
            if (count($opportunityIds)) {
                $list = new OpportunityList($clientId);
                $list->setSkipPermissionCheck(true);
                $list->setOpportunityIds($opportunityIds);
                $list->setIgnoreEnableFlag();
                $list->getFormatter()->setShowStageInfo(true);
                if($this->showAdjustEmailDynamic){
                    $list->getFormatter()->setShowHandlerInfo(true);
                    $list->getFormatter()->setShowMainUserInfo(true);
                }
                $list->getFormatter()->setSpecifyFields(['opportunity_id', 'name', 'amount', 'currency', 'serial_id', 'enable_flag', 'stage','user_id', 'account_date']);
                $opportunityList = $list->find();
                $opportunityList = array_combine(array_column($opportunityList, 'opportunity_id'), $opportunityList);
            } else {
                $opportunityList = [];
            }
            $map['opportunity'] = $opportunityList;
        }

        if ($this->showCaptureCard) {
            $clientId = current($trails)['client_id'] ?? 0;
            $map['captureCard'] = \common\library\ai\classify\capture\Helper::getCardInfo($clientId, array_column($trails, 'trail_id'));
        }

        if ($this->simpleData == false || $this->showCreateUserInfo)
        {
            $createUserIds = array_filter(array_column($trails, 'create_user'));
            if (!empty($createUserIds)) {
                $userList = \common\library\account\Helper::getBatchUserInfo($clientId, $createUserIds);
                $userInfoList = array_map(function ($item) {
                    return [
                        'user_id' => $item['user_id'],
                        'avatar' => $item['avatar'],
                        'name' => $item['nickname'],
                        'nickname' => $item['nickname'],
                    ];

                }, $userList ?? []);

                $map['user'] = array_combine(array_column($userInfoList, 'user_id'), $userInfoList);
            }
        }

        $trailIdMap = [];
        $map['followup'] = [];
        $map['trailData'] = [];
        if ($this->simpleData) {
            $needGetCustomer = $this->showCustomerInfo;
            $followUpReferIds = [];
            $trailDataIds = [];
            $customerMap = [];
            $leadCustomerMap = [];
            $userContactIds = [];
            $sellerAccountIds = [];
            $buyerAccountIds = [];
            $storeIds = [];
            foreach ($trails as $trail) {
                if ($trailTypeName = self::$map[$trail['type']] ?? null) {
                    $trailIdMap[$trailTypeName][] = $trail['refer_id'];
                    switch ($trailTypeName) {
                        case 'formatMail':
                        case 'formatSchedule':
                            $followUpReferIds[] = $trail['refer_id'];
                            break;
                        case 'formatRemark':
                            $followUpReferIds[] = $trail['refer_id'];
                            $trailDataIds[] = $trail['trail_id'];
                            break;
                        case 'formatOpportunity':
                        case 'formatAlibabaTrade':
                        case 'formatMarketing':
                        case 'formatSiteTrack':
                        case 'formatTrackChatConversation':
                        case 'formatCustomer':
                        case 'formatMarketingEdm':
                        case 'formatTmMessage':
                            $trailDataIds[] = $trail['trail_id'];
                            break;
                        case 'formatEdm':
                            $needGetCustomer = true;
                            break;
                        case 'formatContactMessage':
                            $userContactIds[] = $trail['refer_id'];
                            break;
                    }
                }

                $customerId = PgsqlUtil::trimArray($trail['customer_id'] ?? '{}');
                $customerId = is_array($customerId) ? (current($customerId) ?: 0) : ($customerId ?: 0);
                $leadCustomerId = PgsqlUtil::trimArray($trail['lead_customer_id'] ?? '{}');
                $leadCustomerId = is_array($leadCustomerId) ? (current($leadCustomerId) ?: 0) : ($leadCustomerId ?: 0);
                if ($customerId) {
                    $customerMap[$trail['trail_id']] = $customerId;
                } elseif ($leadCustomerId) {
                    $leadCustomerMap[$trail['trail_id']] = $leadCustomerId;
                }
            }

            if ($needGetCustomer) {
                if (!empty($customerMap)) {
                    $customerList = new CustomerList($clientId);
                    $customerList->setCustomerId(array_values($customerMap));
                    $customerList->setFields(['customer_id', 'name', 'email', 'tel_list', 'contact']);
                    $customerListData = $customerList->find();
                    $customerListData = array_column($customerListData, null, 'customer_id');
                    foreach ($customerMap as $trailId => &$customerDatum) {
                        $customerDatum = $customerListData[$customerDatum] ?? [
                                'customer_id' => $customerDatum,
                                'name' => '',
                                'email' => '',
                                'tel_list' => '{}',
                                'contact' => '{}'
                            ];
                        $customerDatum['tel_list'] = json_decode($customerDatum['tel_list'] ?? '{}', true);
                        $customerDatum['contact'] = json_decode($customerDatum['contact'] ?? '{}', true);
                    }
                }
                $map['customer_info'] = $customerMap;
                if (!empty($leadCustomerMap)) {
                    $leadCustomerList = new LeadCustomerList($clientId);
                    $leadCustomerList->setCustomerId(array_values($leadCustomerMap));
                    $leadCustomerList->setFields(['customer_id', 'name', 'email', 'tel_list', 'contact']);
                    $leadCustomerList->setIsArchive([1,2]);
                    $leadCustomerListData = $leadCustomerList->find();
                    $leadCustomerListData = array_column($leadCustomerListData, null, 'customer_id');
                    foreach ($leadCustomerMap as $trailId => &$leadCustomerDatum) {
                        $leadCustomerDatum = $leadCustomerListData[$leadCustomerDatum] ?? [
                                'customer_id' => $leadCustomerDatum,
                                'name' => '',
                                'email' => '',
                                'tel_list' => '{}',
                                'contact' => '{}'
                            ];
                        $leadCustomerDatum['tel_list'] = json_decode($leadCustomerDatum['tel_list'] ?? '{}', true);
                        $leadCustomerDatum['contact'] = json_decode($leadCustomerDatum['contact'] ?? '{}', true);
                    }
                }
                $map['lead_customer_info'] = $leadCustomerMap;
            }

            if (!empty($followUpReferIds)) {
                $map['followup'] = \Followup::model()->findReferMap($clientId, $followUpReferIds);
            }
            if (!empty($trailDataIds)) {
                if ($map['followup']) {
                    $trailDataIds = array_combine($trailDataIds, $trailDataIds);
                    $trailDataIds = array_replace($trailDataIds, array_intersect_key($map['followup'], $trailDataIds));
                }
                $map['trailData'] = array_column(\DynamicTrailData::model()->findByIds($clientId, $trailDataIds), 'data', 'trail_id');
            }

            foreach ($trailIdMap as $formatMethod => $ids) {
                $map[$formatMethod] = [];
                switch ($formatMethod) {
                    case 'formatMail':
                        $formatObjList = new MailList($clientId);
                        $ids = array_combine($ids, $ids);
                        if ($map['followup']) {
                            $ids = array_replace($ids, array_intersect_key($map['followup'], $ids));
                        }
                        $formatObjList->setMailIds($ids);
                        $formatObjList->setFields(['mail_id', 'folder_id', 'user_id', 'subject', 'delete_flag']);
                        $formatObjListData = $formatObjList->find();
                        $ids = array_flip($ids);
                        foreach ($formatObjListData as $formatObjListDatum) {
                            $map[$formatMethod][$ids[$formatObjListDatum['mail_id']]] = $formatObjListDatum;
                        }
                        break;
                    case 'formatQuotation':
                        $formatObjList = new QuotationList($this->operatorUser->getUserId());
                        $formatObjList->setSkipPermissionCheck(true);
                        $formatObjList->setQuotationIds($ids);
                        $formatObjList->setFields(['quotation_id', 'name']);
                        $formatObjListData = $formatObjList->find();
                        $map[$formatMethod] = array_column($formatObjListData, 'name', 'quotation_id');
                        break;
                    case 'formatEdm':
                        if (!empty($customerMap) || !empty($leadCustomerMap)) {
                            $c = new \CDbCriteria();
                            $c->addInCondition('task_id', $ids);
                            $edmSendTo = array_filter(array_unique(array_merge(
                                array_column($customerMap, 'email'),
                                array_column($leadCustomerMap, 'email')
                            )), function ($email) {
                                return !\Util::containChinese($email);
                            });
                            if ($edmSendTo) {
                                $c->addInCondition('send_to', $edmSendTo);
                            }
                            $groupMailList = \GroupMail::model()->findAll($c);
                            foreach ($groupMailList as $groupMail) {
                                $map['formatEdmMail'][$groupMail['task_id']] = $groupMail->getAttributes();
                            }
                            $ids = array_diff($ids, array_keys($map['formatEdmMail'] ?? []));
                        }
                        if ($ids) {
                            $formatObjListData = \GroupMailTask::model()->findAllByPk($ids);
                            $map[$formatMethod] = array_column($formatObjListData, null, 'task_id');
                        }
                        break;
                    case 'formatOrder':
                        $formatObjList = new OrderList($this->operatorUser->getUserId());
                        $formatObjList->setSkipPermissionCheck(true);
                        $formatObjList->setOrderIds($ids);
                        $formatObjList->setFields(['order_id', 'name']);
                        $formatObjListData = $formatObjList->find();
                        $map[$formatMethod] = array_column($formatObjListData, 'name', 'order_id');
                        break;
                    case 'formatPi':
                        //do nothing
                        break;
                    case 'formatPayPhone':
                        break;

                    case 'formatContactMessage':
                        $userCustomerContactList = \common\library\sns\customer\CustomerContactHelper::getTrailUserCustomerContact($clientId, $userContactIds, $this->showCompanyId);
                        $map[$formatMethod] = $userCustomerContactList;
                        break;

					case  'formatAlibabaTrade':
						foreach ($map['trailData'] as $trailDatum) {
							$trailDatum = json_decode($trailDatum, true);
							if (isset($trailDatum['seller_account_id'])) {
								$sellerAccountIds[] = $trailDatum['seller_account_id'];
							}
						}

						if (!empty($sellerAccountIds)) {
							$aliStores = \AlibabaStore::model()->findAll('client_id = :cid AND seller_account_id IN ('. implode(',', $sellerAccountIds) .')', [
								':cid' => $clientId
							]);
							foreach ($aliStores as $item) {
								$map['formatAlibabaTradeStore'][$item['seller_account_id']] = [
									'store_alias' => $item['store_alias'],
								];
							}
						}
						break;
                    case 'formatTmMessage':
                        foreach ($map['trailData'] as $trailDatum) {
                            $trailDatum = json_decode($trailDatum, true);
                            if (isset($trailDatum['seller_account_id'])) {
                                $sellerAccountIds[] = $trailDatum['seller_account_id'];
                            }
                            if (isset($trailDatum['buyer_account_id'])) {
                                $buyerAccountIds[] = $trailDatum['buyer_account_id'];
                            }
                            if (isset($trailDatum['store_id'])) {
                                $storeIds[] = $trailDatum['store_id'];
                            }
                        }

                        if (!empty($sellerAccountIds)) {
                            $aliAccounts = \AlibabaStoreAccount::model()->findAll('client_id = :cid AND seller_account_id IN ('. implode(',', $sellerAccountIds) .')', [
                                ':cid' => $clientId
                            ]);
                            foreach ($aliAccounts as $item) {
                                $map['formatTmMessageSeller'][$item['seller_account_id']] = [
                                    'seller_name' => "{$item['first_name']} {$item['last_name']}",
                                    'login_id' => $item['login_id'],
                                    'seller_email' => $item['seller_email'],
                                ];
                            }
                        }

                        if (!empty($buyerAccountIds)) {
                            $aliCustomers = \common\models\client\AlibabaCustomerRelation::model()->findAll('client_id = :cid AND buyer_account_id IN ('. implode(',', $buyerAccountIds) .')  AND alibaba_company_id > 0', [
                                ':cid' => $clientId
                            ]);
                            foreach ($aliCustomers as $item) {
                                $map['formatTmMessageBuyer'][$item['buyer_account_id']] = [
                                    'buyer_name' => $item['alibaba_customer_name'],
                                    'sec_token'  => $item['sec_token'] ?? '',
                                ];
                            }
                        }

                        if (!empty($storeIds)) {
                            $aliStores = \AlibabaStore::model()->findAll('client_id = :cid AND store_id IN ('. implode(',', $storeIds) .')', [
                                ':cid' => $clientId
                            ]);
                            foreach ($aliStores as $item) {
                                $map['formatTmMessageStore'][$item['store_id']] = [
                                    'store_name' => $item['store_name'],
									'store_alias' => $item['store_alias'] ?? '',
                                ];
                            }
                        }

                        break;

                    default:
                        break;
                }
            }

        }

        if ($this->showInvalidInquiryFlag) {
            $trailDataIds = array_column($trails, 'trail_id');
            if ($trailDataIds) {
                $map['trailData'] = array_column(\DynamicTrailData::model()->findByIds($clientId, $trailDataIds), 'data', 'trail_id');
            }
        }

	    if ($this->showCommentList) {

		    $map['comment_map'] = \common\library\trail\comment\Helper::getTrailCommentMap($clientId, array_column($trails, 'company_id'), array_column($trails, 'trail_id'), $this->keywordSearch);
	    }

        if ($this->showMarketingAutomationPlan) {
            //群发单显邮件需要查询(目前智能营销只支持邮件中的群发单显)
            $mailIds = array_column(array_filter($trails, function ($trail) {
                return in_array($trail['type'] ?? 0, [TrailConstants::TYPE_MAIL_SEND,TrailConstants::TYPE_MAIL_RECEIVER]);
            }),'refer_id');
            $mainMailList = \common\library\mail\Helper::queryExposeMainMailList($clientId, $mailIds);

            //查询群发单显主邮件id
            $mainIds = array_column($mainMailList, 'main_mail_id');

            $edmIds = array_column(array_filter($trails, function ($trail) {
                return in_array($trail['type'] ?? 0, [TrailConstants::TYPE_EDM_SEND]);
            }),'refer_id');


            $referIds = array_values(array_unique(array_merge($mainIds , $edmIds)));
            if (count($referIds)) {
                $planApi = new MarketingAutomationPlanApi($clientId);
                $planMap = array_column($planApi->planListWithTrail($referIds), null,'process_item');
            }


            $map['mail_map'] = array_column($mainMailList, 'main_mail_id','mail_id') ?? [];
            $map['plan_map'] = $planMap ?? [];
        }

        $userList = new UserList();
        $userList->setFields('user_id, nickname');
        $userList->setClientId($clientId);
        $map['user_info'] = array_column($userList->find(), null, 'user_id');
	    $this->mapData = $map;
    }

    /**
     * @param $clientId
     * @param $inquiryId
     * @return array
     */
    protected function getInquiryCustomerInfo($clientId, $inquiryId) {
        static $inquiryList = [];
        if(!isset($inquiryList[$inquiryId])) {
            $customerInfo = [];
            $inquiry = new \common\library\cms\inquiry\Inquiry($clientId, $inquiryId);
            $customerInfo['email'] = $inquiry->email ?? '';
            $customerInfo['name'] = $inquiry->customer_name ?? $customerInfo['email'];
            $inquiryList[$inquiryId] = $customerInfo;
        }
        return $inquiryList[$inquiryId];
    }

    /**
     * @param $clientId
     * @param $inquiryId
     * @return array
     */
    protected function getTrackSessionCustomerInfo($clientId, $trackSessionNo) {
        static $trackSessionList = [];
        if(!isset($trackSessionList[$trackSessionNo])) {
            $trackSession = CmsConversationService::getVisitorInfoByTrackSessionNo($clientId, $trackSessionNo);
            $customerInfo['email'] = $trackSession['email'];
            $customerInfo['name'] = $trackSession['nickname'];
            $trackSessionList[$trackSessionNo] = $customerInfo;
        }
        return $trackSessionList[$trackSessionNo];
    }

}
