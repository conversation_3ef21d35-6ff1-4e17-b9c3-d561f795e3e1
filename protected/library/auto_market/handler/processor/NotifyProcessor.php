<?php

namespace common\library\auto_market\handler\processor;

use common\library\auto_market\handler\MarketingAutomationHandler;
use common\library\auto_market\handler\MarketingAutomationHandlerApi;
use common\library\coroutine\exception\RuntimeException;

/**
 * 通知-处理类
 */
abstract class NotifyProcessor extends BaseProcessor
{
    public function __construct(int $clientId , int $userId, int $planId, int $handlerId)
    {
        parent::__construct($clientId , $userId, $planId, $handlerId);
    }

    /**
     * @throws RuntimeException
     */
    public function loadNotifyHandler(): ?MarketingAutomationHandler
    {
        $handler = $this->loadCurrentHandler();
        if (!$handler->isNotifyHandler()) {
            throw new RuntimeException(\Yii::t('marketing_automation', 'Wrong process processing type, not notify type'),
                \ErrorCode::CODE_FAIL);
        }
        return $handler;
    }

}