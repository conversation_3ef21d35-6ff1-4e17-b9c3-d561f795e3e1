<?php
namespace common\library\queue_v2\job;

use common\library\account\Client;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\RecommendCompanyService;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\BaseJob;
use common\library\queue_v2\QueueConstant;
use common\library\queue_v2\QueueService;

/**
 * Created by PhpStorm.
 * Author: Heather(huangchenling)
 * Date: 2025/4/17
 * Time: 17:07
 */

class AiSdrDigTaskJob extends BaseJob
{

    public $channel = QueueConstant::CONNECTION_NAME_AI_SDR;
    protected int $clientId;
    protected int $taskId;
    public $maxTimeout = 3600;

    public function __construct($clientId, $taskId) {
        $this->clientId = $clientId;
        $this->taskId = $taskId;
    }

    public function handle() {
        if (!Helper::hasFunctional($this->clientId, PrivilegeConstants::FUNCTIONAL_AI_SDR)) {
            \LogUtil::info("client_id {$this->clientId} missing ai_sdr privilege, skip dig task {$this->taskId}");
            return;
        }

        \LogUtil::info("client_id {$this->clientId} start dig task {$this->taskId}");
        $adminUserId = PrivilegeService::getInstance($this->clientId)->getAdminUserId();
        \User::setLoginUserById($adminUserId);
        $recommendService = new RecommendCompanyService($this->clientId, $adminUserId);
        $task = new AiSdrTask($this->clientId, $this->taskId);
        if ($task->isNew()) {
            \LogUtil::info("AI_SDR task_id({$this->taskId}) is new, skip processTask");
            return;
        }

        list($matchProducts, $matchIndustries) = $recommendService->processRecommend($task->task_id);
        try {
            [$isDigFinished, $digCount] = $recommendService->getRecommendCompany($matchProducts, $matchIndustries);
dd($isDigFinished, $digCount);
            if ($isDigFinished) {
                // 挖掘不到新数据，标记挖掘完成，下一阶段任务不再重复挖掘
                $filter = new AiSdrTaskFilter($this->clientId);
                $filter->task_id = $task->task_id;
                $filter->find()->getOperator()->update(['current_stage' => Constant::AI_SDR_STAGE_REACHABLE]);
            }

            if ($task->task_status == Constant::AI_SDR_TASK_STATUS_DRAFT) {
                \LogUtil::info("client_id {$this->clientId} task_id {$task->task_id} is draft task, start process dig after recommending");
                $service = new AISdrService($this->clientId, $adminUserId);
                $service->processDig($task);
            }
            \LogUtil::info("client_id {$this->clientId} dig task {$this->taskId} success, isDigFinished: {$isDigFinished}, recommendCompanies count {$digCount}");
        } catch (\RuntimeException $exception) {
            \LogUtil::info("client_id {$this->clientId} dig task {$this->taskId} failed, error: {$exception->getMessage()}");
        }
    }
}