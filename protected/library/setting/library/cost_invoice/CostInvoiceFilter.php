<?php


namespace common\library\setting\library\cost_invoice;


use common\library\setting\item\ItemSettingConstant;
use common\library\setting\item\ItemSettingFilter;

class CostInvoiceFilter extends ItemSettingFilter
{
    use InitCostInvoiceMetadata;

    protected function defaultSetting()
    {
        $this->client_id = $this->clientId;
        $this->module = \Constants::TYPE_COST_INVOICE;
        $this->item_type = ItemSettingConstant::ITEM_TYPE_COST_INVOICE;
    }
}