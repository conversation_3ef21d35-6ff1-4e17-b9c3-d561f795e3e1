<?php

namespace common\library\setting\library\common;

use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\setting\item\ItemSettingConstant;
use protobuf\Customer\PBGrowthLevel;

class GrowthLevelMetadata extends CommonMetadata {


    //	阿里买家分层
    const GROWTH_LEVEL_0  = 0;
    const GROWTH_LEVEL_1  = 1;
    const GROWTH_LEVEL_1P = 2;
    const GROWTH_LEVEL_2  = 22;
    const GROWTH_LEVEL_3  = 3;
    const GROWTH_LEVEL_4  = 4;

    const GROWTH_LEVEL_TO_PB_MAP = [
        self::GROWTH_LEVEL_0 => PBGrowthLevel::GROWTH_LEVEL_0,
        self::GROWTH_LEVEL_2 => PBGrowthLevel::GROWTH_LEVEL_2,
        self::GROWTH_LEVEL_3 => PBGrowthLevel::GROWTH_LEVEL_3,
        self::GROWTH_LEVEL_4 => PBGrowthLevel::GROWTH_LEVEL_4,
    ];

    public static function getExtraDataMap($module) {
        return [
            self::GROWTH_LEVEL_1P => 'L1+',
            self::GROWTH_LEVEL_1  => 'L1',
            self::GROWTH_LEVEL_2  => 'L2',
            self::GROWTH_LEVEL_3  => 'L3',
            self::GROWTH_LEVEL_4  => 'L4',
        ];
    }

    public static function getItemType() {

        return ItemSettingConstant::ITEM_TYPE_GROWTH_LEVEL;
    }
}