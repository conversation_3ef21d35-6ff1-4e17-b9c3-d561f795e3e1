<?php
/**
 *
 * Author: ruisenlin
 * Date: 2023/3/21
 */

namespace common\library\forward\lighthouse;

class SimpleForward
{
    private $url;
    private $params;
    private $cache;
    private $redisKey;

    // 缓存7天
    private $cahceTime = 7 * 24 * 60 * 60;

    /**
     * @param $url
     * @param array $params
     * @param bool $cache
     * @param string $redisKey
     */
    public function __construct($url, $params = [], $cache = false, $redisKey = '')
    {
        if ($cache && empty($redisKey))
            throw new \RuntimeException("redis key is empty");

        $this->url = $url;
        $this->params = $params;
        $this->cache = $cache;
        $this->redisKey = $redisKey;
    }

    /**
     * @param float|int $cahceTime
     */
    public function setCahceTime(float|int $cahceTime): void
    {
        $this->cahceTime = $cahceTime;
    }

    public function doForward()
    {
        if ($this->cache)
        {
            $redis = \RedisService::cache();
            $result = json_decode($redis->get($this->redisKey) ?? '', true);

            if (!empty($result) && ($result['code'] ?? '') == \ErrorCode::CODE_SUCCESS)
            {
                return $result['data'];
            }
        }

        $httpClient = new \HTTPClient();

        $start = microtime(true);
        $lightHouseResult = $httpClient->get($this->url, $this->params) ?? '';
        $end = microtime(true);

        $result = json_decode($lightHouseResult, true);

        \LogUtil::info(__FUNCTION__ . ' lighthouse result : ' . ($lightHouseResult). ' spend time : ' . ($end - $start) . 's');

        if ($result['code'] ?? '' != \ErrorCode::CODE_SUCCESS)
        {
            // 测试环境打印错误信息
            if (\Yii::app()->params['env'] == 'test')
            {
                throw new \RuntimeException($result['msg'] ?? 'internal error');
            }

            throw new \RuntimeException('internal error', \ErrorCode::CODE_FAIL);
        }

        // 缓存7天
        $this->cache && $redis->setex($this->redisKey, $this->cahceTime, $lightHouseResult);

        return $result['data'];
    }

}