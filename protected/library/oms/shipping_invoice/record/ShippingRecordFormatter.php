<?php

namespace common\library\oms\shipping_invoice\record;

use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\oms\common\InvoiceRecordFormatter;
use common\library\oms\task\formatter\RelationMasterProductInfoTask;
use common\library\oms\task\formatter\ShippingInvoiceInfoTask;
use common\library\oms\task\formatter\ShippingOutboundCountTask;
use common\library\oms\task\formatter\ShippingRecordInfo;
use common\library\oms\task\formatter\ShowOrderProductCount;
use common\library\privilege_v3\field\FormatterFieldPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;

/**
 * Class ShippingRecordFormatter
 * @package common\library\oms\shipping_invoice\record
// * @method displayShowProductList(bool $flag)
 * @method displayShowInvoiceInfo(bool $flag)
 * @method displayShowOrderInfo(bool $flag)
 * @method displayShippingOutboundCount(bool $flag)
 * @method displayRelationMasterProductInfo(bool $true)
 */
class ShippingRecordFormatter extends InvoiceRecordFormatter
{
    public function getModuleType()
    {
        return \Constants::TYPE_SHIPPING_INVOICE;
    }

    const MAPPING_SETTING = [
        'show_product_list' => [
            'task_class' => ShippingRecordInfo::class
        ],
        'show_invoice_info' => [
            'task_class' => ShippingInvoiceInfoTask::class,
            'module' => \Constants::TYPE_SHIPPING_INVOICE
        ],
        'show_order_info' => [
            'task_class' => ShowOrderProductCount::class,
            'module' => \Constants::TYPE_SHIPPING_INVOICE
        ],
        //出运单-出库数量
        'shipping_outbound_count'=>[
            'task_class' => ShippingOutboundCountTask::class,
            'module' => \Constants::TYPE_SHIPPING_INVOICE
        ],
        'relation_master_product_info' => [
            'task_class'  => RelationMasterProductInfoTask::class,
            'module_type' => \Constants::TYPE_SHIPPING_INVOICE,
        ],
    ] + parent::MAPPING_SETTING;

    public function webListSetting()
    {
        $this->displayShowInvoiceInfo(true);
        $this->displayShowProductList(true);
        $this->displayShowOrderInfo(true);
        $this->displayShippingOutboundCount(true);
        $this->displayExternalFieldData(true);
        $this->displayRelationMasterProductInfo(true);
        $this->displayMasterGroupInfo(true);
        $this->displayFields($this->metadata->columnFields());
        $this->displayRichField(true);
    }

    public function webExportSetting()
    {
        $this->displayShowInvoiceInfo(true);
        $this->displayShowProductList(true);
        $this->displayShowOrderInfo(true);
        $this->displayShippingOutboundCount(true);
        $this->displayExternalFieldData(true,true);
        $this->displayMasterGroupInfo(true);
        $this->displayFields($this->metadata->columnFields());
    }


    protected function getExternalFieldSettingList()
    {
        if (isset($this->externalFieldSettingList)) {
            return $this->externalFieldSettingList;
        }

        $fieldList = new FieldList($this->clientId);
        $fieldList->setType(\Constants::TYPE_SHIPPING_INVOICE);
        $fieldList->setBase(0);
        $fieldList->setGroupId(CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE);
        $fieldList->setDisableFlag(false);
        $fieldList->setNeedList(true);
        $externalFieldSettingList = $fieldList->find();

        $this->externalFieldSettingList = array_column($externalFieldSettingList, null, 'id');

        return $this->externalFieldSettingList;
    }

    public function displayShowProductList($flag, $record_type = '')
    {
        $this->setting['show_product_list'] = [
            'argument' => $flag,
            'record_type' => $record_type
        ];
    }

//    protected function resultDecorator(array &$result)
//    {
//        //字段权限过滤
//        $formatterFieldPrivilegeService = new FormatterFieldPrivilegeService(\User::getLoginUser()->getUserId(),PrivilegeConstants::FUNCTIONAL_PURCHASE_SUPPLIER);
//        $formatterFieldPrivilegeService->handle($result);
//
//        $result['privilege_field_stats'] = $formatterFieldPrivilegeService->getPrivilegeFieldStats();
//
//        parent::resultDecorator($result);
//    }


}