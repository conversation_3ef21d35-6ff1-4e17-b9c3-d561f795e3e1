<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *响应 用户设置列表 /userRead/setting
 *
 * Generated from protobuf message <code>PBUserSettingRsp</code>
 */
class PBUserSettingRsp extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.PBUserSetting setting = 1;</code>
     */
    protected $setting = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \protobuf\CRMCommon\PBUserSetting $setting
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.PBUserSetting setting = 1;</code>
     * @return \protobuf\CRMCommon\PBUserSetting|null
     */
    public function getSetting()
    {
        return $this->setting;
    }

    public function hasSetting()
    {
        return isset($this->setting);
    }

    public function clearSetting()
    {
        unset($this->setting);
    }

    /**
     * Generated from protobuf field <code>.PBUserSetting setting = 1;</code>
     * @param \protobuf\CRMCommon\PBUserSetting $var
     * @return $this
     */
    public function setSetting($var)
    {
        GPBUtil::checkMessage($var, \protobuf\CRMCommon\PBUserSetting::class);
        $this->setting = $var;

        return $this;
    }

}

