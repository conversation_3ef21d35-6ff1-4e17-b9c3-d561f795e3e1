<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Chat.proto

namespace protobuf\Chat;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 **
 * 事件类型消息
 * message_type=event
 *
 * Generated from protobuf message <code>PBEvent</code>
 */
class PBEvent extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string event_type = 1;</code>
     */
    protected $event_type = '';
    protected $event_data;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $event_type
     *     @type \protobuf\Chat\PBEventMessageDelete $message_delete
     *     @type \protobuf\Chat\PBEventMediaDownloadable $media_downloadable
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Chat::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string event_type = 1;</code>
     * @return string
     */
    public function getEventType()
    {
        return $this->event_type;
    }

    /**
     * Generated from protobuf field <code>string event_type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEventType($var)
    {
        GPBUtil::checkString($var, True);
        $this->event_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBEventMessageDelete message_delete = 2;</code>
     * @return \protobuf\Chat\PBEventMessageDelete|null
     */
    public function getMessageDelete()
    {
        return $this->readOneof(2);
    }

    public function hasMessageDelete()
    {
        return $this->hasOneof(2);
    }

    /**
     * Generated from protobuf field <code>.PBEventMessageDelete message_delete = 2;</code>
     * @param \protobuf\Chat\PBEventMessageDelete $var
     * @return $this
     */
    public function setMessageDelete($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Chat\PBEventMessageDelete::class);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBEventMediaDownloadable media_downloadable = 3;</code>
     * @return \protobuf\Chat\PBEventMediaDownloadable|null
     */
    public function getMediaDownloadable()
    {
        return $this->readOneof(3);
    }

    public function hasMediaDownloadable()
    {
        return $this->hasOneof(3);
    }

    /**
     * Generated from protobuf field <code>.PBEventMediaDownloadable media_downloadable = 3;</code>
     * @param \protobuf\Chat\PBEventMediaDownloadable $var
     * @return $this
     */
    public function setMediaDownloadable($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Chat\PBEventMediaDownloadable::class);
        $this->writeOneof(3, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getEventData()
    {
        return $this->whichOneof("event_data");
    }

}

