<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: PaasInterface.proto

namespace protobuf\FlutterPaas;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *&#47;业务对象，
 *
 * Generated from protobuf message <code>PbPaasObject</code>
 */
class PbPaasObject extends \Google\Protobuf\Internal\Message
{
    /**
     *业务对象名
     *
     * Generated from protobuf field <code>.PaasObjectName object_name = 1;</code>
     */
    protected $object_name = 0;
    /**
     *主键field，用于跳转通用详情页面        
     *
     * Generated from protobuf field <code>.PbPaasField unique_field = 2;</code>
     */
    protected $unique_field = null;
    /**
     *所有字段field,用于渲染
     *
     * Generated from protobuf field <code>repeated .PbPaasField fields = 3;</code>
     */
    private $fields;
    /**
     *审批单相关数据
     *
     * Generated from protobuf field <code>.PbApprovalFlowInfo approval_flow_info = 4;</code>
     */
    protected $approval_flow_info = null;
    /**
     *对象权限相关
     *
     * Generated from protobuf field <code>repeated .PaasObjectOperatePrivilege operate_privilege = 5;</code>
     */
    private $operate_privilege;
    /**
     *字段权限相关
     *
     * Generated from protobuf field <code>.PaasObjectFieldPrivilege field_privilege_stats = 6;</code>
     */
    protected $field_privilege_stats = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $object_name
     *          业务对象名
     *     @type \protobuf\FlutterPaas\PbPaasField $unique_field
     *          主键field，用于跳转通用详情页面        
     *     @type array<\protobuf\FlutterPaas\PbPaasField>|\Google\Protobuf\Internal\RepeatedField $fields
     *          所有字段field,用于渲染
     *     @type \protobuf\FlutterPaas\PbApprovalFlowInfo $approval_flow_info
     *          审批单相关数据
     *     @type array<\protobuf\FlutterPaas\PaasObjectOperatePrivilege>|\Google\Protobuf\Internal\RepeatedField $operate_privilege
     *          对象权限相关
     *     @type \protobuf\FlutterPaas\PaasObjectFieldPrivilege $field_privilege_stats
     *          字段权限相关
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\PaasInterface::initOnce();
        parent::__construct($data);
    }

    /**
     *业务对象名
     *
     * Generated from protobuf field <code>.PaasObjectName object_name = 1;</code>
     * @return int
     */
    public function getObjectName()
    {
        return $this->object_name;
    }

    /**
     *业务对象名
     *
     * Generated from protobuf field <code>.PaasObjectName object_name = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setObjectName($var)
    {
        GPBUtil::checkEnum($var, \protobuf\FlutterPaas\PaasObjectName::class);
        $this->object_name = $var;

        return $this;
    }

    /**
     *主键field，用于跳转通用详情页面        
     *
     * Generated from protobuf field <code>.PbPaasField unique_field = 2;</code>
     * @return \protobuf\FlutterPaas\PbPaasField|null
     */
    public function getUniqueField()
    {
        return $this->unique_field;
    }

    public function hasUniqueField()
    {
        return isset($this->unique_field);
    }

    public function clearUniqueField()
    {
        unset($this->unique_field);
    }

    /**
     *主键field，用于跳转通用详情页面        
     *
     * Generated from protobuf field <code>.PbPaasField unique_field = 2;</code>
     * @param \protobuf\FlutterPaas\PbPaasField $var
     * @return $this
     */
    public function setUniqueField($var)
    {
        GPBUtil::checkMessage($var, \protobuf\FlutterPaas\PbPaasField::class);
        $this->unique_field = $var;

        return $this;
    }

    /**
     *所有字段field,用于渲染
     *
     * Generated from protobuf field <code>repeated .PbPaasField fields = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFields()
    {
        return $this->fields;
    }

    /**
     *所有字段field,用于渲染
     *
     * Generated from protobuf field <code>repeated .PbPaasField fields = 3;</code>
     * @param array<\protobuf\FlutterPaas\PbPaasField>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFields($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\FlutterPaas\PbPaasField::class);
        $this->fields = $arr;

        return $this;
    }

    /**
     *审批单相关数据
     *
     * Generated from protobuf field <code>.PbApprovalFlowInfo approval_flow_info = 4;</code>
     * @return \protobuf\FlutterPaas\PbApprovalFlowInfo|null
     */
    public function getApprovalFlowInfo()
    {
        return $this->approval_flow_info;
    }

    public function hasApprovalFlowInfo()
    {
        return isset($this->approval_flow_info);
    }

    public function clearApprovalFlowInfo()
    {
        unset($this->approval_flow_info);
    }

    /**
     *审批单相关数据
     *
     * Generated from protobuf field <code>.PbApprovalFlowInfo approval_flow_info = 4;</code>
     * @param \protobuf\FlutterPaas\PbApprovalFlowInfo $var
     * @return $this
     */
    public function setApprovalFlowInfo($var)
    {
        GPBUtil::checkMessage($var, \protobuf\FlutterPaas\PbApprovalFlowInfo::class);
        $this->approval_flow_info = $var;

        return $this;
    }

    /**
     *对象权限相关
     *
     * Generated from protobuf field <code>repeated .PaasObjectOperatePrivilege operate_privilege = 5;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getOperatePrivilege()
    {
        return $this->operate_privilege;
    }

    /**
     *对象权限相关
     *
     * Generated from protobuf field <code>repeated .PaasObjectOperatePrivilege operate_privilege = 5;</code>
     * @param array<\protobuf\FlutterPaas\PaasObjectOperatePrivilege>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setOperatePrivilege($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\FlutterPaas\PaasObjectOperatePrivilege::class);
        $this->operate_privilege = $arr;

        return $this;
    }

    /**
     *字段权限相关
     *
     * Generated from protobuf field <code>.PaasObjectFieldPrivilege field_privilege_stats = 6;</code>
     * @return \protobuf\FlutterPaas\PaasObjectFieldPrivilege|null
     */
    public function getFieldPrivilegeStats()
    {
        return $this->field_privilege_stats;
    }

    public function hasFieldPrivilegeStats()
    {
        return isset($this->field_privilege_stats);
    }

    public function clearFieldPrivilegeStats()
    {
        unset($this->field_privilege_stats);
    }

    /**
     *字段权限相关
     *
     * Generated from protobuf field <code>.PaasObjectFieldPrivilege field_privilege_stats = 6;</code>
     * @param \protobuf\FlutterPaas\PaasObjectFieldPrivilege $var
     * @return $this
     */
    public function setFieldPrivilegeStats($var)
    {
        GPBUtil::checkMessage($var, \protobuf\FlutterPaas\PaasObjectFieldPrivilege::class);
        $this->field_privilege_stats = $var;

        return $this;
    }

}

