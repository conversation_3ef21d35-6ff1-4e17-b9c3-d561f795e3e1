<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use UnexpectedValueException;

/**
 * Protobuf type <code>StageTypeEnum</code>
 */
class StageTypeEnum
{
    /**
     *无商机
     *
     * Generated from protobuf enum <code>STAGE_ON_NONE = 0;</code>
     */
    const STAGE_ON_NONE = 0;
    /**
     *进行中
     *
     * Generated from protobuf enum <code>STAGE_ON_GOING_STATUS = 1;</code>
     */
    const STAGE_ON_GOING_STATUS = 1;
    /**
     *赢单
     *
     * Generated from protobuf enum <code>STAGE_WIN_STATUS = 2;</code>
     */
    const STAGE_WIN_STATUS = 2;
    /**
     *输单
     *
     * Generated from protobuf enum <code>STAGE_FAIL_STATUS = 3;</code>
     */
    const STAGE_FAIL_STATUS = 3;

    private static $valueToName = [
        self::STAGE_ON_NONE => 'STAGE_ON_NONE',
        self::STAGE_ON_GOING_STATUS => 'STAGE_ON_GOING_STATUS',
        self::STAGE_WIN_STATUS => 'STAGE_WIN_STATUS',
        self::STAGE_FAIL_STATUS => 'STAGE_FAIL_STATUS',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

