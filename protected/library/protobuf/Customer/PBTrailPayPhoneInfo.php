<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 动态公费电话类型
 *
 * Generated from protobuf message <code>PBTrailPayPhoneInfo</code>
 */
class PBTrailPayPhoneInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 call_id = 1;</code>
     */
    protected $call_id = 0;
    /**
     * Generated from protobuf field <code>string target_tel = 2;</code>
     */
    protected $target_tel = '';
    /**
     * Generated from protobuf field <code>string seconds = 3;</code>
     */
    protected $seconds = '';
    /**
     * Generated from protobuf field <code>string record_url = 4;</code>
     */
    protected $record_url = '';
    /**
     * Generated from protobuf field <code>string file_ext = 5;</code>
     */
    protected $file_ext = '';
    /**
     * Generated from protobuf field <code>string type = 6;</code>
     */
    protected $type = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $call_id
     *     @type string $target_tel
     *     @type string $seconds
     *     @type string $record_url
     *     @type string $file_ext
     *     @type string $type
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 call_id = 1;</code>
     * @return int|string
     */
    public function getCallId()
    {
        return $this->call_id;
    }

    /**
     * Generated from protobuf field <code>uint64 call_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCallId($var)
    {
        GPBUtil::checkUint64($var);
        $this->call_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string target_tel = 2;</code>
     * @return string
     */
    public function getTargetTel()
    {
        return $this->target_tel;
    }

    /**
     * Generated from protobuf field <code>string target_tel = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTargetTel($var)
    {
        GPBUtil::checkString($var, True);
        $this->target_tel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string seconds = 3;</code>
     * @return string
     */
    public function getSeconds()
    {
        return $this->seconds;
    }

    /**
     * Generated from protobuf field <code>string seconds = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSeconds($var)
    {
        GPBUtil::checkString($var, True);
        $this->seconds = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string record_url = 4;</code>
     * @return string
     */
    public function getRecordUrl()
    {
        return $this->record_url;
    }

    /**
     * Generated from protobuf field <code>string record_url = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRecordUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->record_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string file_ext = 5;</code>
     * @return string
     */
    public function getFileExt()
    {
        return $this->file_ext;
    }

    /**
     * Generated from protobuf field <code>string file_ext = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setFileExt($var)
    {
        GPBUtil::checkString($var, True);
        $this->file_ext = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string type = 6;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>string type = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

}

