<?php
/**
 * Created by PhpStorm.
 * Author: <PERSON>(<PERSON><PERSON>)
 * Date: 2023/12/14
 * Time: 10:47
 */

namespace common\library\recommend_plaza;

use common\library\account\service\LoginService;
use common\library\api\InnerApi;
use common\library\discovery\api\BaseInnerApi;
use common\library\util\Arr;

class RecommendApi extends BaseInnerApi {

    protected function getParamsConfig(): array
    {
        return [
            'client_id|require|integer|default:',
            'user_id|require|integer|default:',
            'list|string|default:""',
            'data|array|default:[]',
            'async|boolean|default:false',
        ];
    }

    protected $serviceName = 'okki_leads_service';
    public function __construct(int $clientId, int $userId)
    {
        parent::__construct($clientId, $userId);
        $this->setParams([
            'client_id' => $clientId,
            'user_id' => $userId,
        ]);
    }

    /**
     * 算法服务提前把数据插入到client库中，如果库里没有数据提前通知算法进行预加载
    */
    public function getRecommendList() {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
        $data = $this->innerCall($api, 'recommendList', $this->getParams());
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function getUserKeywordsMapping() {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
        $data = $this->innerCall($api, 'getUserKeywordsMapping', $this->getParams());
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function upsertUserKeywordsMapping(array $demandMap, array $hsCodeMap) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $data = $this->innerCall($api, 'upsertUserKeywordsMapping', array_merge($this->getParams(),[
            'demand' => $demandMap,
            'hscode' => $hsCodeMap
        ]));
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function deleteUserKeywordsMapping(array $demandMap, array $hsCodeMap) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $data = $this->innerCall($api, 'deleteUserKeywordsMapping', array_merge($this->getParams(),[
            'demand' => $demandMap,
            'hscode' => $hsCodeMap
        ]));
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function getDynamicUpdateCompanyIds(array $companyHashIds, int $page, int $pageSize, string $sortField = 'start_date', string $sortType = 'desc') {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $data = $this->innerCall($api, 'getDynamicUpdateCompanyList', array_merge($this->getParams(), [
            'company_hash_ids' => $companyHashIds,
            'page' => $page,
            'page_size' => $pageSize,
            'sort_field' => $sortField,
            'sort_type' => $sortType,
        ]));
        if (!$data) {
            return [0, [], []];
        }
        return [$data['count']??0, $data['list']??[], $data['updated_company_ids']??[]];
    }

    public function getRecommendListBySearchTag(array $tagList, array $type = [], array $keywords = [], array $sort = [], $hasCompany = null) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
        $params = array_merge($this->getParams());
        if (!empty($type)) {
            $params['type'] = $type;
        }
        if (!empty($tagList)) {
            $params['tags'] = $tagList;
        }
        if (!empty($keywords)) {
            $params['keywords'] = $keywords;
        }
        if (!empty($sort)) {
            $params['sort'] = $sort;
        }
        $params['has_company'] = $hasCompany;
        \LogUtil::info("{$this->serviceName} searchAfterTag params: ".json_encode($params));
        $data = $this->innerCall($api, 'searchEvent', $params);
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function getCustomizedRecommend($limit) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
        $data = $this->innerCall($api, 'customizedRecommend', array_merge($this->getParams(), ['limit' => $limit]));
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function getRelatedCompanyProfiles() {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
        $data = $this->innerCall($api, 'relatedCompanyProfiles', $this->getParams());
        if (!$data) {
            return [];
        }
        return $data;
    }

    protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
    {
        $language = \Yii::app()->language;
        $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId};_t_language={$language}"]);
        try {
            return parent::innerCall($api, $interface, $params, $cacheKey, $processResult);
        } catch (\Exception $exception) {
            if ($exception->getCode() == 28) { // 超时
                throw new \RuntimeException(\Yii::t("common", "Failed to get data, please try again later"), $exception->getCode());
            } else {
                throw $exception;
            }
        }
    }


	public function getUserConfigTags()
	{
		$api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
		$data = $this->innerCall($api, 'userConfigTags', $this->getParams());
		if (!$data) {
			return [];
		}
		return $data;
	}

	public function getMainProductConfig()
	{
		$api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
		$data = $this->innerCall($api, 'getMainProductConfig', $this->getParams());
		if (!$data) {
			return [];
		}
		return $data;
	}


	public function getMaContacts()
	{
		$api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
		$data = $this->innerCall($api, 'maContacts', $this->getParams());
		if (!$data) {
			return [];
		}
		return $data;
	}


	public function saveMainProductConfig()
	{
		$api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
		$data = $this->innerCall($api, 'mainProductConfig', $this->getParams());
		if (!$data) {
			return [];
		}
		return $data;
	}

    public function getMailQualityRating(array $emails)
    {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $data = $this->innerCall($api, 'getMailQualityRating', array_merge($this->getParams(), ['emails' => $emails]));
        if (!$data) {
            return [];
        }

        return $data;
    }

    public function resetAiBackgroundCount($userId, $email, $company = '') {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $params = [
            'user_id' => $userId,
            'params' => [
                'contacts_way' => $email,
                'company_name' => $company,
            ]
        ];
        if (empty($company)) {
            unset($params['params']['company_name']);
        }
        $result = $this->innerCall($api, 'resetAiBackgroundCount', $params);
        return $result['data']??false;
    }

    public function getMatchCompanyByProfile($matchType, $industryIds = [], $industryProducts = [], $beforePortraitIds = null, $excludeDomains = [], $pageSize = 100) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_GET);
        $params = [
            'match_type' => $matchType,
            'match_industry_ids' => $industryIds,
            'match_product_ids' => $industryProducts,
            'page_size' => $pageSize,
        ];
        if ($beforePortraitIds) {
            $params['before_portrait_id'] = $beforePortraitIds;
        }
        if (!empty($excludeDomains)) {
            $params['exclude_domains'] = $excludeDomains;
        }
        $api->setTimeout(10);
        $data = $this->innerCall($api, 'getMatchCompanyByProfile', $params);
        if (!$data) {
            return [];
        }
        return $data;
    }

    public function getCompanyProfileByDomains(array $domains) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $params = [
            'domains' => $domains,
        ];
        $api->setTimeout(10);
        $data = $this->innerCall($api, 'getBuyerPortrait', $params);
        if (!$data) {
            return [];
        }
        return $data;
    }

    public function resetDailyCount($prefix, $userId) {
        $api = $this->innerApiFactory($this->serviceName, InnerApi::HTTP_METHOD_POST);
        $params = [
            'type' => $prefix,
            'userId' => $userId,
        ];
        $data = $this->innerCall($api, 'resetDailyCount', $params);
        if (!$data) {
            return [];
        }
        return $data;
    }

}