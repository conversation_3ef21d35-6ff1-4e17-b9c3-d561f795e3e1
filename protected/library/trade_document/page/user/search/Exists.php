<?php
/**
 *
 * Author: ruise<PERSON><PERSON>
 * Date: 2022/3/24
 */

namespace common\library\trade_document\page\user\search;

use common\library\product_v2\search\queries\AbstractQuery;

class Exists extends AbstractQuery
{
    public function compile($column)
    {
        if (strpos($column,'exists_') === 0) {
            $column = substr($column, 7);
        }

        return parent::compile($column);
    }


    public function clause($column)
    {
        return [[ $this->getOperate() => ['field'  => $column]]];
    }

    public function getOperate()
    {
        return "exists";
    }

}