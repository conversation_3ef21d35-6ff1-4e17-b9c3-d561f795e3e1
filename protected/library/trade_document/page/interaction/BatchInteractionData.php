<?php

/**
 * This file is part of php-crm.
 *
 * <AUTHOR> <<EMAIL>>
 * @created_at  2022/03/09 15:11:36
 */

namespace common\library\trade_document\page\interaction;

use xiaoman\orm\common\BatchObject;

/**
 * @method InteractionDataFilter getFilter()
 * @method InteractionDataFormatter getFormatter()
 * @method InteractionDataOperator getOperator()
 */
class BatchInteractionData extends BatchObject
{
    public static function getMetadataClass()
    {
        return InteractionDataMetadata::class;
    }
}
