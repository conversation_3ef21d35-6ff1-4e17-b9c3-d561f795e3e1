<?php


use common\library\account\Client;
use common\library\custom_field\CustomFieldService;
use common\library\customer_v3\company\orm\CompanyMetadata;
use common\library\history\customer\BatchCompanyBuilder;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\stage\Stage;
use common\library\workflow\WorkflowConstant;
use common\library\cash_collection\CashCollection;
use common\models\prometheus\RuleFrequency;
use common\library\report\sensors\events\EventWorkflowRunCountPush;
use common\library\account\service\DbService;

class WorkflowCommand extends CrontabCommand
{

    // 修复历史规则存在错误字段replied_flag
    // php ./yiic-test workflow fixRepliedFlag --dryRun=1 --client_id=9650
    public function actionFixRepliedFlag($dryRun = 1, $client_id = '')
    {
        if (empty($client_id)) {
            $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0, null, 0, 0, 0, true), 'client_id');
        } else {
            $clientIds = explode(',', $client_id);
            if (empty($clientIds)) {
                return;
            }
        }
        $total = count($clientIds);
        $records = [];
        foreach ($clientIds as $i => $clientId) {
            echo ($i+1).'/'.$total.PHP_EOL;
            $db = PgActiveRecord::getDbByClientId($clientId);
            try {
                $sql = "select * from tbl_workflow_rule where refer_type = 6 and filters::text like '%replied_flag%'";
                $list = $db->createCommand($sql)->queryAll();
                foreach ($list as $ruleInfo) {
                    self::info("actionFixRepliedFlag|{$ruleInfo['rule_id']}|".$ruleInfo['filters'] ?? '');
                    echo $clientId.'|rule_id:'.$ruleInfo['rule_id'].PHP_EOL;
                    $filters = json_decode($ruleInfo['filters'], true);
                    foreach ($filters as $index => $item) {
                        if (!empty($item['field']) && $item['field'] == 'replied_flag') {
                            $filters[$index]['field'] = 'reply_flag';
                        }
                    }
                    $records[] = ['client_id' => $clientId, 'rule_id' => $ruleInfo['rule_id'], 'name' => $ruleInfo['name']];
                    $filtersStr = json_encode($filters);
                    $updateSql = "update tbl_workflow_rule set filters='{$filtersStr}' where rule_id={$ruleInfo['rule_id']}";
                    if (empty($dryRun)) {
                        $ruleInfo['rule_id'] && $db->createCommand($updateSql)->execute();
                    }
                }
            } catch (\Throwable $t) {

            } finally {
                \common\library\account\Client::cleanCacheMap($clientId);
                PgActiveRecord::releaseDbByClientId($clientId);
            }
        }
        self::info("actionFixRepliedFlag_end|".json_encode($records));
        print_r($records);
    }

    // 工作流历史规则迁移time_setting
    // php ./yiic-test workflow historyRuleTimeSetting --client_id=9650 --rule_id=**********
    public function actionHistoryRuleTimeSetting($client_id = '', $rule_id = 0)
    {
        if (!empty($client_id)) {
            $clientIds = explode(',', $client_id);
            if (empty($clientIds)) {
                return;
            }
        } else {
            $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0, null, 0, 0, 0, true), 'client_id');
        }
        $getWorkRules = function ($db, $rule_id = 0) {
            $ruleId = 0;
            $size   = 500;
            $workRuleTotalSql = "select count(*) from tbl_workflow_rule where rule_type = 1 and trigger_type = 3 and enable_flag = 1";
            if (!empty($rule_id)) {
                $workRuleTotalSql .= " and rule_id = {$rule_id}";
            }
            $total = $db->createCommand($workRuleTotalSql)->queryScalar();
            if ($total == 0) {
                return;
            }
            $index = intval(ceil($total/$size));
            for ($i = 0; $i <= $index; $i++) {
                $workRuleSql = "select * from tbl_workflow_rule where rule_type = 1 and trigger_type = 3 and enable_flag = 1 ";
                if (!empty($rule_id)) {
                    $workRuleSql .= " and rule_id = {$rule_id}";
                }
                $workRuleSql .= " and rule_id > {$ruleId} order by rule_id asc limit {$size}";
                $list = $db->createCommand($workRuleSql)->queryAll();
                foreach ($list as $item) {
                    $ruleId = $item['rule_id'];
                    yield $item;
                }
            }
        };
        $newTimeSetting = json_decode('{"regular_type":1,"cycle_type":2,"cycle_value":[],"start_date":{"date_type":1, "value":"", "refer_type":"", "field":"", "field_type":"", "operator":""},"end_date":{"date_type":1, "value":"", "refer_type":"", "field":"", "field_type":"", "operator":""}}', true);
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            try {
                foreach ($getWorkRules($db, $rule_id) as $ruleInfo) {
                    $ruleId = $ruleInfo['rule_id'];
                    try {
                        $timeSetting = json_decode($ruleInfo['time_setting'] ?? '{}', true);
                        $startDate = $ruleInfo['start_date'];
                        $endDate = $ruleInfo['end_date'];
                        $updateTime = $ruleInfo['update_time'];
                        $needTran = false;
                        if (empty($timeSetting)) {
                            $needTran = true;
                        } else if ($timeSetting['regular_type'] == WorkflowConstant::REGULAR_TYPE_REPEAT && $timeSetting['cycle_type'] == WorkflowConstant::CYCLE_TYPE_DAY) {
                            if (($timeSetting['start_date']['value'] ?? '') != $startDate
                                || ($timeSetting['end_date']['value'] ?? '') != $endDate) {
                                $needTran = true;
                            }
                        }
                        if (!$needTran) {
                            continue;
                        }
                        $newTimeSetting['start_date']['value'] = $startDate;
                        $newTimeSetting['end_date']['value'] = $endDate;
                        $updateTimeSetting = json_encode($newTimeSetting);
                        $db->createCommand("update tbl_workflow_rule set time_setting='{$updateTimeSetting}',update_time='{$updateTime}' where rule_id={$ruleId}")->execute();
                    } catch (\Throwable $t) {
                        \LogUtil::error('actionHistoryRuleTimeSetting', [
                            'rule_id' => $ruleInfo['rule_id'],
                            'client_id' => $ruleInfo['client_id'],
                            'message' => $t->getMessage(),
                            'line' => $t->getLine(),
                        ]);
                    }
                }
            } catch (\Throwable $t) {

            } finally {
                \common\library\account\Client::cleanCacheMap($clientId);
                PgActiveRecord::releaseDbByClientId($clientId);
            }
        }
    }

    // 统计企业工作流每日执行次数
    // php ./yiic-test workflow ruleRunCount
    public function actionRuleRunCount()
    {
        $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0, null, 0, 0, 0, true), 'client_id');
        // 日期、client_id、rule_id、run_count（只有>0才计入）
        $ruleType = WorkflowConstant::RULE_TYPE_WORKFLOW;
        $ruleFrequencyTableName = RuleFrequency::model()->tableName();
        $workflowRuleTableName = WorkflowRule::model()->tableName();
        $nowDate = date('Y-m-d');
        $lastDate = date('Y-m-d', strtotime('-1 day'));
        $frequencyDb = RuleFrequency::model()->getDbConnection();
        if (empty($frequencyDb)) {
            return false;
        }
        \LogUtil::info("actionRuleRunCount - {$nowDate} - {$lastDate} start");
        foreach ($clientIds as $clientId) {
            $clientInfo = \common\library\account\Client::getClient($clientId);
            if ($clientInfo->isNew()) {
                continue;
            }
            // 过滤外部企业，内部企业的不需要
            if ($clientInfo->client_type != 1) {
                \common\library\account\Client::cleanCacheMap($clientId);
                continue;
            }
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) continue;
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            $report = new EventWorkflowRunCountPush($clientId, $adminUserId);
            try {
                $insertList = $reportData = [];
                // 今天工作流统计
                $sql = "select rule_id,run_count,refer_type,trigger_type from {$workflowRuleTableName} where client_id={$clientId} and rule_type = {$ruleType} and enable_flag=1 and disable_flag = 0";
                $newRuleFrequencyList = $db->createCommand($sql)->queryAll();
                $newRuleFrequencyList = array_column($newRuleFrequencyList, null, 'rule_id');
                $lastDaySql = "select rule_id,sum_count from {$ruleFrequencyTableName} where client_id={$clientId} and `date` ='{$lastDate}'";
                // 昨天工作流统计
                $lastRuleFrequencyList = $frequencyDb->createCommand($lastDaySql)->queryAll();
                $lastRuleFrequencyList = array_column($lastRuleFrequencyList, 'sum_count', 'rule_id');
                // 组装数据
                foreach ($newRuleFrequencyList as $ruleId => $ruleItem) {
                    $addCount = 0;
                    if (isset($lastRuleFrequencyList[$ruleId])) {
                        $addCount = $ruleItem['run_count'] - $lastRuleFrequencyList[$ruleId];
                    }
                    $_insert = [
                        'client_id' => $clientId,
                        'rule_id' => $ruleId,
                        'sum_count' => $ruleItem['run_count'],
                        'add_count' => $addCount,
                        'refer_type' => $ruleItem['refer_type'],
                        'trigger_type' => $ruleItem['trigger_type'],
                        'count_date' => $nowDate,
                        'date' => $nowDate,
                    ];;
                    $insertList[] = $_insert;
                    // 神策关键字date，需要换下
                    unset($_insert['date']);
                    $reportData[] = $_insert;
                }

                // 数据入库
                if (empty($insertList)) {
                    continue;
                }
                $insetSqlField = "insert into {$ruleFrequencyTableName} (`client_id`, `rule_id`, `sum_count`, `add_count`, `date`, `refer_type`, `trigger_type`) values ";
                $insetSqlArr = [];
                foreach ($insertList as $insertItem) {
                    $insetSqlArr[]  = "({$insertItem['client_id']}, {$insertItem['rule_id']}, {$insertItem['sum_count']}, {$insertItem['add_count']}, '{$insertItem['date']}', {$insertItem['refer_type']}, {$insertItem['trigger_type']})";
                }
                // 若有今天的，删除掉，重复运行的情况下
                $delSql = "delete from {$ruleFrequencyTableName} where client_id={$clientId} and `date`='{$nowDate}'";
                $frequencyDb->createCommand($delSql)->execute();
                $insertSql = $insetSqlField.implode(',',$insetSqlArr);
                $frequencyDb->createCommand($insertSql)->execute();
                foreach ($reportData as $reportDatum) {
                    $report->setParams($reportDatum);
                    $report->report();
                }
            } catch (\Throwable $t) {
                \LogUtil::error("actionRuleRunCount 其他报错（{$t->getMessage()}）");
            } finally {
                \common\library\account\Client::cleanCacheMap($clientId);
                PgActiveRecord::releaseDbByClientId($clientId);
            }
        }
        // 删除之前的
        try {
            $delLastSql = "delete from {$ruleFrequencyTableName} where `date`<'{$lastDate}'";
            $frequencyDb->createCommand($delLastSql)->execute();
        } catch (\Throwable $t) {}
        \LogUtil::info("actionRuleRunCount - {$nowDate} - {$lastDate} end");
    }

    public function actionInitTemplate()
    {
        $data = [
            [
                'id'            => 701,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '线索自动轮流分配',
                'description'   => '将新建的线索自动轮流分配给多个业务员',
                'refer_type'    => \Constants::TYPE_LEAD,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_CREATE,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value'      => '',
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'field'  => "user_id",
                                'value'  => [
                                    'user_id' => [],
                                ],
                            ]
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 401,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '公海客户自动盘活',
                'description'   => '将久未联系的公海客户自动轮流分配给多个业务员',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'value'      => '',
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'order_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => -30,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'field'  => "user_id",
                                'value'  => [
                                    'user_id' => [],
                                ],
                            ]
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 201,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '订单回款邮件通知',
                'description'   => '当订单回款完成时，自动发送邮件通知相关人员',
                'refer_type'    => \Constants::TYPE_ORDER,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => CashCollection::FIELD_STATUS,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value'      => CashCollection::COLLECTION_STATUS_FINISH,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => CashCollection::FIELD_STATUS,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value'      => '',
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '订单回款完成通知 -【{{order_no}}】{{name}}',
                            'content'        => "订单：【{{order_no}}】{{name}}回款完成<br/>客户：{{company_name}}<br/>订单金额：{{amount}} {{currency}}<br/>已回款金额：{{cash_collection_collect_amount}} {{currency}}",
                            'user_field'     => [
                                'users',
                                'handler',
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 402,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '重要客户跟进提醒',
                'description'   => '当重要客户的未联系天数>30天时，自动给跟进人发送跟进提醒',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'star',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value'      => 5,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'order_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => -30,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'subject'       => '重要客户跟进提醒',
                            'content'       => "客户：{{name}}<br/>您已超过30天未联系该客户，请及时进行跟进。",
                            'user_field'    => [
                                'user_id',
                            ],
                            'addition_user' => [],
                            'channel'       => [
                                'email'   => 0,
                                'message' => 1,
                                'app'     => 1,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 901,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '滞留商机跟进提醒',
                'description'   => '提醒商机团队成员久未跟进的商机',
                'refer_type'    => \Constants::TYPE_OPPORTUNITY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'stage_stay_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value'      => 15,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'value'      => [
                            Stage::STAGE_WIN_STATUS,
                            Stage::STAGE_FAIL_STATUS
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'subject'       => '滞留商机跟进提醒',
                            'content'       => "商机：【{{serial_id}}】{{name}} 停留在 {{stage}} 销售阶段{{stage_stay_time}}天了，请及时跟进。",
                            'user_field'    => [
                                'handler',
                                'create_user',
                            ],
                            'addition_user' => [],
                            'channel'       => [
                                'email'   => 0,
                                'message' => 1,
                                'app'     => 1,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 202,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '订单结单日期将近提醒',
                'description'   => '提醒订单的业绩归属人结单日期将近，需尽快跟进',
                'refer_type'    => \Constants::TYPE_ORDER,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'account_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => 7,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'account_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_LATER,
                        'value'      => 0,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'subject'       => '订单结单日期将近，请及时跟进',
                            'content'       => "订单：【{{order_no}}】{{name}} 的结单日期为{{account_date}}，请及时跟进。",
                            'user_field'    => [
                                'users',
                                'handler',
                            ],
                            'addition_user' => [],
                            'channel'       => [
                                'email'   => 0,
                                'message' => 1,
                                'app'     => 1,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 403,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '自动识别高危客户',
                'description'   => '对于三个月未成交的客户，自动修改其客户分组为“高危客户”',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'deal_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => -90,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'group_id',
                                'value' => 0,
                            ]
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 404,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '久未成交客户自动移入公海',
                'description'   => '将四个月未成交的客户自动移入公海',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'value'      => '',
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_MOVE_TO_POOL,
                        'config' => [
                            'field'      => 'deal_time',
                            'value'      => 120,
                            'notify_day' => 7,
                        ],
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 702,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '线索自动移入公海',
                'description'   => '将一周未跟进的线索自动移入公海',
                'refer_type'    => \Constants::TYPE_LEAD,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'value'      => '',
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_MOVE_TO_POOL,
                        'config' => [
                            'field'      => 'order_time',
                            'value'      => 7,
                            'notify_day' => 7,
                        ],
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 405,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '自动识别长期未联系客户',
                'description'   => '对于一个月没有联系的客户，自动修改其客户分组为“长期未联系客户”',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'order_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => -30,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'group_id',
                                'value' => 0,
                            ]
                        ],
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 406,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '根据地区自动分组客户',
                'description'   => '对于国家为美国、加拿大的客户，为其自动分组',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'country',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value'      => ['US', 'CA'],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'group_id',
                                'value' => 0,
                            ]
                        ],
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 703,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '根据国家地区分配线索',
                'description'   => '来自美国地区的线索自动分配给负责美国客户的业务员',
                'refer_type'    => \Constants::TYPE_LEAD,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'country',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value'      => ['US'],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_REPLACE,
                                'field'  => "user_id",
                                'value'  => [
                                    'user_id' => [],
                                ],
                            ]
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 407,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '分配指定公海客户',
                'description'   => '若取消了某个国家的业务，自动把相关的客户资源排除在分配之外，其他的公海客户继续进行分配',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'value'      => '',
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'country',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'value'      => [],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'field'  => "user_id",
                                'value'  => [
                                    'user_id' => [],
                                ],
                            ]
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 902,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '商机赢单邮件通知',
                'description'   => '商机赢单后发送邮件通知指定人员',
                'refer_type'    => \Constants::TYPE_OPPORTUNITY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value'      => '',
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value'      => Stage::STAGE_WIN_STATUS,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '商机赢单通知-【{{serial_id}}】{{name}}',
                            'content'        => "【{{serial_id}}】{{name}} 商机已赢单。销售金额：{{amount}}",
                            'user_field'     => [
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 903,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '创建大额商机-邮件通知',
                'description'   => '创建大额商机，自动发送邮件通知给领导，引起重视',
                'refer_type'    => \Constants::TYPE_OPPORTUNITY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value'      => 0,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '大额商机创建通知-【{{serial_id}}】{{name}}',
                            'content'        => "大额商机【{{serial_id}}】{{name}}<br/>销售金额：{{amount}}{{currency}}<br/>创建人：{{create_user}}<br/>负责人：{{main_user}}<br/>团队成员：{{handler}}",
                            'user_field'     => [
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 301,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '创建大额报价单-邮件通知',
                'description'   => '创建大额报价单，自动发送邮件通知给相关人员，引起重视',
                'refer_type'    => \Constants::TYPE_QUOTATION,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value'      => 0,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '大额报价单创建通知-【{{quotation_no}}】{{name}}',
                            'content'        => "大额报价单：【{{quotation_no}}】{{name}}<br/>报价单金额：{{amount}}{{currency}}<br/>创建人：{{create_user}}",
                            'user_field'     => [
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 904,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '商机超过结单日期-邮件通知',
                'description'   => '商机超过结单日期，自动发送邮件通知给相关人员及时进行跟进',
                'refer_type'    => \Constants::TYPE_OPPORTUNITY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'account_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_LATER,
                        'value'      => 0,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'value'      => [
                            Stage::STAGE_WIN_STATUS,
                            Stage::STAGE_FAIL_STATUS
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '商机超过结单日期通知-【{{serial_id}}】{{name}}',
                            'content'        => "商机【{{serial_id}}】{{name}}<br/>销售阶段：{{stage_type}}<br/>结单日期：{{account_date}}<br/>销售金额：{{amount}} {{currency}}<br/>创建人：{{create_user}}<br/>负责人：{{main_user}}<br/>团队成员：{{handler}}",
                            'user_field'     => [
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 203,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '即将结单订单待回款提醒',
                'description'   => '临近结单日期的订单，尾款未收齐，自动发送邮件通知给相关人员进行跟进',
                'refer_type'    => \Constants::TYPE_ORDER,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'account_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => 3,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => CashCollection::FIELD_STATUS,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                        'value'      => CashCollection::COLLECTION_STATUS_FINISH,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1 AND 2',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '即将结单订单待回款提醒-【{{order_no}}】{{name}}',
                            'content'        => "订单：【{{order_no}}】{{name}}临近结单日期，请及时跟进回款情况。<br/>客户：{{company_name}}<br/>订单金额：{{amount}} {{currency}}<br/>已回款金额：{{cash_collection_collect_amount}} {{currency}}<br/>结单日期：{{account_date}}",
                            'user_field'     => [
                                'users',
                                'handler',
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 408,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name'          => '公海客户自动分配给指定人员',
                'description'   => '将公海客户自动分配给指定人员',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'value'      => '',
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'field'  => "user_id",
                                'value'  => [
                                    'user_id' => [],
                                ],
                            ]
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id'            => 409,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '客户即将移入公海提醒',
                'description'   => '客户移入公海前3天进行系统通知',
                'refer_type'    => \Constants::TYPE_COMPANY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'order_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => -27,
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'subject'       => '客户即将移入公海提醒',
                            'content'       => "客户：{{name}} 即将移入公海，请及时进行跟进。",
                            'user_field'    => [
                                'user_id',
                            ],
                            'addition_user' => [],
                            'channel'       => [
                                'email'   => 0,
                                'message' => 1,
                                'app'     => 1,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],
            [
                'id'            => 905,
                'group_id'      => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name'          => '样机回收-邮件提醒',
                'description'   => '商机已结单，但样机未归还，发送邮件提醒相关人员进行跟进',
                'refer_type'    => \Constants::TYPE_OPPORTUNITY,
                'trigger_type'  => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters'       => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'value'      => [],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria'      => '1',
                'handlers'      => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject'        => '',
                            'content'        => "",
                            'user_field'     => [
                            ],
                            'user_email'     => [],
                            'addition_email' => [],
                            'channel'        => [
                                'email'   => 1,
                                'message' => 0,
                                'app'     => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'icon'          => 1,
            ],

            [
                'id' => 410,
                'group_id' => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name' => '高级公海规则',
                'description' => '将断约客户自动移入公海',
                'refer_type' => Constants::TYPE_COMPANY,
                'trigger_type' => \common\library\workflow\WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters' => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'value'      => []
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'success_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value'      => 0
                    ],
                ],
                'handlers' => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_MOVE_TO_POOL,
                        'config' => [
                            'field' => 'deal_time',
                            'value' => 30,
                            'notify_day' => 7,
                        ]
                    ],
                ],
                'criteria_type' => \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
            ],
            [
                'id' => 411,
                'group_id' => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name' => '自动评定5星用户',
                'description' => '根据所定义的条件，让系统自动帮助您评出优质客户。',
                'refer_type' => Constants::TYPE_COMPANY,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'filters' => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'value'      => 0
                    ],
                ],
                'handlers' => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'star',
                                'value' => 5,
                            ]
                        ],
                    ]
                ],
                'criteria_type' => \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 2,
            ],
            [
                'id' => 412,
                'group_id' => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name' => '自动变更客户阶段',
                'description' => '如果向客户发送了关键邮件，则自动变更客户的阶段。',
                'refer_type' => Constants::TYPE_COMPANY,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters' => [],
                'handlers' => [],
                'criteria_type' => \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '',
                'icon' => 3,
                'enable_flag' => 0
            ],
            [
                'id' => 413,
                'group_id' => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_COMPANY,
                'name' => '客户自动分组',
                'description' => '每日检查，自动将客户根据条件分配到特定的分组。比如，按照赢单商机数进行客户分组，自动将客户分为不同等级分组',
                'refer_type' => Constants::TYPE_COMPANY,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters' => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'value'      => 0
                    ],
                ],
                'handlers' => [[
                    'type'   => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                    'config' => [
                        [
                            'field' => 'group_id',
                            'value' => '',
                        ]
                    ],
                ]],
                'criteria_type' => \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 4,
            ],
            [
                'id' => 906,
                'group_id' => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_CPQ,
                'name' => '汇报进行中的大额商机',
                'description' => '将断约客户自动移入公海。',
                'refer_type' => Constants::TYPE_OPPORTUNITY,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters' => [],
                'handlers' => [],
                'criteria_type' => \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '',
                'icon' => 5,
                'enable_flag' => 0,
            ],
            [
                'id' => 907,
                'group_id' => \common\library\workflow\WorkflowConstant::WORKFLOW_RULE_TEMPLATE_GROUP_FOLLOWUP,
                'name' => '大额商机结束前系统通知',
                'description' => '大额商机的结单日期前一周，自动发送系统通知提醒相关人员进行跟进',
                'refer_type' => Constants::TYPE_OPPORTUNITY,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'filters' => [
                    [
                        'filter_no'  => 1,
                        'field'      => 'account_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'value'      => 7,
                    ],
                    [
                        'filter_no'  => 2,
                        'field'      => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value'      => 0,
                    ]
                ],
                'handlers' => [
                    [
                        'type'   => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'subject'       => '大额商机即将结单，请及时跟进',
                            'content'       => "商机【{{serial_id}}】{{name}}<br/>销售阶段：{{stage}}<br/>结单日期：{{account_date}}<br/>销售金额：{{amount}} {{currency}}<br/>创建人：{{create_user}}<br/>负责人：{{main_user}}<br/>团队成员：{{handler}}",
                            'user_field'    => [
                                'main_user',
                                'handler',
                            ],
                            'addition_user' => [],
                            'channel'       => [
                                'email'   => 0,
                                'message' => 1,
                                'app'     => 1,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'criteria_type' => \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 6,
            ],

        ];

        WorkflowRuleTemplate::model()->deleteAll();
        foreach ($data as $datum) {
            $datum['filters'] = json_encode($datum['filters']);
            $datum['handlers'] = json_encode($datum['handlers']);
            $template = new WorkflowRuleTemplate();
            $template->setAttributes($datum, false);
            $template->enable_flag = $datum['enable_flag'] ?? 1;
            $template->create_time = $template->update_time = date('Y-m-d H:i:s');
            $inserted = $template->save();
        }
    }

    public function actionDaily($clientId)
    {
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW,
        ];
        $clientIds = $db->createCommand($sql)->queryColumn($params);

        if ($clientId) {
            $clientIds = array_intersect($clientIds, explode(',', $clientId));
        }

        foreach ($clientIds as $clientId) {
            \LogUtil::info("start workflow daily for client: $clientId");
            ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
            PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
            (new \common\library\workflow\trigger\DailyTrigger($clientId))->run(false);
        }
    }

    public function actionRunWorkflow($clientId, $ruleId, $referId = '', $dryRun = 1)
    {
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
        $rule = WorkflowRule::model()->findByPk($ruleId);
        $clientId = $rule['client_id'];
        $referIds = array_filter(explode(',', $referId));
        $trigger = new \common\library\workflow\trigger\RuleTrigger($clientId, $ruleId);
        if (count($referIds)) {
            $trigger->setReferIds($referIds);
        }
        $logs = $trigger->run($dryRun, 1000, false);
        /**
         * @var $handler \common\library\workflow\handler\HandlerRunner
         */
        foreach ($logs as $log) {
            echo json_encode(json_decode($log, true), JSON_PRETTY_PRINT) . "\n";
        }
    }

    public function actionRefreshWorkflow($method, $clientId = 0, $greyNum = null)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif (!is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $clientId) {
            if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW)) {
                echo " client $clientId skip without workflow \n";
                continue;
            }
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo " client $clientId skip without db \n";
                continue;
            }

            $sql = "select * from tbl_workflow_rule limit 100000";
            $rules = $db->createCommand($sql)->queryAll($sql);
            $updates = [];
            $params = [];
            foreach ($rules as $rule) {
                $filters = json_decode($rule['filters'] ?? '{}', true);
                $handlers = json_decode($rule['handlers'] ?? '{}', true);
                [$filters, $handlers] = $this->{$method}($rule, $filters, $handlers);
                $filtersSql = json_encode($filters);
                $handlersSql = json_encode($handlers);
                $ruleId = $rule['rule_id'];
                $updates[] = "update tbl_workflow_rule set filters=:{$ruleId}_filters, handlers=:{$ruleId}_handlers where rule_id = $ruleId";
                $params[$ruleId . '_filters'] = $filtersSql;
                $params[$ruleId . '_handlers'] = $handlersSql;
            }
            $count = 0;
            if (count($updates)) {
                $count = $db->createCommand(implode(';', $updates))->execute($params);
            }
            echo "update client $clientId count $count" . PHP_EOL;
        }
    }

    protected function refreshWorkflowAmountUnit($rule, $filters, $handlers)
    {
        $mainCurrency = Client::getClient($rule['client_id'])->getMainCurrency();
        foreach ($filters as &$filter) {
            if (WorkflowConstant::FIELD_UNIT_NAME_CURRENCY == (WorkflowConstant::FIELD_UNIT_MAP[$filter['refer_type']][$filter['field']] ?? '')) {
                $filter['unit'] = $mainCurrency;
            }
        }
        return [$filters, $handlers];
    }

    protected function refreshWorkflowRuleRefer($rule, $filters, $handlers)
    {
        $ruleReferType = $rule['refer_type'];
        foreach ($filters as &$filter) {
            $filter['refer_type'] =  $filter['refer_type'] ?? $ruleReferType;
        }
        foreach ($handlers as &$handler) {
            switch ($handler['type']) {
                case WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD:
                    foreach ($handler['config'] as &$item) {
                        $item['refer_type'] =  $item['refer_type'] ?? $ruleReferType;
                    }
                    break;
                case WorkflowConstant::HANDLER_TYPE_NOTIFY:
                case WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY:
                    $replaceField = function($text, $ruleReferType) {
                        preg_match_all('/{{([0-9a-zA-Z_]*?)}}/', $text, $matches);
                        foreach ($matches[0] ?? [] as $k => $v) {
                            $text = str_replace($v, "{{" . $ruleReferType . '.' . $matches[1][$k] . "}}", $text);
                        }
                        return $text;
                    };
                    $handler['config']['subject'] = $replaceField($handler['config']['subject'], $ruleReferType);
                    $handler['config']['content'] = $replaceField($handler['config']['content'], $ruleReferType);
                    if (isset($handler['config']['user_field'])) {
                        if (is_array($handler['config']['user_field'])) {
                            foreach ($handler['config']['user_field']??[] as &$userField) {
                                if (strpos($userField, '.') === false) {
                                    $userField = "$ruleReferType.$userField";
                                }
                            }
                        }
                    }
                    break;
            }
        }

        return [$filters, $handlers];
    }

    /**
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     *
     * pro 1.5
     */
    public function actionCalWorkflowRunCount()
    {
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW,
        ];
        $clientIds = $db->createCommand($sql)->queryColumn($params);

        foreach ($clientIds as $clientId) {
            $sql = "select rule_id, count(1) from tbl_workflow_log where client_id={$clientId} group by rule_id;";
            $db = PgActiveRecord::getDbByClientId($clientId);
            $countMap = array_column($db->getCommandBuilder()->createSqlCommand($sql)->queryAll(), 'count', 'rule_id');
            foreach ($countMap as $rule_id => $count) {
                \common\library\workflow\Helper::updateRunCount($clientId, $rule_id, $count, true);
            }
        }
    }

    /**
     * pro 1.5
     */
    public function actionInitForPro15($clientId, $greyNum = null)
    {
        $this->actionRefreshWorkflow('initBrowserNotify', $clientId, $greyNum);
        $this->actionRefreshWorkflow('refreshWorkflowAmountUnit', $clientId, $greyNum);
    }

    public function initBrowserNotify($rule, $filters, $handlers)
    {
        foreach ($handlers as &$handler) {
            if ($handler['type'] == WorkflowConstant::HANDLER_TYPE_NOTIFY) {
                $handler['config']['channel']['browser'] = $handler['config']['channel']['browser'] ?? 1;
            }
        }

        return [$filters, $handlers];
    }

    protected function getAllWorkflowClientIds()
    {
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW,
        ];
        return $db->createCommand($sql)->queryColumn($params);
    }

    public function actionStatisticNotify()
    {
        $clientIds = $this->getAllWorkflowClientIds();

        $result = [];
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "select * from tbl_workflow_rule where client_id = $clientId and handlers @> '[{\"type\": \"notify\"}]'";
            $rules = $db->createCommand($sql)->queryAll(true);
            $result[$clientId] = [
                'client_id' => $clientId,
                'client_name' => Client::getClient($clientId)['full_name'],
                'rule_count' => count($rules),
                'enable_push_count' => 0,
                'unread_msg_count' => 0,
                'read_msg_count' => 0,
                'total_msg_count' => 0,
                'before_unread_msg_count' => 0,
                'before_read_msg_count' => 0,
                'before_total_msg_count' => 0,
            ];
            if (!count($rules)) {
                echo "skip for client: $clientId \n";
                continue;
            }
            $userIds = \common\library\account\Helper::getActivationUserIds($clientId);
            $enablePushRuleIds = [];
            foreach ($rules as $rule) {
                $handlers = json_decode($rule['handlers'], true);
                foreach ($handlers as $handler) {
                    if ($handler['type'] == WorkflowConstant::HANDLER_TYPE_NOTIFY) {
                        $enablePush = $handler['config']['channel']['browser'] ?? false;
                        if ($enablePush) {
                            $enablePushRuleIds[] = $rule['rule_id'];
                        }
                    }
                }
            }
            $enablePushRuleIds = array_unique($enablePushRuleIds);
            $result[$clientId]['enable_push_count'] = count($enablePushRuleIds);
            $ruleIds = array_column($rules, 'rule_id');
            foreach ($userIds as $userId) {
                $mgReadSql = "SELECT read_flag, count(1) AS `count` FROM tbl_msg_box WHERE user_id = $userId AND refer_type = 101 and create_time >= '2020-04-16' GROUP BY read_flag;";
                $readCount = array_column($mysqlDb->createCommand($mgReadSql)->queryAll(true), 'count', 'read_flag');
                $result[$clientId]['unread_msg_count'] += $readCount[0] ?? 0;
                $result[$clientId]['read_msg_count'] += $readCount[1] ?? 0;
                $mgReadSql = "SELECT read_flag, count(1) AS `count` FROM tbl_msg_box WHERE user_id = $userId AND refer_type = 101 and create_time < '2020-04-16' GROUP BY read_flag;";
                $readCount = array_column($mysqlDb->createCommand($mgReadSql)->queryAll(true), 'count', 'read_flag');
                $result[$clientId]['before_unread_msg_count'] += $readCount[0] ?? 0;
                $result[$clientId]['before_read_msg_count'] += $readCount[1] ?? 0;
                echo "process for client: $clientId user: $userId \n";
            }
            $result[$clientId]['total_msg_count'] = $result[$clientId]['unread_msg_count'] + $result[$clientId]['read_msg_count'];
            $result[$clientId]['before_total_msg_count'] = $result[$clientId]['before_unread_msg_count'] + $result[$clientId]['before_read_msg_count'];
            echo "finish for client: $clientId \n";
        }
        $resultPath = '/tmp/workflow_notify_statistic.csv';
        $fp = fopen($resultPath, 'w');
        fputcsv($fp, ['client_id', '公司名称', '工作流数量', '开启通知数', '4-16之后消息未读数', '4-16之后消息已读数', '4-16之后消息总数', '4-16之前消息未读数', '4-16之前消息已读数', '4-16之前消息总数']);
        foreach ($result as $value) {
            fputcsv($fp, $value);
        }
        fclose($fp);
        echo json_encode($result, JSON_PRETTY_PRINT);
    }

    public function actionAddAlibabaHookHandler($clientId, $workflowId, $field, $type, $append = false)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        PgActiveRecord::setConnection($db);
        $rule = new \common\library\workflow\WorkflowRule($clientId, $workflowId);
        if ($rule->isNew()) {
            echo "工作流：$workflowId 不存在";
            return false;
        }
        $added = false;
        $handlers = is_array($rule->handlers) ? $rule->handlers : json_decode($rule->handlers, true);
        foreach ($handlers as &$handler) {
            $handlerConfig = [
                'field' => explode(',', $field),
                'type' => $type
            ];
            if ($handler['type'] == WorkflowConstant::HANDLER_TYPE_HOOK_PUSH_ALIBABA) {
                if ($append) {
                    $handler['config'][] = $handlerConfig;
                } else {
                    $handler['config'] = [$handlerConfig];
                }
                $added = true;
                break;
            }
        }
        if (!$added) {
            $handlers[] = [
                'type' => WorkflowConstant::HANDLER_TYPE_HOOK_PUSH_ALIBABA,
                'config' => [
                    $handlerConfig
                ]
            ];
        }
        $rule->handlers = $handlers;
        $rule->save();
    }


    public function actionRecoverMoveToPublic($clientId, $logId, $type, $dryRun = 1)
    {
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        PgActiveRecord::setConnection($pgDb);
        $log = WorkflowLog::model()->findByPk($logId);
        $ruleId = $log['rule_id'];
        $recordIds = \common\library\util\PgsqlUtil::trimArray($log['record']);
        echo "count " . count($recordIds) . '\n';

        $sql = "select distinct on (company_id) * from tbl_company_history where client_id = $clientId and company_id  in (" . implode(',', $recordIds) . ") and type = " . $type . " and update_refer = {$ruleId} and update_type = 3 order by company_id, update_time;";
        $historyList = $pgDb->createCommand($sql)->queryAll();
        $originCompanyUser = array_column($pgDb->createCommand("select company_id, user_id from tbl_company where company_id in (" . implode(',', $recordIds) . ")")->queryAll(), 'user_id', 'company_id');
        $updateSqlList = [];
        $newHistory = [];
        foreach ($historyList as $moveHistory) {
            foreach (json_decode($moveHistory['diff'], true) as $diffItem) {
                if ($diffItem['id'] == 'user_id') {
                    $new = $diffItem['old'];
                    $companyId = $moveHistory['company_id'];
                    $old = \common\library\util\PgsqlUtil::trimArray($originCompanyUser[$companyId] ?? '{}');
                    if (!array_diff($new, $old) && !array_diff($old, $new)) {
                        echo "company $companyId user_id same \n";
                        continue;
                    }
                    $updateSqlList[$companyId] = "update tbl_company set user_id = '" . \common\library\util\PgsqlUtil::formatArray($new) . "', update_time='" . date('Y-m-d H:i:s') . "' where company_id = $companyId";
                    $newHistory[$companyId] = [
                        [
                            'id' => 'user_id',
                            'new' => $new,
                            'old' => $old
                        ]
                    ];
                }
            }
        }
        \LogUtil::info(json_encode($newHistory));
        if (!$dryRun) {
            if (empty($updateSqlList)) {
                echo "nothing change\n";
                return ;
            }
            $count = $pgDb->createCommand(implode(';', $updateSqlList))->execute();
            echo "update count: $count\n";
            
            // 更新scope_user_ids
//            $scopeUserService = new ScopeUserFieldService($clientId, new CompanyMetadata($clientId));
//            $scopeUserService->refreshScopeUserIdsByPids(array_keys($updateSqlList));
            
            $history = new BatchCompanyBuilder();
            $history->setEditInfo(\common\components\BaseObject::FIELD_EDIT_TYPE_BY_NONE, null, $ruleId);
            $userId = 0;
            $history->buildByMap($clientId, $userId, \common\models\client\CompanyHistoryPg::TYPE_ASSIGN, $newHistory);
            \common\library\CommandRunner::run('elasticsearch', 'SyncIndexForClient', [
                'clientId' => $clientId
            ], '/dev/null', 0);
        } else {
            echo json_encode($newHistory, JSON_PRETTY_PRINT);
        }
    }

    public function actionRunWorkflowByReferIds($clientId, $ruleId, $referIds, $dryRun = 1)
    {
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
        $rule = WorkflowRule::model()->findByPk($ruleId);
        $clientId = $rule['client_id'];
        $referIds = array_filter(explode(',', $referIds));

        foreach ($referIds as $referId) {
            $trigger = new \common\library\workflow\trigger\RuleTrigger($clientId, $ruleId);
            $trigger->setReferIds($referId);
            $logs = $trigger->run($dryRun, 1000, false);
            foreach ($logs as $log) {
                echo json_encode(json_decode($log, true), JSON_PRETTY_PRINT) . "\n";
            }
        }
    }

    /**
     * @param $clientId
     * @param int $grey
     * @param null $greyNum
     * @param int $dryRun
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     *
     * feature/may.k7 邮箱通知变更
     */
    public function actionFixEmailNotifyConfig($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW,
        ];
        $allClientIds = $db->createCommand($sql)->queryColumn($params);

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientIds = array_intersect($clientIds, $allClientIds);


        foreach ($clientIds as $clientId) {
            $pg = PgActiveRecord::getDbByClientId($clientId);
            if (!$pg) {
                echo "skip for $clientId \n";
                continue;
            }

            $userAccountMap = array_column($db->createCommand("select user_id,email from tbl_user_info where client_id = {$clientId}")->queryAll(), 'user_id', 'email');
            $rules = $pg->createCommand("select rule_id, handlers from tbl_workflow_rule  where client_id = $clientId and handlers @> '[{\"type\": \"email\"}]'")->queryAll();
            $updateSql = [];
            $updateSqlParams = [];
            $updateMap = [];
            foreach ($rules as $rule) {
                $handlers = json_decode($rule['handlers'], true);
                $needUpdate = false;
                $ruleId = $rule['rule_id'];
                foreach ($handlers as &$handler) {
                    if ($handler['type'] == 'email') {
                        if (empty($handler['config']['addition_user']) && !empty($handler['config']['user_email'])) {
                            foreach ($handler['config']['user_email'] as $email) {
                                if (!empty($userAccountMap[$email])) {
                                    $updateMap[$ruleId][$email] = $handler['config']['addition_user'][] = $userAccountMap[$email];
                                }
                            }
                            $needUpdate = true;
                        }
                    }
                }
                if ($needUpdate) {
                    $updateSql[$ruleId] = "update tbl_workflow_rule set handlers = :handlers_{$ruleId} where rule_id=" . $ruleId;
                    $updateSqlParams[":handlers_{$ruleId}"] = json_encode($handlers);
                }
            }

            $count = count($updateSql);
            if (!empty($updateSql)) {
                if ($dryRun) {
                    print_r($updateMap);
                } else {
                    $count = $pg->createCommand(implode(';', $updateSql))->execute($updateSqlParams);
                }
            }

            echo "update rule for $clientId count: $count \n";
        }
    }

    //  * 0 * * * root /data/codebase/v4_client/protected/yiic-omg workflow RunTaskDailyRule > /dev/null 2>&1
    public function actionRunTaskDailyRule()
    {
        $greyClientIds = [3,28118,5832,14833,23412,18617,18618,6688,18133,18137,18154,18152,6698,18120,18136,19709,43381,7,10,2106,17383,16149,31704,32527,33319,7606,31788,19559,31150,34843,32962,2762,26672,32381,32014,42024,840,4743,36560,6077,49740,49381,42196];
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKBENCH_V2,
        ];
        $clientIds = $db->createCommand($sql)->queryColumn($params);

        $clientIds = array_intersect($clientIds, $greyClientIds);

        foreach ($clientIds as $clientId) {
            \LogUtil::info("start workflow daily for client: $clientId");
            ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
            PgActiveRecord::setConnection(PgActiveRecord::getDbByClientId($clientId));
            $dailyTrigger = new \common\library\workflow\trigger\DailyTrigger($clientId);
            $dailyTrigger->setRuleType(WorkflowConstant::RULE_TYPE_TASK);
            $dailyTrigger->run(false);
        }
    }

    /**
     * @param $clientId
     * @param int $grey
     * @param null $greyNum
     * @param int $dryRun
     * @throws CDbException
     * @throws CException
     * @throws ProcessException
     *
     * bugfix/902.bugfix
     */
    public function actionFixCashCollectionConfig($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW,
        ];
        $allClientIds = $db->createCommand($sql)->queryColumn($params);

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientIds = array_intersect($clientIds, $allClientIds);

        foreach ($clientIds as $clientId) {
            $pg = PgActiveRecord::getDbByClientId($clientId);
            if (!$pg) {
                echo "skip for $clientId \n";
                continue;
            }

            $rules = $pg->createCommand("select rule_id, filters from tbl_workflow_rule  where client_id = $clientId and refer_type = 10 and filters @> '[{\"field\": \"department_id\"}]'")->queryAll();
            $updateSql = [];
            $updateSqlParams = [];
            foreach ($rules as $rule) {
                $filters = json_decode($rule['filters'], true);
                $needUpdate = false;
                $ruleId = $rule['rule_id'];
                foreach ($filters as &$filter) {
                    if ($filter['field'] == 'department_id') {
                        if (!empty($filter['value']) && is_array($filter['value']) && is_array(current($filter['value']))) {
                            $filter['value'] = array_column($filter['value'], 'id');
                            $needUpdate = true;
                        }
                    }
                }
                if ($needUpdate) {
                    $updateSql[$ruleId] = "update tbl_workflow_rule set filters = :filters_{$ruleId} where rule_id=" . $ruleId;
                    $updateSqlParams[":filters_{$ruleId}"] = json_encode($filters);
                }
            }

            $count = count($updateSql);
            if (!empty($updateSql)) {
                if ($dryRun) {
                    print_r($updateSql);
                    print_r($updateSqlParams);
                } else {
                    $count = $pg->createCommand(implode(';', $updateSql))->execute($updateSqlParams);
                }
            }

            echo "update rule for $clientId count: $count \n";
        }
    }

    /**
     * @param $clientId
     * @param int $grey
     * @param null $greyNum
     * @param int $dryRun
     * @throws ProcessException
     * 统计执行修改回款单amount字段的配置个数
     * 2021-11-01 数量：0
     */
    public function actionUpdateCollectionAmountStat($clientId, $grey = 1, $greyNum = null, $dryRun = 1)
    {
        $sql = "select distinct client_id from tbl_client_privilege where enable_flag = 1 and privilege = :privilege";
        /**
         * @var CDbConnection $db
         */
        $db = Yii::app()->account_base_db;
        $params = [
            ':privilege' => \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_WORKFLOW,
        ];
        $allClientIds = $db->createCommand($sql)->queryColumn($params);

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $clientIds = array_intersect($clientIds, $allClientIds);

        $i = 1;
        $clientCount = count($clientIds);

        $countMap = [];
        foreach ($clientIds as $clientId) {

            echo " $clientId $i/$clientCount \n";
            $i++;

            $pg = PgActiveRecord::getDbByClientId($clientId);
            if (!$pg) {
                echo "skip for $clientId \n";
                continue;
            }

            $rules = $pg->createCommand("select rule_id, handlers from tbl_workflow_rule  where client_id = $clientId and handlers @> '[{\"type\": \"field\"}]'")->queryAll();
            $count = 0;
            foreach ($rules as $rule) {
                $handlers = json_decode($rule['handlers'], true);

                foreach ($handlers as $handler) {
                    if ($handler['type'] !== 'field')
                        continue;

                    foreach ($handler['config'] ?? [] as $config) {
                        if ($config['refer_type'] == Constants::TYPE_CASH_COLLECTION && $config['field'] == 'amount')
                            $count++;
                    }
                }
            }

            if (!empty($count)) {
                $countMap[$clientId] = $count;
                echo "$clientId count: $count \n";
            }
        }
    }


    public function actionDisableReceiveRule($clientId, $dryRun = 1)
    {
        ini_set('memory_limit','1500M');
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (!$dryRun) {
                $sql = "update tbl_workflow_rule set disable_flag = 1 where client_id = {$clientId} and trigger_type = 5 and start_date = '2021-10-28' and enable_flag = 1 and disable_flag = 0";
                $count = $db->createCommand($sql)->execute();
                \LogUtil::info("disable count: $count for $clientId");
                echo "disable count: $count for $clientId\n";
            } else {
                $sql = "select  count(1) from tbl_workflow_rule where client_id = {$clientId} and trigger_type = 5 and start_date = '2021-10-28' and enable_flag = 1 and disable_flag = 0";
                $count = $db->createCommand($sql)->queryScalar();
                echo "disable count {$count} for $clientId\n";
            }
        }
    }

    public function actionDebugRunRule($dryRun = 1)
    {
        \User::setLoginUserById(55547001);
        $ruleIds = [
            2699458665559,
            2699463398356
        ];
        $referIds = [
            1714455102988,
            1714452051241
        ];
        $clientId = 74168;
        foreach ($ruleIds as $ruleId) {
            $trigger = new \common\library\workflow\trigger\RuleTrigger($clientId, $ruleId, $fields ?? []);
            $trigger->setReferIds($referIds);
            $logger = $trigger->run($dryRun, 1000, false);
        }
    }

    public function actionRemoveDisableFieldInFilters(int $clientId = 0 , string $disableField = '', int $moveWorkFlow = 0, int $movePerformanceRule = 0, int $dryRun = 1)
    {
        if (empty($disableField)) {
            self::info('disableField 不能为空');
            return;
        }
        if (empty($clientId)) {
            $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
        } else {
            $clientIds = [$clientId];
        }
        $count = 0;
        self::info("disableField={$disableField}");
        foreach ($clientIds as $clientId) {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) {
                continue;
            }
            $sql = "select client_id,rule_id,filters from tbl_workflow_rule where  client_id ={$clientId} and filters!='{}'  and  (ARRAY(select(jsonb_array_elements(filters)#>>'{field}'))::text[] && ARRAY['{$disableField}']::text[])";
            $data = $db->createCommand($sql)->queryAll();
            if (!empty($data)) {
                $count += count($data);
                self::info("client_id={$clientId}, data=" . json_encode($data));
            }
        }
        self::info("RemoveDisableFieldInFilters,count={$count}");
    }

    public function actionWorkflowRollback(int $clientId, int $ruleId, int $opUserId, int $dryRun = 1)
    {
        \User::setLoginUserById($opUserId);;
        $ruleTrigger = new \common\library\workflow\trigger\RollbackTrigger($clientId, $ruleId);
        $ruleTrigger->rollback($dryRun);

        //埋点 https://www.tapd.cn/21404721/prong/stories/view/1121404721001054773&from=worktable_title
        $rule = (new \common\library\workflow\WorkflowRule($clientId, $ruleId))->getAttributes();
        $handlers = [];
        foreach ($rule['handlers'] as $handler)
        {
            $type = $handler['type'] ?? '';
            if (!empty($type)) {
                $handlers[] = $type;
            }
        }
        $params = [
            'rule_id' => $ruleId,
            'handles' => $handlers
        ];
        $event = new \common\library\report\sensors\events\EventWorkflowRollback($clientId, $opUserId);
        $event->setParams($params);
        $event->report();
    }
}
