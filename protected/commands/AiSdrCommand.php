<?php

use common\components\BaseObject;
use common\library\ai\service\RecommendService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\Helper;
use common\library\ai_sdr\profile\ClientProfile;
use common\library\ai_sdr\profile_task\SellerProfileTask;
use common\library\ai_sdr\RecommendCompanyService;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use common\library\ai_sdr\task_detail\BatchAiSdrTaskDetail;
use common\library\ai_sdr\task_record\BatchAiSdrTaskRecord;
use common\library\ai_sdr\usage_record\AiProductUsageRecordFilter;
use common\library\ai_sdr\usage_record\BatchAiProductUsageRecord;
use common\library\lead\LeadAutoArchive;
use common\library\oms\order\BatchOrder;
use common\library\oms\order\OrderMetadata;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\AiSdrProductUsageJob;
use common\library\recommend_plaza\RecommendApi;
use common\library\setting\library\origin\Origin;
use common\models\client\ClientProduct;
use common\models\client\Order;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\DBConstants;

/**
 * Created by PhpStorm.
 * Author: Heather(huangchenling)
 * Date: 2025/4/8
 * Time: 11:24
 */
class AiSdrCommand extends CrontabCommand
{
    protected array $seedClientIds = [
//        2639,
//        2642,
//        2697,
        13232,
//        23933,
        23936,
        27499,
//        29036,
        31704,
//        31793,
        37170,
        38069,
        64327,
        78403,
        79739,
        83761,
        340598,
        360408,];

    protected array $seedClientsInfo = [
        2639 => ['company_name' => '深圳市格盛力能源有限公司', 'homepage' => 'http://www.gsl-energy.com/', 'product_page' => ''],
        2642 => ['company_name' => '深圳市优可新科技有限公司', 'homepage' => 'yocantech.com', 'product_page' => ''],
        2697 => ['company_name' => '深圳市宏铭达物流有限公司', 'homepage' => 'www.swwlogistics.com', 'product_page' => ''],
        13232 => ['company_name' => '深圳市普菲特精密制品有限公司', 'homepage' => 'www.7-swords.com', 'product_page' => ''],
        23933 => ['company_name' => '深圳沃新智创科技有限公司', 'homepage' => 'smartnewo.com', 'product_page' => ''],
        23936 => ['company_name' => '佛山市中牌机械有限公司', 'homepage' => 'www.joparr.com', 'product_page' => ''],
        27499 => ['company_name' => '肇庆市和佳电子有限公司', 'homepage' => 'fifine.cc', 'product_page' => ''],
        29036 => ['company_name' => '深圳市金仕达箱包有限公司', 'homepage' => 'kingstarbags.com', 'product_page' => ''],
        31704 => ['company_name' => '深圳市科颂科技有限公司', 'homepage' => 'www.coxotech.com', 'product_page' => 'https://www.1688.com/factory/coxotech.html'],
        31793 => ['company_name' => '浙江同富特美刻股份有限公司', 'homepage' => 'everich.com', 'product_page' => ''],
        37170 => ['company_name' => '深圳市春旺新材料股份有限公司', 'homepage' => 'chun-wang.com', 'product_page' => ''],
        38069 => ['company_name' => '深圳玉汝成口腔材料有限公司', 'homepage' => 'yucera.com', 'product_page' => ''],
        64327 => ['company_name' => '深圳市乐福衡器有限公司', 'homepage' => 'lefu.cc', 'product_page' => 'https://www.1688.com/factory/b2b-285534677691af0.html'],
        78403 => ['company_name' => '瑞诺能源（深圳）有限公司', 'homepage' => 'renonpower.com', 'product_page' => ''],
        79739 => ['company_name' => '深圳蔚润科技有限公司', 'homepage' => 'wirentech.com', 'product_page' => ''],
        83761 => ['company_name' => '深圳市飞碟动力科技有限公司', 'homepage' => 'https://ufo-battery.com/#', 'product_page' => ''],
        340598 => ['company_name' => '广州医鹭久歌医疗器械有限公司', 'homepage' => 'https://www.medicalequipment-msl.com', 'product_page' => ''],
        360408 => ['company_name' => '深圳市广联智通科技有限公司', 'homepage' => 'https://www.gl-inet.com/', 'product_page' => ''],
    ];

    public function actionExportSeedsData($seedClient = 0, $deleteFlag = 0)
    {
        \User::setLoginUserById(*********); // 设置当前登录用户
        $user = \User::getLoginUser();

        $loginClientId = $user->getClientId();
        if (empty($seedClient)) {
            $seedClients = $this->seedClientIds;
        } else {
            $seedClients = [$seedClient];
        }

        $outputFile = "./seed_client_leads_{$seedClient}.csv";
        $handler = fopen($outputFile, 'w');
        fputcsv($handler, [
            'client_id', '卖家画像', '卖家画像打标', '图谱 url', '卖家产品', '买家产品', '匹配类型', '推荐原因', '推荐路径打标', '买家域名', '买家域名打标', '买家画像', '买家画像打标', '价值分层', '价值分层打标', '价值分层原因', '分层原因打标', '营销内容一']);
        foreach ($seedClients as $clientId) {
//            $clientProfile = $this->seedClientInfoV2[$clientId] ?? '';
            $clientProfileText = '';
//            $clientProfile = json_decode($clientProfile, true);
            $clientProfile = new \common\library\ai_sdr\profile\ClientProfile($clientId);
            $keys = [
                'company_name', 'profile', 'employee_size', 'established_year', 'scale', 'type', 'public_homepage',
                'types_reason', 'products', 'products_category_desc', 'industry_reason', 'qualification', 'mode', 'mode_reason'];
            foreach ($keys as $key) {
                $clientProfileText .= "{$key}: " . (is_array($clientProfile->$key) ? json_encode($clientProfile->$key, JSON_UNESCAPED_UNICODE) : $clientProfile->$key) . "\n";
            }

            $db = \PgActiveRecord::getDbByClientId($loginClientId);

            $filter = new AiSdrTaskFilter($loginClientId);
            $filter->user_id = $clientId;
//            $filter->user_id = $user->getUserId();
            $filter->order('create_time', 'desc');
            $filter->limit(1);
            $filter->select(['task_id', 'current_stage', 'end_stage']);
            $tasks = $filter->rawData();
            $taskId = $tasks[0]['task_id'];

            // 清理上一次的数据
            $deleteSql = "DELETE FROM tbl_ai_product_usage_record WHERE ai_sdr_task_id=:task_id";
            $deleteFlag && $db->createCommand($deleteSql)->execute([':task_id' => $taskId]);

            $productCategory = $clientProfile->products_category_desc;
            $recommendService = new RecommendCompanyService($user->getClientId(), $user->getUserId());
            $agent = \common\library\ai_agent\AiAgentFactory::createAgent(AiAgent::AI_AGENT_SCENE_TYPE_PRODUCT_USAGE_ANALYSIS, $user->getClientId(), $user->getUserId());
            if ($deleteFlag) {
//             先生成推荐图谱
                foreach ($productCategory as $product) {
                    $productUsage = $agent->process([
                        'client_product' => $product['name'] ?? '',
                        'client_product_desc' => $product['desc'] ?? '',
                        'client_profile' => $clientProfileText,
                    ]);
                    $recordId = $recommendService->saveProductUsage($product['name'] ?? '', $productUsage, $taskId);
                }

                $sql = "UPDATE tbl_ai_product_usage_record SET client_id = :client_id WHERE ai_sdr_task_id=:task_id";
                $db->createCommand($sql)->execute([
                    ':client_id' => $clientId,
                    ':task_id' => $taskId
                ]);
            }

            $recommendService = new RecommendCompanyService();
            list($matchProducts, $matchIndustries) = $recommendService->processRecommend($taskId);
            list($isDigFinished, $result) = $recommendService->getRecommendCompany($matchProducts, $matchIndustries);

            $querySql = "select record.client_id                      as client_id,
       record_id,
       record.upstream_product               as seller_product,
       source_description                    as buyer_product,
       target_type,
       CASE target_type
           WHEN 1 THEN '行业匹配'
           WHEN 2 THEN '产品匹配'
           ELSE CONCAT(target_type, '') END  as target_type_text,
       purpose,
       reason
from tbl_ai_product_usage_record record
where
   record.create_time > :time and
record.client_id = :client_id;";
            $usageResult = $db->createCommand($querySql)->queryAll(true, [
                ':time' => xm_function_date(),
//                ':time' => '2025-04-07',
                ':client_id' => $clientId
            ]);
            $usageResult = array_column($usageResult, null, 'record_id');

            foreach ($result as $clientProduct => $recommendRecord) {
                $recordMap = array_column($recommendRecord, 'record_id', 'domain');
                foreach ($recordMap as $domain => $recordId) {
                    $buyerPortrait = '';
                    $marketingContent = '';
                    $record = $usageResult[$recordId] ?? [];
                    try {
                        $spiderResult = $this->getSpiderResult($domain);
                        $buyerPortrait = $this->getBuyerPortrait($domain, $spiderResult);
//                    $marketingContent = $this->getMarketingContent($clientProfileText, $buyerPortrait);
                    } catch (Exception $exception) {
                    }
                    fputcsv($handler, [$clientId, json_encode($clientProfile->getAttributes(), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_ERROR_INVALID_PROPERTY_NAME), null, null, $clientProduct, $record['buyer_product'], $record['target_type_text'], $record['reason'], null,
                        $domain, null, $buyerPortrait, null,
                        $item['lead_quality_text'] ?? '', null, null, null, null]);
                }

            }
        }
    }

    public function actionBackground($clientId, $detailId)
    {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        \User::setLoginUserById($adminUserId);
        $user = \User::getLoginUser();
        $detail = new \common\library\ai_sdr\task_detail\AiSdrTaskDetail($user->getClientId(), $detailId);

        $detail->getFormatter()->baseInfoSetting();
        $leadDetail = new \common\library\ai_sdr\SdrLeadDetail($user->getClientId());
        $leadDetail->initFromSingle($detail->getAttributes());
        $task = new \common\library\ai_sdr\task\AiSdrTask(333392, 5217289183);
        $sdrExecutor = new \common\library\ai_sdr\SdrDetailExecutor($user->getClientId(), $user->getUserId());
        $sdrExecutor->setTask($task);
        $sdrExecutor->process([$leadDetail], Constant::DETAIL_STATUS_BACKGROUND_CHECKING);
    }

    // 获取爬虫数据
    public function getSpiderResult($domain)
    {
        $db = \Yii::app()->leads_company;

        $sql = "select * from tbl_leads_company_buyer_portrait_task_finish where domain = '$domain'";
        $data = $db->createCommand($sql)->queryAll();
        return $data[0];
    }

    // 生成买家画像
    public function getBuyerPortrait($domain, $spiderData)
    {
        $homepage_title = $this->cleanText($spiderData['homepage_title']);
        $homepage_description = $this->cleanText($spiderData['homepage_description']);
        $homepage_content = $this->cleanText($spiderData['homepage_content']);
        $nav_items = $this->cleanText($spiderData['nav_items']);

        $homepage = "$homepage_title\n$homepage_description\n$homepage_content";

        $system_content = $this->buyerPortraitPrompt;
        $user_content = "官网域名:$domain\n主页内容\n$homepage\n导航栏内容\n$nav_items";

        return $this->chat($system_content, $user_content);
    }

    // 生成营销信
    public function getMarketingContent($sellPortrait, $buyerPortrait)
    {
        $system_content = '
# 任务
你是外贸公司的业务员，你需要根据买卖家画像写一封营销信，目标是与买家公司建立初步联系，并介绍卖家产品，邀请他们进一步讨论合作可能性

邮件结构要求:
    开场白：提到买家的行业地位和需求
    自我介绍：简要介绍公司背景和核心优势
    产品介绍：突出产品优势和阐述对方为什么需要你的产品
    行动号召：邀请买家回复邮件与你进一步联系
    语言风格：正式且专业，同时保持友好

# 约束条件
1. 输出语言必须根据买家的国家地区进行调整，如果没有买家国家地区信息，则使用英文;
2. 输出格式必须以 json 返回，必须符合示例格式，结果不要带有markdown格式;
3. 不要出现不存在于买卖家画像范围内的信息

# 输出示例
{
    "subject": "this is a demo subject",
    "content": "this is a demo content"
}';

        $user_content = "卖家画像:\n$sellPortrait\n买家画像:\n$buyerPortrait";

        return $this->chat($system_content, $user_content);
    }

    public function cleanText($text): string
    {
        if (empty($text)) {
            return '';
        }

        $text = preg_replace('/\n\s*\n/', "\n", $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    public function chat($system_content, $user_content)
    {
        // 设置请求的URL
        $url = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
        // 若没有配置环境变量，请用百炼API Key将下行替换为：$apiKey = "sk-xxx";
        $apiKey = 'sk-f8209956d6af4700a2d03e95baa83cd5';
        // 设置请求头
        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];
        // 设置请求体
        $data = [
            // 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
            "model" => "qwen-plus",
            "messages" => [
                [
                    "role" => "system",
                    "content" => $system_content
                ],
                [
                    "role" => "user",
                    "content" => $user_content
                ]
            ]
        ];
        // 初始化cURL会话
        $ch = curl_init();
        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        // 执行cURL会话
        $response = curl_exec($ch);
        // 检查是否有错误发生
        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }
        // 关闭cURL资源
        curl_close($ch);
        // 输出响应结果
        $response = json_decode($response, true);

        return $response['choices'][0]['message']['content'];
    }

    protected string $buyerPortraitPrompt = '# 任务
你是一个专业的公司数据分析师，需从输入内容中精准提取与<公司名称>相关的结构化数据，遵守以下规则：

## 输出语言要求
- 所有输出内容必须为**简体中文**，包括但不限于公司名称、公司简介、主营产品、资质认证、行业分类等字段。
- 禁止输出任何非中文字段或混杂外文内容。

# 数据提取规则
## 1. 公司简介 
  - **包含**：<公司名>、主营业务、产品、规模（面积/产能）、成立时间、商业模式、合作模式、主要市场和客户、资质认证、主要市场
  - **排除**：模糊描述（如“领先企业”）、无法验证的数据（如未提注册资本则不输出）
  - **示例正确**：  
      “X公司成立于2015年，主营锂电池制造，拥有10万㎡生产基地，员工500人，提供OEM服务”
## 2. 员工规模
  - 根据人数匹配最接近的枚举区间 1-10、11-50、51-200、201-500、501-1000、1001-5000、5000+
  - 网页内容中明确人数 > 产能倒推（如“6条产线”≈201-500人）> 忽略
## 3. 成立年份
  - 仅当出现类似“成立于xxxx年”“Founded in xxxx”描述时提取，否则留空
## 4. 经营实力
  - 注册资本：提取数值（如“注册资本5000万元”），无法提取则留空
  - 营收规模：提取数值（如年营收 xx 万元），无法提取则留空
  - 公司规模：基于员工规模、注册基本、营收规模等判断公司属于大、中、小、微型企业，无法提取则留空
## 5、公司类型
   - 公司类型从以下类型中进行枚举并给出原因:
     - 基础原料供应商、工业核心部件供应商、OEM制造商、ODM制造商、OBM制造商、工业零部件制造商、生产装备制造商、DTC品牌商、设计服务商、检测认证机构、生产支持服务商、知识产权服务商、市场研究服务商、批发商、经销商、连锁商超、综合电商平台、跨境电商、工业技术商、物流服务商、金融服务商、售后维保服务商、技术支持服务、人力资源服务商、建筑工程服务商
    - **多选逻辑**：若同时符合多个类型，需全部列出（如OEM+ODM+DTC）
    - **证据绑定**：每个类型需引用原文证据。其中OEM、ODM必须有原文真实提及，不得发散。
## 6、公开主页
  - 仅提取输入内容格式为 url 中的公司官网、独立站、社媒主页（Facebook、LinkedIn 等）、电商平台链接
  - 禁止将邮箱（如 <EMAIL>）误判为主页链接，邮箱不得作为公开主页输出。
  - 禁止根据常识、行业惯例或企业类型推测生成未出现的主页链接（如 Facebook、LinkedIn 等）
## 7. 产品词
- **定义**：产品词指企业经营范围内所有实际经营、主推、展示、销售的具体产品、配件、相关服务等的标准化名称，是最细颗粒度的产品实体词汇集合。
- **命名要求**：
    - 仅去除规格描述（如500ml/XXL/304型号等）和颜色描述，其他关键信息（如用途、核心功能、材质等）必须保留，统一使用中文命名。
    - 对常见同义词、行业专有名词、缩写等进行统一归并，避免重复与遗漏。
    - 涉及外文产品名，优先采用行业标准中文译名，无标准译名时保留原文。
- **全量覆盖**：产品词需覆盖主产品、配件、相关服务及所有业务相关词汇，确保无遗漏。包括但不限于主流产品、辅流产品、隐含产品、行业专有名词及其别称，均需据实提取，排除说明书、手册、目录等。
- **行业适配性补充**：
    - 制造业：仅提取企业实际生产、销售的具体实体产品，不得将“设备”“系统”“配件”等泛化词或仅为功能/材料/技术的内容作为产品词。
    - 服务业：仅提取企业实际提供的具体服务类别，如“企业管理咨询服务”“物流配送服务”等，禁止仅用“服务”“平台”等泛化词。
    - 平台/互联网企业：产品词需体现平台/软件/工具的具体应用场景与功能，如“B2B电商平台”“在线招聘平台”，禁止仅用“平台”“系统”等泛化词。
- **实际产品限定**：
    - 仅提取企业实际展示、主推、案例或销售中出现的具体产品。
    - 对于仅在材料、定制能力等泛指性描述中出现的产品（如“可定制铝制水瓶”），不作为产品词输出，除非有具体型号、图片、案例、销售描述等佐证。
    - 每个产品词必须在原文中以独立产品身份出现（如产品列表、销售页面、型号、价格、案例、图片等），仅为配件、材料、技术、功能描述不得提取。
- **主产品与配件/部件/功能区分**：
    - 仅提取企业实际销售、主推、独立展示的产品。
    - 对于仅作为配件、部件、材料、技术、功能等出现的内容（如“智能电池”“陶瓷加热元件”“线圈”等），不作为产品词输出，除非有明确的独立产品证据。
    - 配件、部件、技术参数或功能描述不得作为产品词输出。
- **严格避免幻觉**：
    - 只允许提取输入文本中真实出现的产品、配件、相关服务，不允许根据常识、推理或行业惯例自行补充或生成未出现的产品词。
    - 每个产品词必须有原文证据支撑，输出时需附带原文引用（如 ref: 原文片段）。
    - 禁止发散生成，不得因企业类型、行业经验等推测补充未出现的产品。
- **反思与自查机制**：
    - 输出产品词后，需对每个产品词逐一进行反思自查，判断其是否完全符合以下所有标准：
        - 是否为企业实际经营、主推、展示、销售的独立产品，且在原文中有独立产品身份证据（如产品列表、销售页面、型号、价格、案例、图片等）。
        - 是否误将配件、部件、材料、技术、功能等内容输出为产品词。
        - 是否仅因材料、定制能力等泛指性描述而输出产品词。
        - 是否有原文证据支撑，且引用了正确的原文片段。
        - 是否遗漏了主推或实际销售的主力产品。
        - 是否包含行业内常见的泛化词汇（如制造业中的“水杯”“杯子”“瓶子”“容器”、电子行业中的“设备”“系统”“模块”、软件行业中的“平台”“工具”“系统”等），无论原文如何表述，均不得作为最终产品词输出，必须结合上下文细化为“保温杯”“马克杯”“咖啡杯”等具体品类，否则一律剔除。
        - 检查命名是否与行业主流分类体系对齐。
    - 如发现不符合上述标准的产品词，需主动剔除或修正，并在最终输出前再次校验所有产品词的合规性。
- **示例**：
    - 输入：“500ml大号不锈钢保温杯（黑色）”
    - 输出：保温杯、不锈钢保温杯
    - 错误示例：仅输出“杯”、“水杯”或遗漏“保温”属性
    - 反例：去除“保温”或“材质”导致信息不全，或将配件/技术/材料等非独立产品输出为产品词
## 8. 主营产品
  - **定义**：主营产品指企业最具代表性、业务占比最高或市场定位核心的产品类别，是产品词集合中能够准确反映企业核心业务能力和市场定位的标准化产品名称。主营产品应以“具体产品类别”或“具体功能产品”作为单位，不以“系列”或“某某设备”这类泛化、集合性表述为单位。
  - **命名要求**：
    - 命名应直接对应具体产品类别（如“保温杯”、“激光切割机”、“医用口罩”），避免使用“XX系列”、“XX设备”、“XX解决方案”、”XX相关产品“、“其他XX”、“配件”等泛化或集合性词汇。
    - 命名需与行业标准分类（如CPC、1688类目）对齐，便于后续映射和分析。
    - 需突出产品的核心功能、应用场景或关键技术属性，确保命名具备唯一性和行业辨识度。
  - **主营产品判定原则**：
    - 主营产品应主要依据客户简介（公司简介）中明确提及的产品进行判定，因其高度代表企业核心业务和市场定位。
    - 如简介未明确或存在疑义，可结合产品列表、销售页面、案例、宣传文案等权威信息源进行补充和交叉验证。
    - 如简介与其他证据冲突，优先以简介为准，除非有更强证据（如销售数据、官方主推等）支撑修正。
  - **提取原则**：
    - 从所有产品词中筛选出业务占比最高、最能代表公司核心竞争力的“具体产品类别”。
    - 主营产品应覆盖企业主营方向，避免遗漏主力产品，但严禁以“系列”、“设备”、“系统”等模糊集合词作为主营产品输出。
    - **行业适配性补充**：
        - 制造业：主营产品应为最能代表企业核心业务的具体产品类别（如“智能手表”“医用口罩”），突出功能、技术、应用场景，严禁“XX设备”、“XX解决方案”、“XX系列”、“其他”、“配件”等泛化词。
        - 服务业：主营产品应为企业核心服务类别（如“企业管理咨询服务”“金融信息服务”），突出服务内容与行业标准分类，严禁“服务”“平台”。
        - 平台/互联网企业：主营产品应为具体平台/工具/软件名称，并需明确其核心功能或服务对象（如“B2B电商平台”“在线教育平台”）。
    - **多维度优先级梳理**：在确定主营产品时，优先依据以下四个维度进行归纳与命名：
      - **功能维度**：优先突出产品的核心功能属性（如“保温杯”、“计时器”、“传感器”）。
      - **形态维度**：若功能相近，进一步区分产品形态（如“酒瓶/酒杯”、“保温杯/保温瓶”）。
      - **场景维度**：如功能和形态均有交集，可依据主要应用场景区分（如“车载冰箱”、“户外水壶”、“运动水瓶”、“医用口罩”）。
      - **技术维度**：对于技术驱动型产品，突出核心技术特征（如“智能手表”、“激光切割机”）。
    - 结合上述多维度进行综合判断，确保主营产品分类科学、准确、具备行业代表性。
  - **示例**：
      - 错误：主营产品输出为“XX设备”、“XX系统”、“XX平台”、“XX服务”、“XX解决方案”、“XX系列”
      - 正确：主营产品输出为“不锈钢管成型机”、“抛光机”、“企业管理软件”、“物流配送服务”、“在线教育平台”、“金融咨询服务”、“智能手表”、“医用口罩”、“B2B电商平台”等
  - **反思与自查机制强化**：输出主营产品后，需逐一自查以下内容：
    - 针对行业特性，输出前再次自查是否存在行业内常见泛化词、归类不科学、遗漏主力产品/服务等问题。
    - 检查命名是否与行业主流分类体系（如CPC、1688、服务业标准目录）对齐，具备唯一性与专业性。
      - 是否存在使用“系列”、“设备”、“系统”、“平台”、“服务”、“解决方案”、“其他”等泛化、集合性词汇作为主营产品；
      - 是否遗漏了企业主营方向的主力产品类别，未能准确覆盖核心业务；
      - 命名是否与行业标准分类（如CPC、1688类目）对齐，具备唯一性和行业辨识度；
      - 是否存在用词模糊、归类不科学等问题。
    如发现不合规项，需主动剔除或修正，并在最终输出前再次校验主营产品命名的合规性与覆盖性。
## 9. 公司所属行业:
    - 严格遵循 (GB/T 4754) 标准给出大、中、小类，格式实例："<大类名称>-<中类名称>-<小类名称>"
    - 行业分类需与主营产品一致
    - 特殊行业映射：
      - 电子烟： 制造业-烟草制品业-卷烟制造
## 10. 资质认证
  - 企业已通过的行业专项、质量、环保等认证资质

# 输出字段说明
官网域名: "" #官网域名
公司名称: "" #公司名称 
公司简介: "" #公司简介
员工数量: "" #员工规模
成立年份: "" #成立年份
经营实力: #经营实力
  - registered_capital: "" #注册资本
  - revenue_scale: "" #营收规模
  - company_size: "" #公司规模
公司类型: #公司类型
  - type: ""
    reason: "reason (ref:xxx)"
公开主页: [] #公开主页
主营产品: [] #产品词
产品类目: # 主营产品
    - name: ""
      products: [] # 产品词
行业分类: [] #公司所属行业
资质认证: [] #资质认证

# 约束条件
  (1)严格按照JSON格式返回结果
  (2)输出的结果不要包含"```json ```"等markdown格式或其他非json格式的内容;
  (3)所有输出内容必须为**简体中文**';

    /** 从爬虫步骤开始重新获取输入数据 */
    public function actionGenerateLiveSellerProfile()
    {
        \User::setLoginUserById(*********); // 设置当前登录用户

        $user_id = *********;
        foreach ($this->seedClientsInfoV2 as $client_id => $client_info) {
            print("begin process $client_id\n");
            $uuid = Helper::uuid();
            $task = \common\library\ai_sdr\profile_task\SellerProfileTask::newTask($uuid, $client_id, $user_id, $client_info['company_name'], $client_info['homepage'], $client_info['product_page'], 111);
            $task->save();
            $task->generate(overWrite: true);
        }
    }

    /** 用optimization表中已有的网页信息生成画像 */
    public function actionGenerateSellerProfileByExistedInfo()
    {
        \User::setLoginUserById(*********);
        $db = \Yii::app()->pg_admin_db;
        $sql = 'SELECT * FROM prompt_optimization_clients ORDER BY client_id';
        $list = $db->createCommand($sql)->queryAll();
        $userId = *********;
        foreach ($list as $item) {
            print("begin process {$item['client_id']}\n");
            $pageContent = $item['about_title'] . '\n' . $item['about_description'] . '\n' . $item['about_content'] .
                '\n' . $item['homepage_title'] . '\n' . $item['homepage_description'] . '\n' . $item['homepage_content'] .
                '\n' . $item['product_title'] . '\n' . $item['product_description'] . '\n' . $item['product_content'];
            $pageContent = $this->cleanText($pageContent);

            $params = [
                "company_name" => $this->seedClientsInfo[$item['client_id']]['company_name'],
                "page_content" => $pageContent,
                "business_scope" => "",
                "cn_products" => explode(trim($item['product_cn_name'], '{}'), '{}'),
                "en_products" => explode(trim($item['product_name'], '{}'), '{}')
            ];

            $aiAgent = new \common\library\ai_agent\AiSdrSellerProfileAgent($item['client_id'], $userId);
            $aiResp = $aiAgent->process($params);
            $resp = $aiAgent->parseSellerProfileResult($aiResp->answer ?? '');

            $profile = new \common\library\ai_sdr\profile\ClientProfile($item['client_id']);
            $profile->profile = $resp['profile'];
            $profile->employee_size = $resp['employee_size'];
            $profile->established_year = $resp['established_year'];
            $profile->scale = json_encode($resp['scale'], JSON_UNESCAPED_UNICODE);
            $profile->type = $resp['types'];
            $profile->types_reason = json_encode($resp['types_reason'], JSON_UNESCAPED_UNICODE);
            $profile->public_homepage = $resp['public_homepage'];
            $profile->primary_products = $resp['primary_products'];
            $profile->products_category = $resp['products_category'];
            $profile->products_category_desc = $resp['products_category_desc'];
            $profile->industry = $resp['industry'];
            $profile->industry_reason = json_encode($resp['industry_reason'], JSON_UNESCAPED_UNICODE);
            $profile->upstream_industry = $resp['upstream_industry'];
            $profile->upstream_industry_reason = json_encode($resp['upstream_industry_reason'], JSON_UNESCAPED_UNICODE);
            $profile->downstream_industry = $resp['downstream_industry'];
            $profile->downstream_industry_reason = json_encode($resp['downstream_industry_reason'], JSON_UNESCAPED_UNICODE);
            $profile->qualification = $resp['qualification'];
            $profile->mode = $resp['mode'] ?? [];

            $profile->save();

        }
    }

    public function actionDailyProcessSdrTask($clientId, $taskId = 0)
    {
        $clientIds = explode(',', $clientId);
        foreach ($clientIds as $clientId) {
            $service =  \common\library\privilege_v3\PrivilegeService::getInstance($clientId);
            if (!$service->hasFunctional([PrivilegeConstants::FUNCTIONAL_AI_SDR])) {
                \LogUtil::info("clientId ($clientId) has no access to functional_ai_sdr");
                continue;
            }
            $adminUserId = $service->getAdminUserId();
            User::setLoginUserById($adminUserId);
            $job = new \common\library\queue_v2\job\AiSdrProcessTaskJob($clientId, $taskId);
            \common\library\queue_v2\QueueService::dispatch($job);
        }
    }

    public function actionDeleteAiSdrTask($clientId)
    {
        $filter = new AiSdrTaskFilter($clientId);
        $filter->find()->getOperator()->delete();
    }

    public function actionDeleteSystemSellerProfile(int $clientId)
    {
        $model = \common\models\pg_admin\ClientProfile::model();
        $table = $model->tableName();
        $sql = "DELETE FROM {$table} WHERE client_id = :client_id";
        $params = [':client_id' => $clientId];
        $model->getDbConnection()->createCommand($sql)->execute($params);
    }

    public function actionChangeClientInfo()
    {
        $replaceClients = [
            //原始数据=>测试client
            //1 => 333392,
            //2639 => 441532,
            //2642 => 441533,
            //31704 => 441534,
            //31793 => 442167,
            //340598 => 442168,
            //78403 => 442169,
            //79815 => 443900,
            340598=>443901
        ];
        $model = \Client::model();
        $table = $model->tableName();
        $originClients = implode(',', array_keys($replaceClients));
        $selectSql = "SELECT client_id,full_name,homepage,email FROM {$table} WHERE client_id IN ($originClients) ORDER BY client_id ASC";
        $data = $model->getDbConnection()->createCommand($selectSql)->queryAll();

        $params = [];
        foreach ($data as $item) {
            var_dump("begin process {$item['client_id']} ");
            $params[] = [
                ":client_id" => $replaceClients[$item['client_id']],
                ":full_name" => $item['full_name'],
                ":homepage" => $item['homepage'],
                ":email" => $item['email']
            ];
        }

        $sql = "update $table set full_name = :full_name, homepage = :homepage,email = :email where client_id = :client_id";
        foreach ($params as $param) {
            $model->getDbConnection()->createCommand($sql)->execute($param);
        }
    }

    public function actionChangeClientOrderInfo()
    {
        $replaceClients = [
            //1 => 333392,
            //2639 => 441532,
            //2642 => 441533,
            //31704 => 441534,
            //31793 => 442167,
            //78403 => 442169,
            //340598 => 442168,
            //79815 => 443900,
            340598=>443901
        ];

        foreach ($replaceClients as $clientId => $replaceClient) {
            //复制产品
            var_dump("begin process {$clientId}");
            $this->syncProductInfo($clientId, $replaceClient);
            //复制订单表
            $this->syncOrderInfo($clientId, $replaceClient);
        }
    }

    protected function syncProductInfo($clientId, $replaceClient)
    {
        $productTable = ClientProduct::model()->tableName();

        $db = PgActiveRecord::getDbByClientId($clientId);
        $productSql = "SELECT name,cn_name,enable_flag,product_no FROM {$productTable} WHERE client_id = {$clientId} AND enable_flag = 1 ORDER BY create_time DESC LIMIT 1";
        $productData = $db->createCommand($productSql)->queryAll();
        var_dump("product count is " . count($productData));
        foreach ($productData as &$product) {
            $product['product_id'] = \ProjectActiveRecord::produceAutoIncrementId();
            $product['client_id'] = $replaceClient;
            $product['create_time'] = $product['update_time'] = xm_function_now();
        }
        $batchOperator = (new \common\library\product_v2\BatchProduct($replaceClient))->getOperator();
        $batchOperator->batchInsert($productData, DBConstants::INSERT_MODE_GENERAL, [], [\common\library\product_v2\ProductMetadata::objectIdKey()]);
    }

    protected function syncOrderInfo($clientId, $replaceClient)
    {
        $db = PgActiveRecord::getDbByClientId($clientId);
        $table = Order::model()->tableName();
        $sql = "SELECT order_no,name,product_total_amount_rmb,country,company_id FROM {$table} WHERE client_id = {$clientId} AND company_id != 0 ORDER BY create_time DESC LIMIT 3";
        $dataList = $db->createCommand($sql)->queryAll();
        var_dump("order count is " . count($dataList));
        $createOrderDataChunk = array_chunk($dataList, 300);
        $insertField = [
            'order_id' => 'int',
            'order_no' => 'string',
            'name' => 'string',

            'client_id' => 'int',
            'user_id' => 'json',
            'users' => 'json',
            'departments' => 'json',
            'status' => 'int',
            'link_status' => 'json',
            'country' => 'string',
            'account_date' => 'string',
            'handler' => 'json',
            'currency' => 'string',

            'company_id' => 'int',
            'company_name' => 'string',
            'customer_email' => 'string',
            'customer_phone' => 'string',
            'company_group_id' => 'int',
            'company_phone' => 'string',
            'company_fax' => 'string',
            'customer_id' => 'int',
            'customer_name' => 'string',
            'company_address' => 'string',

            'exchange_rate' => 'int',
            'shipment_deadline_remark' => 'string',
            'price_contract' => 'string',
            'receive_remittance_way' => 'string',
            'remark' => 'string',
            'cost_list' => 'json',
            'product_list' => 'json',

            'exchange_rate_usd' => 'int',
            'amount_usd' => 'int',
            'amount_rmb' => 'int',
            'amount' => 'int',
            'product_total_amount' => 'int',
            'product_total_amount_rmb' => 'int',
            'product_total_amount_usd' => 'int',
            'product_total_count' => 'int',
            'order_gross_margin' => 'int',
            'order_gross_margin_cny' => 'int',
            'order_gross_margin_usd' => 'int',
            'cost_with_tax_total' => 'int',
            'addition_cost_amount' => 'int',
            'addition_cost_amount_rmb' => 'int',
            'addition_cost_amount_usd' => 'int',
            'not_collect_amount_rmb' => 'int',
            'not_collect_amount_usd' => 'int',
            'capital_account_id' => 'int',

            'enable_flag' => 'int',
            'archive_type' => 'int',
            'create_user' => 'int',
            'update_user' => 'int',
            'create_time' => 'string',
            'delete_time' => 'string',
            'update_time' => 'string',
            'tax_refund_type' => 'int',
            'external_field_data' => 'json'
        ];
        foreach ($createOrderDataChunk as &$chunk) {
            foreach ($chunk as &$item) {
                $item['client_id'] = $replaceClient;
                $item['order_id'] = \ProjectActiveRecord::produceAutoIncrementId();
                $item['create_time'] = $item['update_time'] = xm_function_now();
                foreach ($insertField as $field => $fieldType) {
                    if (!isset($item[$field])) {        // 补全data中不存在的字段值，保证所有row的字段列数是相同的
                        switch ($fieldType) {
                            case 'string':
                                $item[$field] = "";
                                break;
                            case 'int':
                                $item[$field] = 0;
                                break;
                            case 'json':
                                $item[$field] = [];
                                break;
                        }
                    }
                }
            }
            $orderOperator = (new BatchOrder($replaceClient))->getOperator();
            $orderOperator->batchInsert($chunk, DBConstants::INSERT_MODE_GENERAL, [], [OrderMetadata::objectIdKey()]);
        }
    }

    public function actionTurnOnLeadsActivationSetting($clients)
    {
        $clientIds = explode(',', $clients);
        foreach ($clientIds as $clientId) {
            $client = \common\library\account\Client::getClient($clientId);
            $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_AI_SDR_LEAD_ACTIVATION_ENABLE => 1]);
            $client->saveExtentAttributes();
        }
    }

    public function actionReDelivery($user_id, $task_id, $lead_id)
    {
        User::setLoginUserById($user_id);

        $user = User::getLoginUser();
        $client_id = $user->getClientId();

        $task = new AiSdrTask($client_id, $task_id);
        $end_stage = $task->end_stage;

        $sdrDetail = new \common\library\ai_sdr\task_detail\AiSdrTaskDetail($client_id);
        $sdrDetail->load([
            'lead_id' => $lead_id,
            'enable_flag' => 1
        ]);

        $sdrLeadDetail = new \common\library\ai_sdr\SdrLeadDetail($client_id);
        $sdrLeadDetail->initFromSingle($sdrDetail->getAttributes());

        $executor = new \common\library\ai_sdr\SdrDetailExecutor($client_id, $user_id);
        $executor->setTask($task);

        $executor->checkTaskStage($sdrLeadDetail, $end_stage);
    }

    public function actionAssignRolePrivilege($clientId, $roleId)
    {
        if (!PrivilegeService::getInstance($clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_AI_SDR])) {
            print ("clientId ($clientId) has no access to functional_ai_sdr");
            return;
        }

        print("begin process: clientId: $clientId, roleId: $roleId\n");

        $privilegeService = PrivilegeService::getInstance($clientId);
        $adminUserId = $privilegeService->getAdminUserId();
        if (!$adminUserId) {
            print("clientId:$clientId adminUser not exist!");
            return;
        }
        User::setLoginUserById($adminUserId);
        $roles = $privilegeService->getUserPrivilege()->getRoles();
        if (!$roles->isRoleIdExist($roleId)) {
            print("role_id ($roleId) not found\n");
            return;
        };
        $roles->assignPrivilege(
            $roleId,
            PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,
            [
                PrivilegeConstants::PRIVILEGE_CRM_AI_SDR_VIEW
            ],
        );
    }

    public function actionFixEdm($clientId) {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $recordFilter = new \common\library\ai_sdr\task_record\AiSdrTaskRecordFilter($clientId);
        $recordFilter->type = Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN;
        $recordFilter->executed_time = new \xiaoman\orm\database\data\NotNull();
        $recordCount = $recordFilter->count();
        $pageSize = 100;
        $leadList = new \common\library\lead_v3\LeadList($adminUserId);
        $leadList->setSkipPrivilege(true);
        $leadList->setSkipFilterStatus(true);
        $pageCount = ceil($recordCount / $pageSize);

        $edmList = new \common\library\edm\EdmTaskList($clientId);
        $edmList->setDraftFlag(0);
        $edmList->setTaskSubjectSearchKey('AI SDR营销任务');
        $edmList->setOrderBy('create_time');
        $edmList->setOrder('asc');
        $edmList->setFields(['task_subject', 'task_id', 'create_time']);
        $edms = $edmList->find();

        $edmMap = [];
        foreach ($edms as $edm) {
            $subject = $edm['task_subject'];
            $subject = str_replace('AI SDR营销任务', '', $subject);
            $subjects = explode("2025", $subject);
            $subject = $subjects[0];
            $taskId = $edm['task_id'];
            $createTime = $edm['create_time'];
            $createTime = substr($createTime, 0, 10);
            if (isset($edmMap[$subject])) {
                $edmMap[$subject][] = [
                    'task_id' => $taskId,
                    'create_time' => $createTime,
                ];
            } else {
                $edmMap[$subject] = [
                    [
                        'task_id' => $taskId,
                        'create_time' => $createTime,
                    ]
                ];
            }
        }

        $recordFilter->order('executed_time');
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $updateSqlArr = [];
        for ($i = 0; $i < $pageCount; $i++) {
            $recordFilter->limit($pageSize, $i+1);
            $recordFilter->select(['record_id', 'lead_id', 'executed_time']);
            $recordList = $recordFilter->rawData();

            $leadList->setLeadId(array_column($recordList, 'lead_id'));
            $leadList->setFields(['lead_id', 'company_name']);
            $leads = $leadList->find();
            $leads = array_column($leads, 'company_name','lead_id');
            foreach ($recordList as $record) {
                $leadId = $record['lead_id'];
                $recordId = $record['record_id'];
                $companyName = $leads[$leadId]??'';
                if (empty($companyName)) {
                    var_dump("record_id {$recordId} lead_id {$leadId} cannot find company name");
                    continue;
                }
                $edms = $edmMap[$companyName]??[];
                if (empty($edms)) {
                    var_dump("lead_id {$leadId} company_name {$companyName} cannot find edm");
                    continue;
                }

                $recordExecutedTime = $record['executed_time'];
                foreach ($edms as $edm) {
                    $createTime = $edm['create_time'];
                    $taskId = $edm['task_id'];
                    if (str_contains($recordExecutedTime, $createTime)) {
                        $updateSqlArr[$recordId] = "UPDATE tbl_ai_sdr_task_record SET refer_type=3, refer_id={$taskId} WHERE record_id={$recordId} and client_id={$clientId} and type=5";
                    }
                }
            }
        }

        echo "record count: {$recordCount}, page count: {$pageCount}\n";
        var_dump($updateSqlArr);
        $db->createCommand(implode(";", $updateSqlArr))->execute();
    }

    public function actionRegenerate($clientId, $taskId, $source = Constant::TASK_SOURCE_AI_SDR) {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $taskFilter = new AiSdrTaskFilter($clientId);
        $taskFilter->source = $source;
        $taskFilter->select(['task_id']);
        $taskIds = $taskFilter->rawData();
        $taskId = $taskIds[0]['task_id']??0;
        $aiSdrTask = new AiSdrTask($clientId,$taskId);
        if ($aiSdrTask->isNew()){
            print ("ai_sdr_task_id ($taskId) of clientId($clientId) is not found");
            return;
        }

        // TODO @yoo.lyh 重新生成同一个 sdr_task的卖家画像
        $clientModel = \Client::findByClientId($clientId);
        $companyName = $clientModel->full_name ?? '';
        $homepage = Helper::getPossibleHomepage($clientModel->homepage, $clientModel->email);
        #生成卖家画像任务
        var_dump('begin  seller_profile generation');
        $sellerTask = SellerProfileTask::newTask($clientId, $adminUserId, $companyName, $homepage, '', $clientModel->country, $taskId);
        $sellerTask->generate(true);

        var_dump('begin graph generation');
        $aiProductUsageFilter = new AiProductUsageRecordFilter($clientId);
        $aiProductUsageFilter->ai_sdr_task_id = $taskId;
        $aiProductUsageFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $aiProductUsageFilter->select(['record_id']);
        $usageIds = $aiProductUsageFilter->rawData();
        var_dump($usageIds);
        $batchProductUsage = new BatchAiProductUsageRecord($clientId);
        $batchProductUsage->initFromData($usageIds);
        !empty($usageIds) && $batchProductUsage->getOperator()->update(['enable_flag' => BaseObject::ENABLE_FLAG_FALSE]);

        $digRecordFilter = new \common\library\ai_sdr\dig_record\AiSdrDigRecordFilter($clientId);
        $digRecordFilter->usage_record_id = new In(array_column($usageIds, 'record_id'));
        $digRecordFilter->lead_quality = Constant::LEAD_QUALITY_UNKNOWN;
        $digRecordFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $digRecordFilter->select(['record_id']);
        $recordIds = $digRecordFilter->rawData();
        $batchDigRecord = new \common\library\ai_sdr\dig_record\BatchAiSdrDigRecord($clientId);
        $batchDigRecord->initFromData($recordIds);
        !empty($recordIds) && $batchDigRecord->getOperator()->update(['enable_flag' => BaseObject::ENABLE_FLAG_FALSE]);

        $job = new AiSdrProductUsageJob($clientId, $taskId);
        $job->handle();
    }

    public function actionFixLeadQuality($clientId) {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $taskFilter = new AiSdrTaskFilter($clientId);
        $taskFilter->task_status = 1;
        $taskFilter->select(['task_id']);
        $taskIds = array_column($taskFilter->rawData(), 'task_id');

        foreach ($taskIds as $taskId) {
            $agent = new \common\library\ai_agent\SdrLeadQualityAnalysisAgent($clientId, $adminUserId);
            $recordFilter = new \common\library\ai_sdr\task_record\AiSdrTaskRecordFilter($clientId);
            $recordFilter->task_id = $taskId;
            $recordFilter->type = Constant::RECORD_TYPE_ANALYZE_QUALITY;
            $recordFilter->getQuery()->rawWhere(" AND jsonb_extract_path_text(data, '0', 'type') is null");
            $recordFilter->select(['detail_id', 'record_id']);
            $recordData = array_column($recordFilter->rawData(), 'record_id', 'detail_id');

            $detailFilter = new AiSdrTaskDetailFilter($clientId);
            $detailFilter->task_id = $taskId;
            $detailFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $detailFilter->id = new In(array_keys($recordData));
            $batchDetail = $detailFilter->find();
            $batchDetail->getFormatter()->baseInfoSetting();
            $batchDetail->getFormatter()->displayBuyerProfile(true);
            $details = $batchDetail->getAttributes();

            $clientProfile = SellerProfileTask::getExistedTaskObjectBySdrTaskId($clientId, $taskId);
            $clientProfile->getFormatter()->displayFields([
                'products_category','profile', 'primary_products', 'products_category', 'industry'
            ]);
            $clientProfileStr = json_encode($clientProfile->getAttributes(), JSON_UNESCAPED_UNICODE);
            $batchRecord = new BatchAiSdrTaskRecord($clientId);
            foreach ($details as $index => $detail) {
                try {
                    $answer = $agent->process([
                        'client_profile' => $clientProfileStr,
                        'buyer_profile' => json_encode($detail['buyer_profile'], JSON_UNESCAPED_UNICODE),
                    ]);
                    $answer = $answer['answer']??[
                        'domain' => '',
                        'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN,
                        'reason' => []
                    ];
                    $detailId = $detail['id'];
                    $recordId = $recordData[$detailId]??0;
                    if (!$recordId) continue;
                    var_dump("process: {$index}/".count($details));
                    $batchRecord->initFromData([[
                        'record_id' => $recordId
                    ]]);
                    $batchRecord->getOperator()->update([
                        'data' => $answer['reason']??[]
                    ]);
                } catch (\Exception $exception) {
                    var_dump($exception->getMessage());
                    \LogUtil::info($exception->getTraceAsString());
                }
            }
        }
    }
    public function actionResetTask($client_id, $task_id, $stage = 0, $status = 0)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $detailFilter = new AiSdrTaskDetailFilter($client_id);
        $detailFilter->task_id = $task_id;
        $detailFilter->enable_flag = 1;

        $updateData = [
            'stage' => $stage,
            'status' => $status
        ];
        $detailFilter->find()->getOperator()->update($updateData);
    }

    public function actionGenerateCrmBuyerPortrait($client_id, $task_id)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $detailFilter = new AiSdrTaskDetailFilter($client_id);
        $detailFilter->task_id = $task_id;
        $detailFilter->lead_id = new \xiaoman\orm\database\data\NotEqual(0);
        $detailFilter->enable_flag = 1;
        $detailData = $detailFilter->rawData();

        $lead_ids = collect($detailData)->pluck('lead_id')->toArray();

        $leadList = new \common\library\lead_v3\LeadList($adminUserId);
        $leadList->setLeadId($lead_ids);
        $leadList->setSkipPrivilege(true);
        $leadList->setSkipFilterStatus(true);
        $leadList->setIsArchive(null);
        $leadList->getFormatter()->setSpecifyFields(['lead_id', 'name', 'homepage']);
        $leadData = $leadList->find();

        $leadDataMap = collect($leadData)->keyBy('lead_id')->toArray();

        foreach ($detailData as $detailDatum){
            if ($detailDatum['product_ids'] || $detailDatum['company_types']){
                continue;
            }

            $detail_id = $detailDatum['id'];
            $lead_id = $detailDatum['lead_id'];

            $homepage = $leadDataMap[$lead_id]['homepage'] ?? '';
            $company_name = $leadDataMap[$lead_id]['name'] ?? '';

            $domain = \common\library\ai\service\RecommendService::getDomain($homepage);
            if (!$domain){
                continue;
            }

            $job = new \common\library\queue_v2\job\AiSdrCrmBuyerPortraitJob($client_id, $adminUserId, $detail_id, $domain, $company_name);
            \common\library\queue_v2\QueueService::dispatch($job);
        }
    }

    //盘活场景根据背调结果补充公司信息
    public function actionFixBackgroundCheckInfo($client_id, $task_id)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $detailFilter = new AiSdrTaskDetailFilter($client_id);
        $detailFilter->task_id = $task_id;
        $detailFilter->enable_flag = 1;
        $detailFilter->lead_id = new \xiaoman\orm\database\data\NotEqual(0);
        $detailData = $detailFilter->rawData();

        foreach ($detailData as $detailDatum){
            $lead = new \common\library\lead\Lead($client_id, $detailDatum['lead_id']);
            if (!$lead->homepage || !$lead->country){
                $recordFilter = new \common\library\ai_sdr\task_record\AiSdrTaskRecordFilter($client_id);
                $recordFilter->task_id = $task_id;
                $recordFilter->lead_id = $detailDatum['lead_id'];
                $recordFilter->type = Constant::RECORD_TYPE_BACKGROUND_CHECK;
                $recordFilter->enable_flag = 1;
                $recordData = $recordFilter->rawData();

                if (!$recordData){
                    continue;
                }

                $backgroundCheckInfo = $recordData[0]['data'] ?? [];
                $report = $backgroundCheckInfo['report'] ?? [];
                if (!$report){
                    continue;
                }

                $homepage = $report['homepage'] ?? '';
                $country = $report['country'] ?? '';

                if (!$homepage && !$country){
                    continue;
                }

                if ($homepage && $homepage != $lead->homepage){
                    $lead->homepage = $homepage;
                }

                if ($country && $country != $lead->country){
                    $lead->country = $country;
                }

                $lead->update(['homepage', 'country']);
            }
        }
    }

    //盘活场景修复潜客开发过程
    public function actionFixDynamicTrail($client_id, $task_id, $lead_id = 0)
    {
        $adminUserId = PrivilegeService::getInstance($client_id)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $task = new AiSdrTask($client_id, $task_id);

        $detailFilter = new AiSdrTaskDetailFilter($client_id);
        $detailFilter->task_id = $task_id;
        $detailFilter->enable_flag = 1;

        if ($lead_id){
            $detailFilter->lead_id = $lead_id;
        } else {
            $detailFilter->lead_id = new \xiaoman\orm\database\data\NotEqual(0);
        }

        $detailData = $detailFilter->rawData();

        foreach ($detailData as $detailDatum){
            $lead_id = $detailDatum['lead_id'];
            $recordFilter = new \common\library\ai_sdr\task_record\AiSdrTaskRecordFilter($client_id);
            $recordFilter->task_id = $task_id;
            $recordFilter->lead_id = $lead_id;
            $recordFilter->type = new In([Constant::RECORD_TYPE_HATCH_SUCCESS, Constant::RECORD_TYPE_HATCH_FAILED, Constant::RECORD_TYPE_DELIVERY_SUCCESS, Constant::RECORD_TYPE_DELIVERY_FAILED]);
            $recordFilter->find()->getOperator()->update(['enable_flag' => 0]);

            //重新执行交付动作
            $detail = new \common\library\ai_sdr\SdrLeadDetail($client_id);
            $detail->initFromSingle($detailDatum);

            $executor = new \common\library\ai_sdr\SdrDetailExecutor($client_id, $adminUserId);
            $executor->setTask($task);
            $executor->checkTaskStage($detail, $detail->stage);
        }
    }

    public function actionResetLeadStatus($clientId, $taskId) {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $sql = "SELECT detail_id, max(type) as type FROM tbl_ai_sdr_task_record where detail_id in (select id from tbl_ai_sdr_task_detail where task_id = {$taskId} and status = 2) group by detail_id";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $result = $db->createCommand($sql)->queryAll($sql);

        $updateSql = [];
        foreach ($result as $detailInfo) {
            $detailId = $detailInfo['detail_id'];
            $type = $detailInfo['type'];
            switch ($type) {
                case Constant::RECORD_TYPE_ANALYZE_QUALITY:
                    $updateSql[] = "UPDATE tbl_ai_sdr_task_detail SET status = 1 WHERE task_id={$taskId} and id = {$detailId}";
                    break;
                default:
                    break;
            }
        }
        !empty($updateSql) && $db->createCommand(implode(";", $updateSql))->execute();
    }

    public function actionFixLeadDuplicate($clientId) {
        $service = PrivilegeService::getInstance($clientId);
        if (!$service->hasFunctional(PrivilegeConstants::FUNCTIONAL_AI_SDR)) {
            var_dump("client_id {$clientId} has no data to fix");
            return;
        }

        $adminUserId = $service->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $recordFilter = new \common\library\ai_sdr\task_record\AiSdrTaskRecordFilter($clientId);
        $recordFilter->type = Constant::RECORD_TYPE_HATCH_SUCCESS;
        $recordFilter->select(['detail_id']);
        $recordData = array_column($recordFilter->rawData(), 'detail_id');

        $detailFilter = new AiSdrTaskDetailFilter($clientId);
        $detailFilter->select(['lead_id']);
        $detailFilter->id = new \xiaoman\orm\database\data\NotIn($recordData);
        $detailFilter->source = Constant::DETAIL_SOURCE_DIG;
        $detailData = array_column($detailFilter->rawData(), 'lead_id');

        $querySql = "UPDATE tbl_lead SET is_archive = 2, origin=34, origin_list=ARRAY[34]::bigint[], status= 7 WHERE client_id={$clientId} AND lead_id IN (".implode(",", $detailData).")";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $db->createCommand($querySql)->execute();

        $leadQuery = "SELECT lead_id, is_archive from tbl_lead WHERE client_id = {$clientId} and status = 10 and lead_id not in (".implode(",", $detailData).")";
        $leadIds = $db->createCommand($leadQuery)->queryAll();
        $archiveLeads = [];
        $leads = [];
        foreach ($leadIds as $leadInfo) {
            $isArchive = $leadInfo['is_archive']??1;
            if ($isArchive == 1) {
                $leads[] = $leadInfo['lead_id'];
            } else {
                $archiveLeads[] = $leadInfo['lead_id'];
            }
        }
        !empty($archiveLeads) && $db->createCommand("UPDATE tbl_lead SET status=7, origin=34, origin_list=ARRAY[34]::bigint[] WHERE client_id={$clientId} AND lead_id IN (".implode(",", $archiveLeads).")")->execute();
        !empty($leads) && $db->createCommand("UPDATE tbl_lead SET status=3, origin=34, origin_list=ARRAY[34]::bigint[] WHERE client_id={$clientId} AND lead_id IN (".implode(",", $leads).")")->execute();

        $detailFilter = new AiSdrTaskDetailFilter($clientId);
        $detailFilter->select(['lead_id']);
        $detailFilter->source = Constant::DETAIL_SOURCE_DIG;
        $leadIds = array_column($detailFilter->rawData(), 'lead_id');
        $db->createCommand("UPDATE tbl_lead_auto_create_record SET origin = 34 WHERE client_id={$clientId} AND lead_id IN (".implode(",", $leadIds).")")->execute();
    }

    public function actionRebuildDuplicateFlag($clientId) {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        User::setLoginUserById($adminUserId);

        $filter = new AiSdrTaskDetailFilter($clientId);
        $filter->select(['lead_id']);
        $leadIds = array_column($filter->rawData(), 'lead_id');
        \common\library\server\duplicate_flag\SyncDuplicateFlagQueueService::pushLeadQueue($adminUserId, $clientId, $leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    public function actionFixHomepage($clientId, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $db = \Yii::app()->account_base_db;
            $clientIds = $db->createCommand("select client_id from tbl_privilege_client_module where module_id = 'ai_sdr' and enable_flag = 1;")->queryColumn();
        }

        foreach ($clientIds as $clientId) {
            $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
            $clientDb = PgActiveRecord::getDbByClientId($clientId);
            $sql = "select lead_id, homepage from tbl_lead where client_id = {$clientId} and is_archive = 2 and origin=34 and homepage <> ''";
            $leadList = $clientDb->createCommand($sql)->queryAll();
            $domains = array_column($leadList, 'homepage' , 'lead_id');
            foreach ($domains as &$url) {
                $url = RecommendService::getDomain($url);
            }

            $recommendApi = new RecommendApi($clientId, $adminUserId);
            $buyerProfiles = $recommendApi->getCompanyProfileByDomains($domains);
            $buyerProfilesHomepage = array_column($buyerProfiles, 'public_homepage', 'domain');

            $detailList = $clientDb->createCommand("select id,lead_id from tbl_ai_sdr_task_detail where client_id = $clientId and lead_quality > 1 and public_homepage = '{}'")->queryAll();
            $updateMap = [];
            foreach ($detailList as $detail) {
                $detailDomains = $buyerProfilesHomepage[$domains[$detail['lead_id']]] ?? '';
                if ($detailDomains) {
                    $updateMap[$detail['id']] = \common\library\util\PgsqlUtil::formatArray(is_array($detailDomains) ? $detailDomains : json_decode($detailDomains, true));
                }
            }

            $updateSql = [];
            $params = [];
            foreach ($updateMap as $id => $homepage) {
                $updateSql[] = "update tbl_ai_sdr_task_detail set public_homepage = :homepage_{$id}  where id = {$id}";
                $params[":homepage_{$id}"] = $homepage;
            }
            if ($dryRun) {
                var_dump($updateSql);
            } else {
                $updated = $clientDb->createCommand(implode(';', $updateSql))->execute($params);
                echo "update count: $updated for $clientId, excepted:" . count($updateSql) . "\n";
            }
        }
    }

    public function actionFixStatTotal($clientId, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $db = \Yii::app()->account_base_db;
            $clientIds = $db->createCommand("select client_id from tbl_privilege_client_module where module_id = 'ai_sdr' and enable_flag = 1;")->queryColumn();
        }

        foreach ($clientIds as $clientId) {
            $sql = "select task_id,count(*) as cc from tbl_ai_sdr_task_detail where client_id = {$clientId} and enable_flag = 1 group by task_id;";
            $totalList = PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll();
            $updateSql = [];
            foreach ($totalList as $item) {
                $updateSql[] = "update tbl_ai_sdr_task set stat_total = {$item['cc']} where task_id = {$item['task_id']}";
            }
            if ($dryRun) {
                var_dump($clientId, $updateSql);
            } else {
                if ($updateSql) {
                    PgActiveRecord::getDbByClientId($clientId)->createCommand(implode(';', $updateSql))->execute();
                }
            }
        }
    }

    public function actionFixDetailTime($clientId, $dryRun = 1)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $db = \Yii::app()->account_base_db;
            $clientIds = $db->createCommand("select client_id from tbl_privilege_client_module where module_id = 'ai_sdr' and enable_flag = 1;")->queryColumn();
        }

        foreach ($clientIds as $clientId) {
            $detailSql = "select id,lead_id,source,task_id from tbl_ai_sdr_task_detail where client_id = {$clientId} and enable_flag = 1";
            $pgDb = \PgActiveRecord::getDbByClientId($clientId);
            $detailList = $pgDb->createCommand($detailSql)->queryAll();
            $detailMap = [];
            foreach ($detailList as $item) {
                $detailMap[$item['task_id']][$item['source']][$item['id']] = $item['lead_id'];
            }

            $updateMap = [];
            foreach ($detailMap as $taskId => $sourceMap) {
                $recordSql = "select type,detail_id,lead_id,executed_time,refer_type from tbl_ai_sdr_task_record where  client_id = {$clientId} and type in (0,3,4) and enable_flag = 1 and detail_id > 0;";
                $recordList = $pgDb->createCommand($recordSql)->queryAll();
                foreach ($recordList as $item) {
                    switch ($item['type']) {
                        case Constant::RECORD_TYPE_ADD_LEAD:
                            $updateMap[$item['detail_id']]['stage_dig_time'] = $item['executed_time'];
                            break;
                        case Constant::RECORD_TYPE_CHECK_CONTACTS:
                            $updateMap[$item['detail_id']]['stage_reachable_time'] = $item['executed_time'];
                            break;
                        case Constant::RECORD_TYPE_CREATE_MARKET_PLAN:
                            $updateMap[$item['detail_id']]['stage_marketing_time'] = $item['executed_time'];
                            break;
                    }
                }
                $edmSql = "select detail_id,refer_id from tbl_ai_sdr_task_record where  client_id = $clientId and refer_type = 3 and enable_flag = 1 and detail_id>0";
                $edmIds = $pgDb->createCommand($edmSql)->queryAll();
                if ($edmIds) {
                    $edmIds = array_column($edmIds, 'detail_id', 'refer_id');
                    $edmIdsStr = implode(',', array_keys($edmIds));
                    $edmMailSql = "SELECT task_id, last_view_time, reply_time FROM tbl_group_mail WHERE task_id IN ($edmIdsStr) AND enable_flag = 1";
                    $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);
                    $groupMailData = $mysqlDb->createCommand($edmMailSql)->queryAll();
                    foreach ($groupMailData as $datum) {
                        $updateMap[$edmIds[$datum['task_id']]]['stage_effective_time'] = $datum['last_view_time'];
                        $updateMap[$edmIds[$datum['task_id']]]['stage_highvalue_time'] = $datum['reply_time'];
                    }
                }

            }
            $updateSql = [];
            $params = [];
            $chunk = 100;
            foreach ($updateMap as $detailId => $updateFields) {
                $replace = [];
                foreach ($updateFields as $updateField => $updateFieldValue) {
                    $params[":{$updateField}_{$detailId}"] = $updateFieldValue;
                    $replace[] = "{$updateField}=:{$updateField}_{$detailId}";
                    $updateSql[] = "update tbl_ai_sdr_task_detail set " . implode(',', $replace) . " where id = {$detailId}";
                }
                if (count($updateSql) > $chunk) {
                    if ($dryRun) {
                        echo implode(';', $updateSql) ."\n";
                        echo implode(';', $params) . "\n";
                    } else {
                        $update = $pgDb->createCommand(implode(';', $updateSql))->execute($params);
                        echo "update count:" . count($updateSql);
                    }
                    $updateSql = [];
                    $params = [];
                }
            }
        }

        if ($dryRun) {
            echo implode(';', $updateSql) ."\n";
            echo implode(';', $params) . "\n";
        } else {
            $update = $pgDb->createCommand(implode(';', $updateSql))->execute($params);
            echo "update count:" . count($updateSql);
        }
    }

}