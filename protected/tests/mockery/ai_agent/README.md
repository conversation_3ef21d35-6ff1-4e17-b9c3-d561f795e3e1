# AI Agent Mockery框架使用指南

## 📋 概述

AI Agent Mockery框架使用Mockery库实现对`AiAgentFactory::createAgent`方法的Mock，为SDR测试流程提供完整的AI Agent模拟功能。

## 🎯 支持的AI Agent类型

- **SdrLeadQualityAnalysisAgent** - 潜客质量分析
- **SdrEdmWriteAgent** - EDM邮件写作
- **SdrSellerIndustryAnalyze** - 卖家行业分析
- **AiSdrSellerProfileAgent** - 卖家档案生成
- **AiSdrBuyerProfileAgent** - 买家档案生成
- **HomepageAiAgent** - 主页分析
- **AiSdrProductCategoryAiAgent** - 产品分类
- **SdrHutchLeadQualityAgent** - Hutch线索质量分析

## 🚀 快速开始

### 1. 基本使用

```php
<?php

use tests\mockery\ai_sdr\AiAgentFactoryMockery;
use tests\mockery\ai_sdr\traits\MockeryAgentTrait;

class YourTestClass extends PHPUnit\Framework\TestCase
{
    use MockeryAgentTrait;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpAiAgentMockery();
    }
    
    protected function tearDown(): void
    {
        $this->tearDownAiAgentMockery();
        parent::tearDown();
    }
    
    public function testSdrWorkflow()
    {
        // 设置测试场景
        $this->setQualityAnalysisScenario('high_quality');
        $this->setEdmWriteScenario('standard_emails');
        
        // 执行业务逻辑
        $sdrService = new AISdrService($clientId, $userId);
        $result = $sdrService->processTask($taskId);
        
        // 验证Agent调用
        $this->assertAgentWasCalled(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS);
        $this->assertAgentWasCalled(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER);
    }
}
```

### 2. 高级使用

```php
public function testComplexSdrWorkflow()
{
    // 设置复杂测试场景
    $this->setAiAgentTestScenario('comprehensive_test', [
        'lead_quality' => Constant::LEAD_QUALITY_HIGH,
        'industry_focus' => 'Technology',
        'email_rounds' => 3
    ]);
    
    // 自定义特定Agent响应
    $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, [
        'answer' => [
            'lead_quality' => Constant::LEAD_QUALITY_HIGH,
            'confidence' => 0.95,
            'reason' => ['Perfect match', 'High intent', 'Decision maker identified'],
            'score' => 92
        ],
        'record_id' => 123456
    ]);
    
    // 执行测试
    $executor = new SdrDetailExecutor($clientId, $userId);
    $executor->process($details, Constant::DETAIL_STATUS_VALIDATE_CONTACTS);
    
    // 详细验证
    $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, 1);
    $this->assertAgentCalledWith(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, [
        'client_profile' => $expectedClientProfile,
        'buyer_profile' => $expectedBuyerProfile
    ]);
}
```

## 🔧 核心功能

### 1. 场景配置

```php
// 预定义场景
$this->setQualityAnalysisScenario('high_quality');
$this->setEdmWriteScenario('standard_emails');
$this->setSellerIndustryAnalysisScenario('tech_focused');

// 自定义场景
$this->setAiAgentTestScenario('custom_scenario', [
    'lead_quality' => Constant::LEAD_QUALITY_MEDIUM,
    'confidence_threshold' => 0.8
]);
```

### 2. 响应数据定制

```php
// 设置特定Agent的响应
$this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER, [
    [
        'subject' => 'Custom Email Subject',
        'content' => 'Custom email content...',
        'round' => 1
    ]
]);
```

### 3. 调用验证

```php
// 基本验证
$this->assertAgentWasCalled($sceneType);
$this->assertAgentWasNotCalled($sceneType);

// 调用次数验证
$this->assertAgentCallCount($sceneType, 2);

// 参数验证
$this->assertAgentCalledWith($sceneType, [
    'client_profile' => $expectedProfile
]);

// 批量验证
$this->assertAllExpectedAgentsCalled([
    \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
    \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER
]);
```

### 4. 异常模拟

```php
// 模拟Agent异常
$this->simulateAgentException(
    \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
    'Quality analysis service unavailable'
);

// 测试异常处理
$this->expectException(AiAgentException::class);
$sdrService->processQualityAnalysis($params);
```

## 📊 调试和监控

### 1. 调用日志

```php
// 获取调用日志
$callLog = $this->getAgentCallLog();

// 清空调用日志
$this->clearAgentCallLog();

// 打印调用统计
$this->printAgentCallStatistics();
```

### 2. 状态管理

```php
// 重置Mock状态
$this->resetAiAgentMockery();

// 获取当前场景
$scenario = AiAgentFactoryMockery::getCurrentScenario();
```

## 🎨 最佳实践

### 1. 测试组织

```php
class SdrQualityAnalysisTest extends AiSdrTestCase
{
    use MockeryAgentTrait;
    
    public function setUp(): void
    {
        parent::setUp();
        $this->setUpAiAgentMockery();
    }
    
    /**
     * @dataProvider qualityScenarioProvider
     */
    public function testQualityAnalysis($scenario, $expectedQuality)
    {
        $this->setQualityAnalysisScenario($scenario);
        
        $result = $this->sdrService->analyzeLeadQuality($params);
        
        $this->assertEquals($expectedQuality, $result['lead_quality']);
        $this->assertAgentWasCalled(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS);
    }
    
    public function qualityScenarioProvider()
    {
        return [
            ['high_quality', Constant::LEAD_QUALITY_HIGH],
            ['medium_quality', Constant::LEAD_QUALITY_MEDIUM],
            ['low_quality', Constant::LEAD_QUALITY_LOW],
        ];
    }
}
```

### 2. 集成测试

```php
class SdrEndToEndTest extends AiSdrTestCase
{
    use MockeryAgentTrait;
    
    public function testCompleteWorkflow()
    {
        // 设置完整的测试场景
        $this->setAiAgentTestScenario('end_to_end_test');
        
        // 执行完整流程
        $task = $this->createTestTask();
        $details = $this->createTestDetails($task);
        
        $executor = new SdrDetailExecutor($this->clientId, $this->userId);
        $executor->setTask($task);
        $executor->process($details);
        
        // 验证完整的Agent调用链
        $this->assertOnlyExpectedAgentsCalled([
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE
        ]);
    }
}
```

## ⚠️ 注意事项

1. **内存管理**: 在`tearDown`中调用`tearDownAiAgentMockery()`
2. **状态隔离**: 每个测试方法开始前重置Mock状态
3. **异常处理**: 正确模拟和测试异常场景
4. **性能考虑**: Mock响应应该快速返回，避免复杂计算

## 🔍 故障排除

### 常见问题

1. **Mock未生效**: 确保在测试开始前调用`setUpAiAgentMockery()`
2. **调用验证失败**: 检查sceneType是否正确
3. **内存泄漏**: 确保在`tearDown`中清理Mock状态
4. **异常未捕获**: 验证异常模拟设置是否正确

### 调试技巧

```php
// 打印调用日志
var_dump($this->getAgentCallLog());

// 打印调用统计
$this->printAgentCallStatistics();

// 检查当前场景
var_dump(AiAgentFactoryMockery::getCurrentScenario());
```

## 📈 性能优势

- **测试速度**: AI Agent调用从秒级降低到毫秒级
- **测试稳定性**: 消除外部AI服务依赖
- **测试覆盖率**: 支持各种边界情况和异常场景
- **开发效率**: 快速验证业务逻辑
- **CI/CD友好**: 在任何环境下稳定运行
