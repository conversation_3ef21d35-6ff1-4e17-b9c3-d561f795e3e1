<?php

namespace tests\functional\ai_sdr\mockery\agents;

use common\library\ai_agent\SdrLeadQualityAnalysisAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\functional\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock SDR线索质量分析Agent
 * 
 * 模拟SdrLeadQualityAnalysisAgent的行为，用于测试
 */
class MockSdrLeadQualityAnalysisAgent extends SdrLeadQualityAnalysisAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''):array
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据
        $responseData = $this->dataFactory->getQualityAnalysisResponse($params);

        // 格式化答案
        $formattedAnswer = $this->formatAnswer($responseData['answer']);

        // 返回与真实Agent相同的数组格式
        return [
            'answer' => $formattedAnswer,
            'record_id' => $responseData['record_id']
        ];
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS;
    }

    /**
     * Mock formatAnswer方法
     */
    protected function formatAnswer(array $answer): array
    {
        // 模拟原始的格式化逻辑
        $leadQuality = $answer['lead_quality'] ?? '';

        // 如果已经是数字常量，直接返回
        if (is_numeric($leadQuality)) {
            return $answer;
        }

        // 如果是字符串，进行转换
        if (str_contains($leadQuality, 'high')) {
            $answer['lead_quality'] = \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH;
        } elseif (str_contains($leadQuality, 'medium')) {
            $answer['lead_quality'] = \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM;
        } else {
            $answer['lead_quality'] = \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW;
        }
        return $answer;
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * Mock其他可能被调用的方法
     */
    public function loadBySceneType(int $sceneType): void
    {
        // Mock实现，不执行实际加载
        $this->callLog[] = [
            'method' => 'loadBySceneType',
            'sceneType' => $sceneType,
            'timestamp' => time()
        ];
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock AI Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getQualityAnalysisResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\exception\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
