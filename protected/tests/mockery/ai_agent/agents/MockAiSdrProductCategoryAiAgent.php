<?php

namespace tests\mockery\ai_sdr\agents;

use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\AiSdrProductCategoryAiAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\mockery\ai_sdr\data\AgentResponseDataFactory;

/**
 * Mock AI SDR产品分类Agent
 * 
 * 模拟AiSdrProductCategoryAiAgent的行为，用于测试
 */
class MockAiSdrProductCategoryAiAgent extends AiSdrProductCategoryAiAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据
        $responseData = $this->dataFactory->getProductCategoryResponse($params);

        // 创建AiAgentProcessResponse对象
        $response = new AiAgentProcessResponse();
        $response->answer = json_encode($responseData['answer']); // 注意：真实Agent返回的answer是JSON字符串
        $response->recordId = $responseData['record_id'];
        $response->messageType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT;
        $response->function = $function ?: 'productCategoryWash';

        return $response;
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取产品分类结果
     */
    public function getProductCategories(array $params = []): array
    {
        $response = $this->process($params);
        return $response->answer;
    }

    /**
     * 获取所有分类
     */
    public function getAllCategories(array $params = []): array
    {
        $result = $this->getProductCategories($params);
        return $result['categories'] ?? [];
    }

    /**
     * 获取主要分类
     */
    public function getPrimaryCategory(array $params = []): ?string
    {
        $result = $this->getProductCategories($params);
        return $result['primary_category'] ?? null;
    }

    /**
     * 获取次要分类
     */
    public function getSecondaryCategories(array $params = []): array
    {
        $result = $this->getProductCategories($params);
        return $result['secondary_categories'] ?? [];
    }

    /**
     * 获取分类置信度
     */
    public function getCategoryConfidence(array $params = []): float
    {
        $result = $this->getProductCategories($params);
        return $result['confidence'] ?? 0.0;
    }

    /**
     * 检查是否包含特定分类
     */
    public function hasCategory(string $targetCategory, array $params = []): bool
    {
        $categories = $this->getAllCategories($params);
        
        foreach ($categories as $category) {
            if (stripos($category, $targetCategory) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取分类层级
     */
    public function getCategoryHierarchy(string $category): array
    {
        $parts = explode(' → ', $category);
        return array_map('trim', $parts);
    }

    /**
     * 获取顶级分类
     */
    public function getTopLevelCategories(array $params = []): array
    {
        $categories = $this->getAllCategories($params);
        $topLevel = [];
        
        foreach ($categories as $category) {
            $hierarchy = $this->getCategoryHierarchy($category);
            if (!empty($hierarchy)) {
                $topLevel[] = $hierarchy[0];
            }
        }
        
        return array_unique($topLevel);
    }

    /**
     * 设置产品分类场景
     */
    public function setProductCategoryScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'electronics_focus':
                $this->setCustomResponse([
                    'answer' => [
                        'categories' => [
                            'Electronics → Consumer Electronics → Mobile Phones',
                            'Electronics → Consumer Electronics → Tablets',
                            'Electronics → Components → Semiconductors'
                        ],
                        'confidence' => 0.95,
                        'primary_category' => 'Electronics → Consumer Electronics',
                        'secondary_categories' => [
                            'Electronics → Components'
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'mixed_categories':
                $this->setCustomResponse([
                    'answer' => [
                        'categories' => [
                            'Technology → Software → Business Software',
                            'Manufacturing → Industrial Equipment → Automation',
                            'Electronics → Consumer Electronics → Smart Devices'
                        ],
                        'confidence' => 0.78,
                        'primary_category' => 'Technology → Software',
                        'secondary_categories' => [
                            'Manufacturing → Industrial Equipment',
                            'Electronics → Consumer Electronics'
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'unclear_categories':
                $this->setCustomResponse([
                    'answer' => [
                        'categories' => [
                            'General → Miscellaneous → Various Products'
                        ],
                        'confidence' => 0.35,
                        'primary_category' => 'General → Miscellaneous',
                        'secondary_categories' => []
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
        }
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock Product Category AI Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getProductCategoryResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
