<?php

namespace tests\functional\ai_sdr\mockery\agents;

use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\AiSdrBuyerProfileAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\functional\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock AI SDR买家档案Agent
 * 
 * 模拟AiSdrBuyerProfileAgent的行为，用于测试
 */
class MockAiSdrBuyerProfileAgent extends AiSdrBuyerProfileAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据
        $responseData = $this->dataFactory->getBuyerProfileResponse($params);

        // 创建AiAgentProcessResponse对象
        $response = new AiAgentProcessResponse();
        $response->answer = json_encode($responseData['answer']); // 注意：真实Agent返回的answer是JSON字符串
        $response->recordId = $responseData['record_id'];
        $response->messageType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT;
        $response->function = $function ?: 'buyerProfileGeneration';

        return $response;
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取买家档案信息
     */
    public function getBuyerProfile(array $params = []): array
    {
        $response = $this->process($params);
        return $response->answer;
    }

    /**
     * 获取买家基本信息
     */
    public function getBuyerBasicInfo(array $params = []): array
    {
        $profile = $this->getBuyerProfile($params);
        
        return [
            'company_name' => $profile['company_name'] ?? '',
            'industry' => $profile['industry'] ?? '',
            'business_scale' => $profile['business_scale'] ?? '',
            'target_market' => $profile['target_market'] ?? []
        ];
    }

    /**
     * 获取采购信息
     */
    public function getPurchasingInfo(array $params = []): array
    {
        $profile = $this->getBuyerProfile($params);
        
        return [
            'main_products' => $profile['main_products'] ?? [],
            'purchasing_power' => $profile['purchasing_power'] ?? '',
            'preferred_suppliers' => $profile['preferred_suppliers'] ?? []
        ];
    }

    /**
     * 获取决策信息
     */
    public function getDecisionMakingInfo(array $params = []): array
    {
        $profile = $this->getBuyerProfile($params);
        
        return [
            'decision_makers' => $profile['decision_makers'] ?? [],
            'communication_preference' => $profile['communication_preference'] ?? ''
        ];
    }

    /**
     * 获取公司类型
     */
    public function getCompanyTypes(array $params = []): array
    {
        $profile = $this->getBuyerProfile($params);
        return $profile['company_type'] ?? [];
    }

    /**
     * 检查采购能力
     */
    public function hasPurchasingPower(array $params = []): bool
    {
        $purchasingInfo = $this->getPurchasingInfo($params);
        $power = $purchasingInfo['purchasing_power'] ?? '';
        
        return in_array(strtolower($power), ['high', 'medium']);
    }

    /**
     * 检查是否为目标买家
     */
    public function isTargetBuyer(array $targetCriteria, array $params = []): bool
    {
        $profile = $this->getBuyerProfile($params);
        
        // 检查行业匹配
        if (isset($targetCriteria['industry'])) {
            $buyerIndustry = $profile['industry'] ?? '';
            if (stripos($buyerIndustry, $targetCriteria['industry']) === false) {
                return false;
            }
        }
        
        // 检查公司类型匹配
        if (isset($targetCriteria['company_type'])) {
            $buyerTypes = $profile['company_type'] ?? [];
            $hasMatchingType = false;
            foreach ($buyerTypes as $type) {
                if (stripos($type, $targetCriteria['company_type']) !== false) {
                    $hasMatchingType = true;
                    break;
                }
            }
            if (!$hasMatchingType) {
                return false;
            }
        }
        
        // 检查采购能力
        if (isset($targetCriteria['min_purchasing_power'])) {
            if (!$this->hasPurchasingPower($params)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 设置买家档案场景
     */
    public function setBuyerProfileScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'high_value_distributor':
                $this->setCustomResponse([
                    'answer' => [
                        'company_name' => 'Premium Distribution Corp',
                        'industry' => 'Electronics Distribution',
                        'company_type' => ['Distributor', 'Wholesaler'],
                        'main_products' => ['Consumer Electronics', 'Industrial Equipment'],
                        'target_market' => ['North America', 'Europe'],
                        'purchasing_power' => 'High',
                        'decision_makers' => ['CEO', 'Purchasing Director'],
                        'preferred_suppliers' => ['Certified manufacturers', 'Long-term partners'],
                        'communication_preference' => 'Email',
                        'business_scale' => 'Large'
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'small_retailer':
                $this->setCustomResponse([
                    'answer' => [
                        'company_name' => 'Local Retail Store',
                        'industry' => 'Retail',
                        'company_type' => ['Retailer'],
                        'main_products' => ['Consumer Goods'],
                        'target_market' => ['Local Market'],
                        'purchasing_power' => 'Low',
                        'decision_makers' => ['Owner'],
                        'preferred_suppliers' => ['Local suppliers', 'Direct manufacturers'],
                        'communication_preference' => 'Phone',
                        'business_scale' => 'Small'
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'manufacturing_buyer':
                $this->setCustomResponse([
                    'answer' => [
                        'company_name' => 'Industrial Manufacturing Ltd',
                        'industry' => 'Manufacturing',
                        'company_type' => ['Manufacturer', 'OEM'],
                        'main_products' => ['Industrial Components', 'Raw Materials'],
                        'target_market' => ['B2B', 'Industrial'],
                        'purchasing_power' => 'High',
                        'decision_makers' => ['Procurement Manager', 'Engineering Director'],
                        'preferred_suppliers' => ['Certified suppliers', 'Quality-focused'],
                        'communication_preference' => 'Email',
                        'business_scale' => 'Medium'
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
        }
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock Buyer Profile Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getBuyerProfileResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
