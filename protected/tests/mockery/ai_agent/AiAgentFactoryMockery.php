<?php

namespace tests\functional\ai_sdr\mockery;

use Mockery;
use Mockery\MockInterface;
use common\library\ai_agent\AiAgentFactory;
use common\library\ai_agent\BaseAiAgent;
use common\library\ai_agent\proxy\AiAgentProxy;
use tests\functional\ai_sdr\mockery\agents\MockSdrLeadQualityAnalysisAgent;
use tests\functional\ai_sdr\mockery\agents\MockSdrEdmWriteAgent;
use tests\functional\ai_sdr\mockery\agents\MockSdrSellerIndustryAnalyze;
use tests\functional\ai_sdr\mockery\agents\MockAiSdrSellerProfileAgent;
use tests\functional\ai_sdr\mockery\agents\MockAiSdrBuyerProfileAgent;
use tests\functional\ai_sdr\mockery\agents\MockHomepageAiAgent;
use tests\functional\ai_sdr\mockery\agents\MockAiSdrProductCategoryAiAgent;
use tests\functional\ai_sdr\mockery\agents\MockSdrHutchLeadQualityAgent;
use tests\functional\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * AI Agent Factory Mockery实现
 * 
 * 使用Mockery框架Mock AiAgentFactory::createAgent方法
 * 支持SDR测试流程中所有AI Agent的Mock
 */
class AiAgentFactoryMockery
{
    /**
     * @var MockInterface|null
     */
    private static $factoryMock = null;
    
    /**
     * @var array 存储各个Agent的Mock实例
     */
    private static $agentMocks = [];
    
    /**
     * @var AgentResponseDataFactory
     */
    private static $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private static $callLog = [];
    
    /**
     * @var array 当前测试场景配置
     */
    private static $currentScenario = [];

    /**
     * 初始化Mockery环境
     */
    public static function initialize(): void
    {
        self::$dataFactory = new AgentResponseDataFactory();
        self::$callLog = [];
        self::$currentScenario = [];
        self::createFactoryMock();
    }

    /**
     * 清理Mockery环境
     */
    public static function cleanup(): void
    {
        if (self::$factoryMock) {
            Mockery::close();
            self::$factoryMock = null;
        }
        self::$agentMocks = [];
        self::$callLog = [];
        self::$currentScenario = [];
    }

    /**
     * 创建Factory的Mock
     */
    private static function createFactoryMock(): void
    {
        self::$factoryMock = Mockery::mock('alias:' . AiAgentFactory::class);
        
        self::$factoryMock->shouldReceive('createAgent')
            ->andReturnUsing(function($sceneType, $clientId, $userId, $businessType = null, $question = '', $params = []) {
                return self::handleCreateAgent($sceneType, $clientId, $userId, $businessType, $question, $params);
            });
    }

    /**
     * 处理createAgent调用
     */
    private static function handleCreateAgent(int $sceneType, int $clientId, int $userId, $businessType = null, string $question = '', array $params = [])
    {
        // 记录调用日志
        self::$callLog[] = [
            'method' => 'createAgent',
            'sceneType' => $sceneType,
            'clientId' => $clientId,
            'userId' => $userId,
            'businessType' => $businessType,
            'question' => $question,
            'params' => $params,
            'timestamp' => time()
        ];

        // 根据sceneType返回对应的Mock Agent
        return self::createMockAgent($sceneType, $clientId, $userId);
    }

    /**
     * 创建对应的Mock Agent
     */
    private static function createMockAgent(int $sceneType, int $clientId, int $userId)
    {
        $agentKey = "{$sceneType}_{$clientId}_{$userId}";
        
        if (!isset(self::$agentMocks[$agentKey])) {
            self::$agentMocks[$agentKey] = self::instantiateMockAgent($sceneType, $clientId, $userId);
        }
        
        return self::$agentMocks[$agentKey];
    }

    /**
     * 实例化具体的Mock Agent
     */
    private static function instantiateMockAgent(int $sceneType, int $clientId, int $userId)
    {
        switch ($sceneType) {
            case \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS:
                return new MockSdrLeadQualityAnalysisAgent($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER:
                return new MockSdrEdmWriteAgent($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE:
                return new MockSdrSellerIndustryAnalyze($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION:
                return new MockAiSdrSellerProfileAgent($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION:
                return new MockAiSdrBuyerProfileAgent($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH:
                return new MockHomepageAiAgent($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY:
                return new MockAiSdrProductCategoryAiAgent($clientId, $userId, self::$dataFactory);
                
            case \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS:
                return new MockSdrHutchLeadQualityAgent($clientId, $userId, self::$dataFactory);
                
            default:
                throw new \RuntimeException("Unsupported scene type for mock: {$sceneType}");
        }
    }

    /**
     * 设置测试场景
     */
    public static function setTestScenario(string $scenarioName, array $config = []): void
    {
        self::$currentScenario = [
            'name' => $scenarioName,
            'config' => $config
        ];
        
        // 将场景配置传递给数据工厂
        self::$dataFactory->setScenario($scenarioName, $config);
    }

    /**
     * 获取调用日志
     */
    public static function getCallLog(): array
    {
        return self::$callLog;
    }

    /**
     * 清空调用日志
     */
    public static function clearCallLog(): void
    {
        self::$callLog = [];
    }

    /**
     * 验证是否调用了指定的Agent
     */
    public static function wasAgentCalled(int $sceneType): bool
    {
        foreach (self::$callLog as $call) {
            if ($call['sceneType'] === $sceneType) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取指定Agent的调用次数
     */
    public static function getAgentCallCount(int $sceneType): int
    {
        $count = 0;
        foreach (self::$callLog as $call) {
            if ($call['sceneType'] === $sceneType) {
                $count++;
            }
        }
        return $count;
    }

    /**
     * 获取指定Agent的最后一次调用参数
     */
    public static function getLastAgentCallParams(int $sceneType): ?array
    {
        $lastCall = null;
        foreach (self::$callLog as $call) {
            if ($call['sceneType'] === $sceneType) {
                $lastCall = $call;
            }
        }
        return $lastCall;
    }

    /**
     * 设置特定Agent的响应数据
     */
    public static function setAgentResponse(int $sceneType, array $responseData): void
    {
        self::$dataFactory->setCustomResponse($sceneType, $responseData);
    }

    /**
     * 获取当前场景配置
     */
    public static function getCurrentScenario(): array
    {
        return self::$currentScenario;
    }

    /**
     * 重置所有Mock状态
     */
    public static function reset(): void
    {
        self::cleanup();
        self::initialize();
    }
}
