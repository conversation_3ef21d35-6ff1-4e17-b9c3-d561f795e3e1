<?php

namespace tests\functional\ai_sdr\mockery\examples;

use PHPUnit\Framework\TestCase;
use tests\functional\ai_sdr\mockery\traits\MockeryAgentTrait;
use tests\functional\ai_sdr\mockery\AiAgentFactoryMockery;
use common\library\ai_sdr\Constant;
use common\library\ai_agent\AiAgentFactory;

/**
 * AI Agent Mockery框架使用示例
 * 
 * 展示如何使用Mockery框架Mock AI Agent进行测试
 */
class AiAgentMockeryExampleTest extends TestCase
{
    use MockeryAgentTrait;
    
    private $clientId = 12345;
    private $userId = 67890;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpAiAgentMockery();
    }

    protected function tearDown(): void
    {
        $this->tearDownAiAgentMockery();
        parent::tearDown();
    }

    /**
     * 示例1: 基本的Agent Mock使用
     */
    public function testBasicAgentMocking()
    {
        // 设置质量分析场景
        $this->setQualityAnalysisScenario('high_quality');
        
        // 创建Agent并调用
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $result = $agent->process([
            'client_profile' => json_encode(['company' => 'Test Corp']),
            'buyer_profile' => json_encode(['industry' => 'Technology'])
        ]);
        
        // 验证结果（质量分析Agent返回数组格式）
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $result['answer']['lead_quality']);
        $this->assertGreaterThan(0.8, $result['answer']['confidence']);
        
        // 验证Agent被调用
        $this->assertAgentWasCalled(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS);
    }

    /**
     * 示例2: 自定义响应数据
     */
    public function testCustomResponseData()
    {
        // 设置自定义响应
        $customResponse = [
            'answer' => [
                'lead_quality' => Constant::LEAD_QUALITY_MEDIUM,
                'confidence' => 0.78,
                'reason' => ['Custom reason 1', 'Custom reason 2'],
                'score' => 75,
                'custom_field' => 'Custom value'
            ],
            'record_id' => 999888
        ];
        
        $this->setAgentResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $customResponse
        );
        
        // 创建Agent并调用
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $result = $agent->process(['test' => 'data']);
        
        // 验证自定义响应（质量分析Agent返回数组格式）
        $this->assertEquals(Constant::LEAD_QUALITY_MEDIUM, $result['answer']['lead_quality']);
        $this->assertEquals(0.78, $result['answer']['confidence']);
        $this->assertEquals('Custom value', $result['answer']['custom_field']);
        $this->assertEquals(999888, $result['record_id']);
    }

    /**
     * 示例3: 多个Agent的协同测试
     */
    public function testMultipleAgentsWorkflow()
    {
        // 设置多个Agent的场景
        $this->setQualityAnalysisScenario('high_quality');
        $this->setEdmWriteScenario('standard_emails');
        $this->setSellerIndustryAnalysisScenario('tech_focused');
        
        // 模拟业务流程中的多个Agent调用
        $qualityAgent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $edmAgent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            $this->clientId,
            $this->userId
        );
        
        $industryAgent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE,
            $this->clientId,
            $this->userId
        );
        
        // 执行调用
        $qualityResult = $qualityAgent->process(['test' => 'quality']);
        $edmResult = $edmAgent->process(['test' => 'edm']);
        $industryResult = $industryAgent->process(['test' => 'industry']);
        
        // 验证所有Agent都被调用
        $this->assertAllExpectedAgentsCalled([
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE
        ]);
        
        // 验证调用次数
        $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, 1);
        $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER, 1);
        $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE, 1);
    }

    /**
     * 示例4: 异常处理测试
     */
    public function testAgentExceptionHandling()
    {
        // 模拟Agent异常
        $this->simulateAgentException(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            'Quality analysis service temporarily unavailable'
        );
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        // 验证异常被抛出
        $this->expectException(\common\library\ai_agent\exception\AiAgentException::class);
        $this->expectExceptionMessage('Quality analysis service temporarily unavailable');
        
        $agent->processWithExceptionHandling(['test' => 'data']);
    }

    /**
     * 示例5: 参数验证测试
     */
    public function testAgentParameterValidation()
    {
        $this->setQualityAnalysisScenario('high_quality');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $testParams = [
            'client_profile' => json_encode(['company' => 'Test Corp']),
            'buyer_profile' => json_encode(['industry' => 'Technology']),
            'additional_data' => 'test_value'
        ];
        
        $agent->process($testParams);
        
        // 验证Agent被调用
        $this->assertAgentWasCalled(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS);

        // 验证Agent实例的process方法被正确调用
        $this->assertTrue($agent->wasCalled());
        $this->assertEquals($testParams, $agent->getLastCallParams());
    }

    /**
     * 示例6: 调用统计和监控
     */
    public function testAgentCallStatistics()
    {
        $this->setQualityAnalysisScenario('high_quality');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        // 多次调用
        $agent->process(['call' => 1]);
        $agent->process(['call' => 2]);
        $agent->process(['call' => 3]);
        
        // 验证调用统计（createAgent只调用了1次，但process调用了3次）
        $statistics = $this->getAgentCallStatistics();
        $this->assertEquals(1, $statistics[\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS]);

        // 验证Agent实例的调用次数
        $this->assertEquals(3, $agent->getCallCount());

        // 验证最后一次调用的参数
        $this->assertEquals(['call' => 3], $agent->getLastCallParams());
    }

    /**
     * 示例7: 场景配置测试
     */
    public function testScenarioConfiguration()
    {
        // 设置复杂的测试场景
        $this->setAiAgentTestScenario('complex_scenario', [
            'lead_quality' => Constant::LEAD_QUALITY_HIGH,
            'confidence_threshold' => 0.9,
            'industry_focus' => 'Technology',
            'email_rounds' => 2
        ]);
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $result = $agent->process(['test' => 'scenario']);
        
        // 验证场景配置生效（质量分析Agent返回数组格式）
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $result['answer']['lead_quality']);
        
        // 获取当前场景配置
        $currentScenario = AiAgentFactoryMockery::getCurrentScenario();
        $this->assertEquals('complex_scenario', $currentScenario['name']);
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $currentScenario['config']['lead_quality']);
    }

    /**
     * 示例8: 重置和清理测试
     */
    public function testResetAndCleanup()
    {
        // 执行一些操作
        $this->setQualityAnalysisScenario('high_quality');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $agent->process(['test' => 'before_reset']);
        
        // 验证有调用记录
        $this->assertAgentWasCalled(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS);
        
        // 重置状态
        $this->resetAiAgentMockery();
        
        // 验证状态已重置
        $callLog = $this->getAgentCallLog();
        $this->assertEmpty($callLog);
        
        // 重新设置并测试
        $this->setQualityAnalysisScenario('medium_quality');
        
        $newAgent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->clientId,
            $this->userId
        );
        
        $result = $newAgent->process(['test' => 'after_reset']);
        
        // 验证新的设置生效（质量分析Agent返回数组格式）
        $this->assertEquals(Constant::LEAD_QUALITY_MEDIUM, $result['answer']['lead_quality']);
        $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, 1);
    }
}
