<?php

namespace tests\mockery\inner_api;

use common\library\api\InnerApi;

/**
 * Mock InnerApi 实现类
 * 
 * 继承自 InnerApi，重写关键方法以支持测试
 */
class MockInnerApi extends InnerApi
{
    /**
     * @var string 模块名
     */
    private $mockModule;

    /**
     * @var array 预设响应数据
     */
    private $mockResponses = [];

    /**
     * @var array 预设异常
     */
    private $mockExceptions = [];

    /**
     * @var array 调用日志
     */
    private static $callLog = [];

    /**
     * 构造函数
     */
    public function __construct($module)
    {
        $this->mockModule = $module;
        // 不调用父类构造函数，避免配置检查
    }

    /**
     * 重写 call 方法
     */
    public function call($interface, $param)
    {
        // 记录调用日志
        self::$callLog[] = [
            'module' => $this->mockModule,
            'interface' => $interface,
            'params' => $param,
            'timestamp' => microtime(true)
        ];

        $key = $this->getResponseKey($interface);

        // 检查是否有预设异常
        if (isset($this->mockExceptions[$key])) {
            $exception = $this->mockExceptions[$key];
            if (is_string($exception)) {
                throw new \Exception($exception);
            } elseif (is_array($exception)) {
                $exceptionClass = $exception['class'] ?? \Exception::class;
                $message = $exception['message'] ?? 'Mock exception';
                $code = $exception['code'] ?? 0;
                throw new $exceptionClass($message, $code);
            }
        }

        // 返回预设的响应数据
        if (isset($this->mockResponses[$key])) {
            return $this->mockResponses[$key];
        }

        // 返回默认成功响应
        return [
            'code' => 0,
            'message' => 'success',
            'data' => []
        ];
    }

    /**
     * 设置响应数据
     */
    public function setMockResponse(string $interface, array $responseData): void
    {
        $key = $this->getResponseKey($interface);
        $this->mockResponses[$key] = $responseData;
    }

    /**
     * 设置异常
     */
    public function setMockException(string $interface, $exception): void
    {
        $key = $this->getResponseKey($interface);
        $this->mockExceptions[$key] = $exception;
    }

    /**
     * 获取响应键
     */
    private function getResponseKey(string $interface): string
    {
        return $interface;
    }

    /**
     * 获取模块名
     */
    public function getModule()
    {
        return $this->mockModule;
    }

    /**
     * 获取接口名
     */
    public function getInterface()
    {
        return $this->interface ?? '';
    }

    /**
     * 获取域名
     */
    public function getDomain()
    {
        return 'mock.domain.com';
    }

    /**
     * 获取调用日志
     */
    public static function getCallLog(): array
    {
        return self::$callLog;
    }

    /**
     * 清空调用日志
     */
    public static function clearCallLog(): void
    {
        self::$callLog = [];
    }

    /**
     * 检查是否被调用
     */
    public static function wasApiCalled(string $module, string $interface): bool
    {
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取调用次数
     */
    public static function getApiCallCount(string $module, string $interface): int
    {
        $count = 0;
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                $count++;
            }
        }
        return $count;
    }

    /**
     * 获取最后一次调用参数
     */
    public static function getLastApiCallParams(string $module, string $interface): ?array
    {
        $lastCall = null;
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                $lastCall = $call;
            }
        }
        return $lastCall ? $lastCall['params'] : null;
    }

    // 重写其他方法以支持链式调用
    public function setHttpMethod($method)
    {
        $this->httpMethod = $method;
        return $this;
    }

    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
        return $this;
    }

    public function setCheckRspJsonCode($flag)
    {
        $this->checkRspJsonCode = $flag;
        return $this;
    }

    public function addHeader($header)
    {
        if (is_array($header)) {
            $this->header = array_merge($this->header ?? [], $header);
        } else {
            $this->header[] = $header;
        }
        return $this;
    }

    public function setApiErrorHandler(callable $handler)
    {
        $this->apiErrorHandler = $handler;
        return $this;
    }

    public function setTimeoutHandler(callable $handler)
    {
        $this->timeoutHandler = $handler;
        return $this;
    }

    public function setServerErrorHandler(callable $handler)
    {
        $this->serverErrorHandler = $handler;
        return $this;
    }

    public function setApiNoCodeHandler(callable $handler)
    {
        $this->apiNoCodeHandler = $handler;
        return $this;
    }

    public function setHttpCodeHandler(callable $handler)
    {
        $this->httpCodeHandler = $handler;
        return $this;
    }

    public function setRespondJsonCodeField($field)
    {
        $this->rspJsonCodeField = $field;
        return $this;
    }

    public function setCheckRspHttpCode($flag)
    {
        $this->checkRspHttpCode = $flag;
        return $this;
    }

    public function setJsonDecode($flag)
    {
        $this->jsonDecode = $flag;
        return $this;
    }

    public function resetHeader()
    {
        $this->resetHeader = true;
        $this->header = [];
        return $this;
    }

    public function setAccessLog($flag)
    {
        $this->accessLog = $flag;
        return $this;
    }

    public function setInterfaceHandler($handler)
    {
        $this->interfaceHandler = $handler;
        return $this;
    }

    /**
     * 重置 Mock 状态
     */
    public function resetMock(): void
    {
        $this->mockResponses = [];
        $this->mockExceptions = [];
    }
}
