<?php

namespace tests\mockery\inner_api;

use common\library\api\InnerApi;
use Mockery;

/**
 * InnerApi Mockery 工厂类
 * 
 * 提供 InnerApi 的 Mock 功能，支持多种测试场景
 */
class InnerApiFactoryMockery
{
    /**
     * @var array Mock 实例缓存
     */
    private static $mockInstances = [];

    /**
     * @var array API 调用日志
     */
    private static $callLog = [];

    /**
     * @var array 预设的响应数据
     */
    private static $responseData = [];

    /**
     * @var array 预设的异常
     */
    private static $exceptions = [];

    /**
     * @var bool 是否已初始化
     */
    private static $initialized = false;

    /**
     * @var array 默认响应配置
     */
    private static $defaultResponses = [
        'success' => [
            'code' => 0,
            'message' => 'success',
            'data' => []
        ],
        'error' => [
            'code' => -1,
            'message' => 'Internal server error',
            'data' => null
        ],
        'timeout' => [
            'code' => -1,
            'message' => 'timeout',
            'data' => null
        ]
    ];

    /**
     * 初始化 Mockery 环境
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }

        // 重写 InnerApi 构造函数
        self::setupInnerApiMock();
        self::$initialized = true;
    }

    /**
     * 设置 InnerApi Mock
     */
    private static function setupInnerApiMock(): void
    {
        // 这里我们使用 Mockery 来 mock InnerApi 类
        // 由于 InnerApi 是在运行时创建的，我们需要拦截其创建过程
    }

    /**
     * 创建 Mock InnerApi 实例
     */
    public static function createMockInnerApi(string $module): InnerApi
    {
        $mockKey = "inner_api_{$module}";
        
        if (!isset(self::$mockInstances[$mockKey])) {
            $mock = Mockery::mock(InnerApi::class);
            
            // 设置基本方法的默认行为
            $mock->shouldReceive('setHttpMethod')->andReturnSelf();
            $mock->shouldReceive('setTimeout')->andReturnSelf();
            $mock->shouldReceive('setCheckRspJsonCode')->andReturnSelf();
            $mock->shouldReceive('addHeader')->andReturnSelf();
            $mock->shouldReceive('setApiErrorHandler')->andReturnSelf();
            $mock->shouldReceive('setTimeoutHandler')->andReturnSelf();
            $mock->shouldReceive('setServerErrorHandler')->andReturnSelf();
            $mock->shouldReceive('getModule')->andReturn($module);
            $mock->shouldReceive('getInterface')->andReturn('');
            $mock->shouldReceive('getDomain')->andReturn('mock.domain.com');

            // 设置 call 方法的行为
            $mock->shouldReceive('call')->andReturnUsing(function ($interface, $params) use ($module) {
                return self::handleApiCall($module, $interface, $params);
            });

            self::$mockInstances[$mockKey] = $mock;
        }

        return self::$mockInstances[$mockKey];
    }

    /**
     * 处理 API 调用
     */
    private static function handleApiCall(string $module, string $interface, $params): array
    {
        // 记录调用日志
        self::$callLog[] = [
            'module' => $module,
            'interface' => $interface,
            'params' => $params,
            'timestamp' => microtime(true)
        ];

        $key = self::getResponseKey($module, $interface);

        // 检查是否有预设异常
        if (isset(self::$exceptions[$key])) {
            $exception = self::$exceptions[$key];
            if (is_string($exception)) {
                throw new \Exception($exception);
            } elseif (is_array($exception)) {
                $exceptionClass = $exception['class'] ?? \Exception::class;
                $message = $exception['message'] ?? 'Mock exception';
                $code = $exception['code'] ?? 0;
                throw new $exceptionClass($message, $code);
            }
        }

        // 返回预设的响应数据
        if (isset(self::$responseData[$key])) {
            return self::$responseData[$key];
        }

        // 返回默认成功响应
        return self::$defaultResponses['success'];
    }

    /**
     * 设置 API 响应数据
     */
    public static function setApiResponse(string $module, string $interface, array $responseData): void
    {
        $key = self::getResponseKey($module, $interface);
        self::$responseData[$key] = $responseData;
    }

    /**
     * 设置 API 异常
     */
    public static function setApiException(string $module, string $interface, $exception): void
    {
        $key = self::getResponseKey($module, $interface);
        self::$exceptions[$key] = $exception;
    }

    /**
     * 设置测试场景
     */
    public static function setTestScenario(string $scenarioName, array $config = []): void
    {
        switch ($scenarioName) {
            case 'ai_background_check_success':
                self::setAiBackgroundCheckSuccessScenario($config);
                break;
            case 'ai_background_check_error':
                self::setAiBackgroundCheckErrorScenario($config);
                break;
            case 'ai_background_check_timeout':
                self::setAiBackgroundCheckTimeoutScenario($config);
                break;
            case 'standardize_content_success':
                self::setStandardizeContentSuccessScenario($config);
                break;
            case 'standardize_content_no_match':
                self::setStandardizeContentNoMatchScenario($config);
                break;
        }
    }

    /**
     * 设置 AI 背景调研成功场景
     */
    private static function setAiBackgroundCheckSuccessScenario(array $config): void
    {
        $defaultResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'report_id' => $config['report_id'] ?? 'bg_001',
                'status' => 'completed',
                'report' => [
                    'company_name' => $config['company_name'] ?? 'Test Company Ltd',
                    'homepage' => $config['homepage'] ?? 'https://test-company.com',
                    'has_company' => $config['has_company'] ?? 1,
                    'has_contacts' => $config['has_contacts'] ?? 1,
                    'employees_min' => $config['employees_min'] ?? 100,
                    'employees_max' => $config['employees_max'] ?? 500,
                    'industry_ids' => $config['industry_ids'] ?? [1, 2],
                    'main_products' => $config['main_products'] ?? 'Software, Cloud Services',
                    'company_description' => $config['company_description'] ?? 'Leading technology company'
                ],
                'time_used' => $config['time_used'] ?? 30
            ]
        ];

        self::setApiResponse('ai_background_check', 'aiBackgroundCheck', $defaultResponse);
    }

    /**
     * 设置标准化内容成功场景
     */
    private static function setStandardizeContentSuccessScenario(array $config): void
    {
        $defaultResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'std_id' => $config['std_id'] ?? 123,
                'std_name' => $config['std_name'] ?? 'Technology',
                'is_adopted' => $config['is_adopted'] ?? true,
                'confidence' => $config['confidence'] ?? 0.95,
                'original_content' => $config['original_content'] ?? 'tech',
                'standardized_content' => $config['standardized_content'] ?? 'Technology'
            ]
        ];

        self::setApiResponse('ai_background_check', 'standardizeContent', $defaultResponse);
    }

    /**
     * 设置标准化内容无匹配场景
     */
    private static function setStandardizeContentNoMatchScenario(array $config): void
    {
        $defaultResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'std_id' => 0,
                'std_name' => '',
                'is_adopted' => false,
                'confidence' => $config['confidence'] ?? 0.3,
                'original_content' => $config['original_content'] ?? 'unknown_industry',
                'standardized_content' => ''
            ]
        ];

        self::setApiResponse('ai_background_check', 'standardizeContent', $defaultResponse);
    }

    /**
     * 设置错误场景
     */
    private static function setAiBackgroundCheckErrorScenario(array $config): void
    {
        $errorResponse = [
            'code' => $config['code'] ?? -1,
            'message' => $config['message'] ?? 'Service unavailable',
            'data' => null
        ];

        self::setApiResponse('ai_background_check', 'aiBackgroundCheck', $errorResponse);
    }

    /**
     * 设置超时场景
     */
    private static function setAiBackgroundCheckTimeoutScenario(array $config): void
    {
        self::setApiException('ai_background_check', 'aiBackgroundCheck', [
            'class' => \Exception::class,
            'message' => $config['message'] ?? 'Request timeout',
            'code' => $config['code'] ?? 28
        ]);
    }

    /**
     * 获取响应键
     */
    private static function getResponseKey(string $module, string $interface): string
    {
        return "{$module}::{$interface}";
    }

    /**
     * 检查 API 是否被调用
     */
    public static function wasApiCalled(string $module, string $interface): bool
    {
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取 API 调用次数
     */
    public static function getApiCallCount(string $module, string $interface): int
    {
        $count = 0;
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                $count++;
            }
        }
        return $count;
    }

    /**
     * 获取最后一次 API 调用参数
     */
    public static function getLastApiCallParams(string $module, string $interface): ?array
    {
        $lastCall = null;
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                $lastCall = $call;
            }
        }
        return $lastCall ? $lastCall['params'] : null;
    }

    /**
     * 获取调用日志
     */
    public static function getCallLog(): array
    {
        return self::$callLog;
    }

    /**
     * 清空调用日志
     */
    public static function clearCallLog(): void
    {
        self::$callLog = [];
    }

    /**
     * 重置所有状态
     */
    public static function reset(): void
    {
        self::$callLog = [];
        self::$responseData = [];
        self::$exceptions = [];
    }

    /**
     * 清理 Mockery 环境
     */
    public static function cleanup(): void
    {
        self::$mockInstances = [];
        self::reset();
        self::$initialized = false;
        Mockery::close();
    }
}
