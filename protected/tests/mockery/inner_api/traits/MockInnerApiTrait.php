<?php

namespace tests\mockery\inner_api\traits;

use tests\mockery\inner_api\InnerApiFactoryMockery;

/**
 * Mock InnerApi 测试辅助 Trait
 * 
 * 为测试类提供便捷的 InnerApi Mock 功能
 */
trait MockInnerApiTrait
{
    /**
     * @var bool 是否已初始化 Mockery
     */
    private static $innerApiMockeryInitialized = false;

    /**
     * 设置 InnerApi Mockery 环境
     */
    protected static function setUpInnerApiMockery(): void
    {
        if (!self::$innerApiMockeryInitialized) {
            InnerApiFactoryMockery::initialize();
            self::$innerApiMockeryInitialized = true;
        }
    }

    /**
     * 清理 InnerApi Mockery 环境
     */
    protected static function tearDownInnerApiMockery(): void
    {
        InnerApiFactoryMockery::cleanup();
        self::$innerApiMockeryInitialized = false;
    }

    /**
     * 重置 InnerApi Mockery 状态
     */
    protected static function resetInnerApiMockery(): void
    {
        InnerApiFactoryMockery::reset();
    }

    /**
     * 设置测试场景
     */
    protected function setInnerApiTestScenario(string $scenarioName, array $config = []): void
    {
        InnerApiFactoryMockery::setTestScenario($scenarioName, $config);
    }

    /**
     * 设置特定 API 的响应数据
     */
    protected function setApiResponse(string $module, string $interface, array $responseData): void
    {
        InnerApiFactoryMockery::setApiResponse($module, $interface, $responseData);
    }

    /**
     * 设置特定 API 的异常
     */
    protected function setApiException(string $module, string $interface, $exception): void
    {
        InnerApiFactoryMockery::setApiException($module, $interface, $exception);
    }

    /**
     * 验证 API 是否被调用
     */
    protected function assertApiWasCalled(string $module, string $interface, string $message = ''): void
    {
        $wasCalled = InnerApiFactoryMockery::wasApiCalled($module, $interface);
        $this->assertTrue($wasCalled, $message ?: "API {$module}::{$interface} was not called");
    }

    /**
     * 验证 API 没有被调用
     */
    protected function assertApiWasNotCalled(string $module, string $interface, string $message = ''): void
    {
        $wasCalled = InnerApiFactoryMockery::wasApiCalled($module, $interface);
        $this->assertFalse($wasCalled, $message ?: "API {$module}::{$interface} was called unexpectedly");
    }

    /**
     * 验证 API 调用次数
     */
    protected function assertApiCallCount(string $module, string $interface, int $expectedCount, string $message = ''): void
    {
        $actualCount = InnerApiFactoryMockery::getApiCallCount($module, $interface);
        $this->assertEquals($expectedCount, $actualCount, $message ?: "API call count mismatch for {$module}::{$interface}");
    }

    /**
     * 验证 API 调用参数
     */
    protected function assertApiCalledWith(string $module, string $interface, array $expectedParams, string $message = ''): void
    {
        $lastCall = InnerApiFactoryMockery::getLastApiCallParams($module, $interface);
        $this->assertNotNull($lastCall, "API {$module}::{$interface} was not called");
        
        foreach ($expectedParams as $key => $expectedValue) {
            $this->assertArrayHasKey($key, $lastCall, "Parameter '{$key}' not found in API call");
            $this->assertEquals($expectedValue, $lastCall[$key], $message ?: "Parameter '{$key}' value mismatch");
        }
    }

    /**
     * 获取 API 调用日志
     */
    protected function getApiCallLog(): array
    {
        return InnerApiFactoryMockery::getCallLog();
    }

    /**
     * 清空 API 调用日志
     */
    protected function clearApiCallLog(): void
    {
        InnerApiFactoryMockery::clearCallLog();
    }

    // === AI Background Check Service 专用方法 ===

    /**
     * 设置 AI 背景调研成功场景
     */
    protected function setAiBackgroundCheckSuccessScenario(array $config = []): void
    {
        $this->setInnerApiTestScenario('ai_background_check_success', $config);
    }

    /**
     * 设置 AI 背景调研错误场景
     */
    protected function setAiBackgroundCheckErrorScenario(array $config = []): void
    {
        $this->setInnerApiTestScenario('ai_background_check_error', $config);
    }

    /**
     * 设置 AI 背景调研超时场景
     */
    protected function setAiBackgroundCheckTimeoutScenario(array $config = []): void
    {
        $this->setInnerApiTestScenario('ai_background_check_timeout', $config);
    }

    /**
     * 设置标准化内容成功场景
     */
    protected function setStandardizeContentSuccessScenario(array $config = []): void
    {
        $this->setInnerApiTestScenario('standardize_content_success', $config);
    }

    /**
     * 设置标准化内容无匹配场景
     */
    protected function setStandardizeContentNoMatchScenario(array $config = []): void
    {
        $this->setInnerApiTestScenario('standardize_content_no_match', $config);
    }

    /**
     * 验证 AI 背景调研 API 被调用
     */
    protected function assertAiBackgroundCheckWasCalled(string $message = ''): void
    {
        $this->assertApiWasCalled('ai_background_check', 'aiBackgroundCheck', $message);
    }

    /**
     * 验证标准化内容 API 被调用
     */
    protected function assertStandardizeContentWasCalled(string $message = ''): void
    {
        $this->assertApiWasCalled('ai_background_check', 'standardizeContent', $message);
    }

    /**
     * 验证 AI 背景调研调用参数
     */
    protected function assertAiBackgroundCheckCalledWith(array $expectedParams, string $message = ''): void
    {
        $this->assertApiCalledWith('ai_background_check', 'aiBackgroundCheck', $expectedParams, $message);
    }

    /**
     * 验证标准化内容调用参数
     */
    protected function assertStandardizeContentCalledWith(array $expectedParams, string $message = ''): void
    {
        $this->assertApiCalledWith('ai_background_check', 'standardizeContent', $expectedParams, $message);
    }

    // === 常用测试场景快捷方法 ===

    /**
     * 设置行业标准化成功场景
     */
    protected function setIndustryStandardizationSuccess(string $industry = 'Technology', int $stdId = 123): void
    {
        $this->setStandardizeContentSuccessScenario([
            'std_id' => $stdId,
            'std_name' => $industry,
            'is_adopted' => true,
            'confidence' => 0.95,
            'original_content' => strtolower($industry),
            'standardized_content' => $industry
        ]);
    }

    /**
     * 设置产品标准化成功场景
     */
    protected function setProductStandardizationSuccess(string $product = 'Software', int $stdId = 456): void
    {
        $this->setStandardizeContentSuccessScenario([
            'std_id' => $stdId,
            'std_name' => $product,
            'is_adopted' => true,
            'confidence' => 0.90,
            'original_content' => strtolower($product),
            'standardized_content' => $product
        ]);
    }

    /**
     * 设置标准化失败场景（置信度过低）
     */
    protected function setStandardizationLowConfidence(string $content = 'unknown_content'): void
    {
        $this->setStandardizeContentNoMatchScenario([
            'confidence' => 0.2,
            'original_content' => $content
        ]);
    }

    /**
     * 设置 API 服务不可用场景
     */
    protected function setApiServiceUnavailable(string $module = 'ai_background_check', string $interface = 'standardizeContent'): void
    {
        $this->setApiException($module, $interface, [
            'class' => \Exception::class,
            'message' => 'Service temporarily unavailable',
            'code' => 503
        ]);
    }

    /**
     * 设置网络超时场景
     */
    protected function setNetworkTimeout(string $module = 'ai_background_check', string $interface = 'standardizeContent'): void
    {
        $this->setApiException($module, $interface, [
            'class' => \Exception::class,
            'message' => 'Request timeout',
            'code' => 28
        ]);
    }

    /**
     * 获取 API 调用统计
     */
    protected function getApiCallStatistics(): array
    {
        $callLog = $this->getApiCallLog();
        $statistics = [];
        
        foreach ($callLog as $call) {
            $key = $call['module'] . '::' . $call['interface'];
            if (!isset($statistics[$key])) {
                $statistics[$key] = 0;
            }
            $statistics[$key]++;
        }
        
        return $statistics;
    }

    /**
     * 打印 API 调用统计（用于调试）
     */
    protected function printApiCallStatistics(): void
    {
        $statistics = $this->getApiCallStatistics();
        echo "\n=== API Call Statistics ===\n";
        foreach ($statistics as $api => $count) {
            echo "{$api}: {$count} calls\n";
        }
        echo "===========================\n";
    }

    /**
     * 验证所有预期的 API 都被调用了
     */
    protected function assertAllExpectedApisCalled(array $expectedApis, string $message = ''): void
    {
        foreach ($expectedApis as $api) {
            list($module, $interface) = explode('::', $api);
            $this->assertApiWasCalled($module, $interface, $message);
        }
    }

    /**
     * 验证只有预期的 API 被调用了
     */
    protected function assertOnlyExpectedApisCalled(array $expectedApis, string $message = ''): void
    {
        $callLog = $this->getApiCallLog();
        $actualApis = array_unique(array_map(function($call) {
            return $call['module'] . '::' . $call['interface'];
        }, $callLog));
        
        sort($expectedApis);
        sort($actualApis);
        
        $this->assertEquals($expectedApis, $actualApis, $message ?: 'Unexpected APIs were called');
    }
}
