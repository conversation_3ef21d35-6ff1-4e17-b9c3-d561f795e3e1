#!/bin/bash

# InnerApi Mockery 测试运行脚本
# 用于运行 InnerApi Mock 相关的测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/Users/<USER>/dev/xiaoman/crm"
PHPUNIT_PATH="/opt/homebrew/bin/php /Users/<USER>/dev/xiaoman/external/vendor/phpunit/phpunit/phpunit"
PHPUNIT_CONFIG="$PROJECT_ROOT/protected/tests/phpunit.xml"

echo -e "${BLUE}=== InnerApi Mockery 测试套件 ===${NC}"
echo "项目路径: $PROJECT_ROOT"
echo "PHPUnit 配置: $PHPUNIT_CONFIG"
echo ""

# 检查必要文件是否存在
check_files() {
    echo -e "${YELLOW}检查测试文件...${NC}"
    
    local files=(
        "protected/tests/mockery/inner_api/InnerApiFactoryMockery.php"
        "protected/tests/mockery/inner_api/traits/MockInnerApiTrait.php"
        "protected/tests/mockery/inner_api/MockInnerApi.php"
        "protected/tests/unit/ai_sdr/AiBackgroundCheckServiceSimpleMockTest.php"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            echo -e "${GREEN}✓${NC} $file"
        else
            echo -e "${RED}✗${NC} $file (缺失)"
            exit 1
        fi
    done
    echo ""
}

# 运行特定测试
run_test() {
    local test_file=$1
    local test_name=$2
    
    echo -e "${BLUE}运行测试: $test_name${NC}"
    echo "文件: $test_file"
    echo ""
    
    cd "$PROJECT_ROOT"
    
    if $PHPUNIT_PATH --configuration "$PHPUNIT_CONFIG" "$test_file"; then
        echo -e "${GREEN}✓ $test_name 测试通过${NC}"
    else
        echo -e "${RED}✗ $test_name 测试失败${NC}"
        return 1
    fi
    echo ""
}

# 运行所有 InnerApi Mock 测试
run_all_tests() {
    echo -e "${BLUE}运行所有 InnerApi Mock 测试...${NC}"
    echo ""
    
    cd "$PROJECT_ROOT"
    
    # 运行 InnerApi Mock 相关测试
    if $PHPUNIT_PATH --configuration "$PHPUNIT_CONFIG" \
        --filter "Mock" \
        protected/tests/unit/ai_sdr/; then
        echo -e "${GREEN}✓ 所有测试通过${NC}"
    else
        echo -e "${RED}✗ 部分测试失败${NC}"
        return 1
    fi
}

# 运行测试并生成覆盖率报告
run_with_coverage() {
    echo -e "${BLUE}运行测试并生成覆盖率报告...${NC}"
    echo ""
    
    cd "$PROJECT_ROOT"
    
    local coverage_dir="protected/tests/coverage/inner_api_mock"
    mkdir -p "$coverage_dir"
    
    if $PHPUNIT_PATH --configuration "$PHPUNIT_CONFIG" \
        --coverage-html "$coverage_dir" \
        --coverage-text \
        --filter "AiBackgroundCheckService.*Mock" \
        protected/tests/unit/ai_sdr/; then
        echo -e "${GREEN}✓ 测试完成，覆盖率报告生成在: $coverage_dir${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
        return 1
    fi
}

# 验证 Mock 框架功能
verify_mock_framework() {
    echo -e "${BLUE}验证 Mock 框架功能...${NC}"
    echo ""
    
    # 创建临时测试文件
    local temp_test="/tmp/inner_api_mock_verify.php"
    
    cat > "$temp_test" << 'EOF'
<?php
require_once '/Users/<USER>/dev/xiaoman/crm/protected/tests/bootstrap.php';

use tests\mockery\inner_api\InnerApiFactoryMockery;
use tests\mockery\inner_api\MockInnerApi;

echo "验证 InnerApiFactoryMockery...\n";

// 初始化
InnerApiFactoryMockery::initialize();

// 创建 Mock 实例
$mockApi = InnerApiFactoryMockery::createMockInnerApi('test_module');
echo "✓ Mock 实例创建成功\n";

// 设置响应
InnerApiFactoryMockery::setApiResponse('test_module', 'test_interface', [
    'code' => 0,
    'data' => ['test' => 'success']
]);
echo "✓ 响应设置成功\n";

// 模拟调用
try {
    $result = $mockApi->call('test_interface', ['param' => 'value']);
    if ($result['data']['test'] === 'success') {
        echo "✓ Mock 调用成功\n";
    } else {
        echo "✗ Mock 调用结果不正确\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "✗ Mock 调用失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 验证调用记录
if (InnerApiFactoryMockery::wasApiCalled('test_module', 'test_interface')) {
    echo "✓ 调用记录验证成功\n";
} else {
    echo "✗ 调用记录验证失败\n";
    exit(1);
}

// 清理
InnerApiFactoryMockery::cleanup();
echo "✓ 清理完成\n";

echo "\n所有验证通过！\n";
EOF

    if /opt/homebrew/bin/php "$temp_test"; then
        echo -e "${GREEN}✓ Mock 框架验证通过${NC}"
        rm -f "$temp_test"
    else
        echo -e "${RED}✗ Mock 框架验证失败${NC}"
        rm -f "$temp_test"
        return 1
    fi
    echo ""
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  check           检查测试文件是否存在"
    echo "  verify          验证 Mock 框架功能"
    echo "  simple          运行简单 Mock 测试"
    echo "  working         运行工作测试（推荐）"
    echo "  helper          运行 Helper 测试（推荐）"
    echo "  basic           运行基础测试"
    echo "  all             运行所有 InnerApi Mock 测试"
    echo "  coverage        运行测试并生成覆盖率报告"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 check        # 检查文件"
    echo "  $0 working      # 运行工作测试（推荐）"
    echo "  $0 helper       # 运行 Helper 测试（推荐）"
    echo "  $0 all          # 运行所有测试"
    echo "  $0 coverage     # 生成覆盖率报告"
}

# 主函数
main() {
    case "${1:-all}" in
        "check")
            check_files
            ;;
        "verify")
            check_files
            verify_mock_framework
            ;;
        "simple")
            check_files
            run_test "protected/tests/unit/ai_sdr/AiBackgroundCheckServiceSimpleMockTest.php" "AiBackgroundCheckService 简单 Mock"
            ;;
        "working")
            check_files
            run_test "protected/tests/unit/ai_sdr/AiBackgroundCheckServiceWorkingTest.php" "AiBackgroundCheckService 工作测试"
            ;;
        "helper")
            check_files
            run_test "protected/tests/unit/ai_sdr/AiBackgroundCheckServiceHelperTest.php" "AiBackgroundCheckService Helper 测试"
            ;;
        "basic")
            check_files
            run_test "protected/tests/unit/ai_sdr/AiBackgroundCheckServiceBasicTest.php" "AiBackgroundCheckService 基础测试"
            ;;
        "all")
            check_files
            run_all_tests
            ;;
        "coverage")
            check_files
            run_with_coverage
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
