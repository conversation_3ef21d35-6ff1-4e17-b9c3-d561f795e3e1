# InnerApi Mockery 框架使用指南

## 概述

InnerApi Mockery 框架为测试依赖 `InnerApi` 的服务类提供了完整的 Mock 解决方案。通过这个框架，您可以轻松地模拟各种 API 响应场景，验证 API 调用行为，并确保代码的健壮性。

## 核心组件

### 1. InnerApiMockHelper（推荐使用）
最简单易用的 Mock 工具类，支持按 module 自定义 call 方法返回结果。

### 2. InnerApiFactoryMockery
完整的 Mock 工厂类，提供高级功能和场景管理。

### 3. MockInnerApiTrait
便捷的测试辅助 Trait，提供各种断言和场景设置方法。

## 快速开始（推荐方式）

### 使用 InnerApiMockHelper

```php
<?php
use PHPUnit\Framework\TestCase;
use tests\mockery\inner_api\InnerApiMockHelper;
use common\library\api\InnerApi;

class YourServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        InnerApiMockHelper::reset();
    }

    protected function tearDown(): void
    {
        InnerApiMockHelper::clearAll();
        \Mockery::close();
        parent::tearDown();
    }

    public function testYourService()
    {
        // 设置 API 响应
        InnerApiMockHelper::setMockResponse('your_module', 'your_interface', [
            'result' => 'success'
        ]);

        // 创建可测试的服务
        $service = $this->createTestService();
        $result = $service->callApi();

        // 验证结果和调用
        $this->assertEquals('success', $result['result']);
        InnerApiMockHelper::assertApiWasCalled('your_module', 'your_interface');
    }

    private function createTestService()
    {
        return new class() extends YourService {
            protected function innerApiFactory(string $module): InnerApi
            {
                return InnerApiMockHelper::createMockInnerApi($module);
            }
        };
    }
}
```

### AiBackgroundCheckService 完整示例

```php
<?php
use PHPUnit\Framework\TestCase;
use tests\mockery\inner_api\InnerApiMockHelper;
use common\library\ai_sdr\AiBackgroundCheckService;

class AiBackgroundCheckServiceTest extends TestCase
{
    private $clientId = 12345;
    private $userId = 67890;

    protected function setUp(): void
    {
        parent::setUp();
        InnerApiMockHelper::reset();
    }

    protected function tearDown(): void
    {
        InnerApiMockHelper::clearAll();
        \Mockery::close();
        parent::tearDown();
    }

    public function testStandardizeContentSuccess()
    {
        // 使用便捷方法设置成功响应
        InnerApiMockHelper::setStandardizeContentSuccess(123, 'Technology', true, 0.95);

        $service = $this->createTestService();
        $result = $service->getStandardResult('tech', 1, 0.8);

        // 验证结果
        $this->assertEquals(123, $result['std_id']);
        $this->assertEquals('Technology', $result['std_name']);

        // 验证调用
        InnerApiMockHelper::assertApiWasCalled('ai_background_check', 'standardizeContent');
    }

    private function createTestService(): AiBackgroundCheckService
    {
        return new class($this->clientId, $this->userId) extends AiBackgroundCheckService {
            protected function innerApiFactory(string $module, string $method = \common\library\api\InnerApi::HTTP_METHOD_POST_JSON): \common\library\api\InnerApi
            {
                return InnerApiMockHelper::createMockInnerApi($module);
            }

            protected function innerCall(\common\library\api\InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                try {
                    return $api->call($interface, $params);
                } catch (\Exception $exception) {
                    throw new \Exception(\Yii::t("common", "Failed to get data, please try again later"), $exception->getCode());
                }
            }
        };
    }
}
```

### 2. 使用简单 Mockery 方式

```php
<?php
use PHPUnit\Framework\TestCase;
use Mockery;
use common\library\api\InnerApi;

class YourServiceSimpleTest extends TestCase
{
    private $mockInnerApi;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->mockInnerApi = Mockery::mock(InnerApi::class);
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    public function testApiCall()
    {
        // 设置 Mock 期望
        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('interface_name', ['param' => 'value'])
            ->andReturn(['result' => 'success']);
            
        // 创建使用 Mock 的服务实例
        $service = new YourTestableService($this->mockInnerApi);
        $result = $service->callApi();
        
        $this->assertEquals('success', $result['result']);
    }
}
```

## 核心功能

### 1. 响应数据设置

#### 设置成功响应
```php
$this->setApiResponse('module_name', 'interface_name', [
    'code' => 0,
    'message' => 'success',
    'data' => [
        'id' => 123,
        'name' => 'Test Item'
    ]
]);
```

#### 设置错误响应
```php
$this->setApiResponse('module_name', 'interface_name', [
    'code' => -1,
    'message' => 'Service unavailable',
    'data' => null
]);
```

### 2. 异常模拟

#### 设置简单异常
```php
$this->setApiException('module_name', 'interface_name', 'Service error');
```

#### 设置复杂异常
```php
$this->setApiException('module_name', 'interface_name', [
    'class' => \RuntimeException::class,
    'message' => 'Custom error message',
    'code' => 500
]);
```

### 3. 预设测试场景

#### AI 背景调研场景
```php
// 成功场景
$this->setAiBackgroundCheckSuccessScenario([
    'report_id' => 'bg_001',
    'company_name' => 'Test Company',
    'has_company' => 1
]);

// 错误场景
$this->setAiBackgroundCheckErrorScenario([
    'code' => -1,
    'message' => 'Service unavailable'
]);

// 超时场景
$this->setAiBackgroundCheckTimeoutScenario();
```

#### 标准化内容场景
```php
// 成功匹配
$this->setStandardizeContentSuccessScenario([
    'std_id' => 123,
    'std_name' => 'Technology',
    'confidence' => 0.95
]);

// 无匹配
$this->setStandardizeContentNoMatchScenario([
    'confidence' => 0.2
]);
```

### 4. 快捷场景设置

```php
// 行业标准化成功
$this->setIndustryStandardizationSuccess('Technology', 123);

// 产品标准化成功
$this->setProductStandardizationSuccess('Software', 456);

// 低置信度场景
$this->setStandardizationLowConfidence('unknown_content');

// 服务不可用
$this->setApiServiceUnavailable();

// 网络超时
$this->setNetworkTimeout();
```

## 验证和断言

### 1. 基本验证

```php
// 验证 API 被调用
$this->assertApiWasCalled('module_name', 'interface_name');

// 验证 API 没有被调用
$this->assertApiWasNotCalled('module_name', 'interface_name');

// 验证调用次数
$this->assertApiCallCount('module_name', 'interface_name', 2);
```

### 2. 参数验证

```php
// 验证调用参数
$this->assertApiCalledWith('module_name', 'interface_name', [
    'param1' => 'expected_value1',
    'param2' => 'expected_value2'
]);
```

### 3. 专用验证方法

```php
// AI 背景调研专用
$this->assertAiBackgroundCheckWasCalled();
$this->assertAiBackgroundCheckCalledWith([
    'client_id' => 12345,
    'user_id' => 67890
]);

// 标准化内容专用
$this->assertStandardizeContentWasCalled();
$this->assertStandardizeContentCalledWith([
    'content' => 'technology',
    'std_type' => 'industry'
]);
```

### 4. 批量验证

```php
// 验证所有预期的 API 都被调用
$this->assertAllExpectedApisCalled([
    'module1::interface1',
    'module2::interface2'
]);

// 验证只有预期的 API 被调用
$this->assertOnlyExpectedApisCalled([
    'module1::interface1'
]);
```

## 调试和监控

### 1. 调用日志

```php
// 获取调用日志
$callLog = $this->getApiCallLog();
foreach ($callLog as $call) {
    echo "Module: {$call['module']}\n";
    echo "Interface: {$call['interface']}\n";
    echo "Params: " . json_encode($call['params']) . "\n";
    echo "Timestamp: {$call['timestamp']}\n\n";
}

// 清空调用日志
$this->clearApiCallLog();
```

### 2. 统计信息

```php
// 获取调用统计
$statistics = $this->getApiCallStatistics();
// 输出: ['module1::interface1' => 3, 'module2::interface2' => 1]

// 打印统计信息（用于调试）
$this->printApiCallStatistics();
```

## 高级用法

### 1. 自定义测试场景

```php
// 创建自定义场景
$this->setInnerApiTestScenario('custom_scenario', [
    'param1' => 'value1',
    'param2' => 'value2'
]);

// 在 InnerApiFactoryMockery 中实现场景逻辑
private static function setCustomScenario(array $config): void
{
    // 自定义场景实现
}
```

### 2. 条件响应

```php
// 根据参数返回不同响应
$this->mockInnerApi
    ->shouldReceive('call')
    ->with('interface', Mockery::subset(['type' => 'industry']))
    ->andReturn(['std_type' => 'industry']);
    
$this->mockInnerApi
    ->shouldReceive('call')
    ->with('interface', Mockery::subset(['type' => 'product']))
    ->andReturn(['std_type' => 'product']);
```

### 3. 动态响应

```php
$this->mockInnerApi
    ->shouldReceive('call')
    ->andReturnUsing(function ($interface, $params) {
        if ($params['content'] === 'technology') {
            return ['std_id' => 123, 'std_name' => 'Technology'];
        }
        return ['std_id' => 0, 'std_name' => ''];
    });
```

## 最佳实践

### 1. 测试组织

```php
class AiBackgroundCheckServiceTest extends TestCase
{
    use MockInnerApiTrait;
    
    // 将相关测试分组
    
    /** @group success_scenarios */
    public function testSuccessScenarios() { /* ... */ }
    
    /** @group error_scenarios */
    public function testErrorScenarios() { /* ... */ }
    
    /** @group edge_cases */
    public function testEdgeCases() { /* ... */ }
}
```

### 2. 数据提供者

```php
/**
 * @dataProvider contentTypeProvider
 */
public function testContentTypeMapping($inputType, $expectedType)
{
    $this->mockInnerApi
        ->shouldReceive('call')
        ->with('standardizeContent', Mockery::subset([
            'std_type' => $expectedType
        ]))
        ->andReturn(['result' => 'success']);
        
    $this->service->getStandardResult('test', $inputType);
}

public function contentTypeProvider()
{
    return [
        'industry' => [1, 'industry'],
        'product' => [2, 'product']
    ];
}
```

### 3. 测试隔离

```php
protected function setUp(): void
{
    parent::setUp();
    self::setUpInnerApiMockery();
    $this->resetInnerApiMockery(); // 确保每个测试都是干净的状态
}
```

## 故障排除

### 1. 常见问题

#### Mock 没有生效
```php
// 确保正确设置了 Mock
self::setUpInnerApiMockery();

// 检查是否正确重写了 innerApiFactory 方法
protected function innerApiFactory(string $module): InnerApi
{
    return InnerApiFactoryMockery::createMockInnerApi($module);
}
```

#### 参数验证失败
```php
// 使用 Mockery::subset() 进行部分匹配
$this->mockInnerApi
    ->shouldReceive('call')
    ->with('interface', Mockery::subset(['key' => 'value']))
    ->andReturn($response);
```

#### 异常没有被正确抛出
```php
// 确保异常类型和消息正确
$this->setApiException('module', 'interface', [
    'class' => \RuntimeException::class,  // 确保类存在
    'message' => 'Expected message',
    'code' => 500
]);
```

### 2. 调试技巧

```php
// 打印调用日志进行调试
public function testDebugApiCalls()
{
    // ... 执行测试代码 ...
    
    $this->printApiCallStatistics();
    
    $callLog = $this->getApiCallLog();
    var_dump($callLog); // 查看详细调用信息
}
```

## 扩展框架

### 1. 添加新的预设场景

在 `InnerApiFactoryMockery` 中添加新场景：

```php
public static function setTestScenario(string $scenarioName, array $config = []): void
{
    switch ($scenarioName) {
        case 'your_new_scenario':
            self::setYourNewScenario($config);
            break;
        // ... 其他场景
    }
}

private static function setYourNewScenario(array $config): void
{
    // 实现新场景逻辑
}
```

### 2. 添加新的便捷方法

在 `MockInnerApiTrait` 中添加：

```php
protected function setYourServiceSuccessScenario(array $config = []): void
{
    $this->setInnerApiTestScenario('your_service_success', $config);
}

protected function assertYourServiceWasCalled(string $message = ''): void
{
    $this->assertApiWasCalled('your_module', 'your_interface', $message);
}
```

这样就可以为其他服务提供类似的 Mock 支持。
