<?php

namespace tests\mockery\inner_api\data;

/**
 * InnerApi 响应数据工厂
 * 
 * 为各种 InnerApi 模块提供标准化的测试响应数据
 */
class InnerApiResponseDataFactory
{
    /**
     * @var array 当前场景配置
     */
    private $currentScenario = [];

    /**
     * @var array 自定义响应数据
     */
    private $customResponses = [];

    /**
     * 设置测试场景
     */
    public function setScenario(string $scenarioName, array $config = []): void
    {
        $this->currentScenario = [
            'name' => $scenarioName,
            'config' => $config
        ];
    }

    /**
     * 设置自定义响应
     */
    public function setCustomResponse(string $module, string $interface, $responseData): void
    {
        $key = "{$module}::{$interface}";
        $this->customResponses[$key] = $responseData;
    }

    /**
     * 获取 AI 背景调研标准化内容响应数据
     */
    public function getAiBackgroundCheckStandardizeResponse(array $input = []): array
    {
        $module = 'ai_background_check';
        $interface = 'standardizeContent';
        $key = "{$module}::{$interface}";

        if (isset($this->customResponses[$key])) {
            return $this->customResponses[$key];
        }

        // 根据场景返回不同的响应
        return $this->getStandardizeResponseByScenario($input);
    }

    /**
     * 获取 AI 背景调研响应数据
     */
    public function getAiBackgroundCheckResponse(array $input = []): array
    {
        $module = 'ai_background_check';
        $interface = 'aiBackgroundCheck';
        $key = "{$module}::{$interface}";

        if (isset($this->customResponses[$key])) {
            return $this->customResponses[$key];
        }

        return [
            'report_id' => $this->generateReportId(),
            'status' => 'completed',
            'report' => $this->generateBackgroundReport($input),
            'time_used' => mt_rand(20, 60)
        ];
    }

    /**
     * 获取 OKKI Leads Service 响应数据
     */
    public function getOkkiLeadsServiceResponse(string $interface, array $input = []): array
    {
        $module = 'okki_leads_service';
        $key = "{$module}::{$interface}";

        if (isset($this->customResponses[$key])) {
            return $this->customResponses[$key];
        }

        switch ($interface) {
            case 'taskResult':
                return $this->getTaskResultResponse($input);
            case 'backgroundCheckTaskCallback':
                return $this->getBackgroundCheckCallbackResponse($input);
            case 'checkCgsTmExternal':
                return $this->getCheckCgsTmResponse($input);
            default:
                return $this->getDefaultSuccessResponse();
        }
    }

    /**
     * 获取任务结果响应
     */
    private function getTaskResultResponse(array $input = []): array
    {
        $scenarioName = $this->currentScenario['name'] ?? '';
        
        switch ($scenarioName) {
            case 'task_completed':
                return [
                    'code' => 0,
                    'message' => 'success',
                    'data' => [
                        'task_id' => $input['task_id'] ?? 'task_001',
                        'status' => 'completed',
                        'result' => $this->generateTaskResult($input),
                        'create_time' => time() - 3600,
                        'complete_time' => time()
                    ]
                ];
            case 'task_processing':
                return [
                    'code' => 0,
                    'message' => 'success',
                    'data' => [
                        'task_id' => $input['task_id'] ?? 'task_001',
                        'status' => 'processing',
                        'progress' => mt_rand(10, 90),
                        'create_time' => time() - 1800
                    ]
                ];
            case 'task_failed':
                return [
                    'code' => -1,
                    'message' => 'Task execution failed',
                    'data' => [
                        'task_id' => $input['task_id'] ?? 'task_001',
                        'status' => 'failed',
                        'error' => 'Processing timeout',
                        'create_time' => time() - 3600
                    ]
                ];
            default:
                return $this->getTaskResultResponse(['scenario' => 'task_completed'] + $input);
        }
    }

    /**
     * 获取背景调研回调响应
     */
    private function getBackgroundCheckCallbackResponse(array $input = []): array
    {
        return [
            'code' => 0,
            'message' => 'Callback received successfully',
            'data' => [
                'callback_id' => $this->generateCallbackId(),
                'processed_at' => time()
            ]
        ];
    }

    /**
     * 获取 CGS TM 检查响应
     */
    private function getCheckCgsTmResponse(array $input = []): array
    {
        $scenarioName = $this->currentScenario['name'] ?? '';
        
        $hasAuth = $scenarioName === 'cgs_tm_authorized' || 
                   ($this->currentScenario['config']['has_auth'] ?? true);

        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'code' => 0,
                'data' => $hasAuth,
                'message' => $hasAuth ? 'Authorized' : 'Not authorized'
            ]
        ];
    }

    /**
     * 根据场景获取标准化响应
     */
    private function getStandardizeResponseByScenario(array $input = []): array
    {
        $scenarioName = $this->currentScenario['name'] ?? '';
        $config = $this->currentScenario['config'] ?? [];

        switch ($scenarioName) {
            case 'standardize_success':
                return [
                    'std_id' => $config['std_id'] ?? 123,
                    'std_name' => $config['std_name'] ?? 'Technology',
                    'is_adopted' => $config['is_adopted'] ?? true,
                    'confidence' => $config['confidence'] ?? 0.95,
                    'original_content' => $input['content'] ?? 'tech',
                    'standardized_content' => $config['std_name'] ?? 'Technology'
                ];
            case 'standardize_no_match':
                return [
                    'std_id' => 0,
                    'std_name' => '',
                    'is_adopted' => false,
                    'confidence' => $config['confidence'] ?? 0.2,
                    'original_content' => $input['content'] ?? 'unknown',
                    'standardized_content' => ''
                ];
            case 'standardize_low_confidence':
                return [
                    'std_id' => $config['std_id'] ?? 456,
                    'std_name' => $config['std_name'] ?? 'Uncertain Category',
                    'is_adopted' => false,
                    'confidence' => $config['confidence'] ?? 0.4,
                    'original_content' => $input['content'] ?? 'uncertain',
                    'standardized_content' => $config['std_name'] ?? 'Uncertain Category'
                ];
            default:
                return $this->getStandardizeResponseByScenario(['scenario' => 'standardize_success'] + $input);
        }
    }

    /**
     * 生成背景调研报告
     */
    private function generateBackgroundReport(array $input = []): array
    {
        $scenarioName = $this->currentScenario['name'] ?? '';
        $config = $this->currentScenario['config'] ?? [];

        $baseReport = [
            'company_name' => $config['company_name'] ?? 'Mock Technology Co., Ltd.',
            'homepage' => $config['homepage'] ?? 'https://mock-tech.com',
            'has_company' => $config['has_company'] ?? 1,
            'has_contacts' => $config['has_contacts'] ?? 1,
            'employees_min' => $config['employees_min'] ?? 100,
            'employees_max' => $config['employees_max'] ?? 500,
            'industry_ids' => $config['industry_ids'] ?? [1, 2, 3],
            'main_products' => $config['main_products'] ?? 'Software, Hardware, Cloud Services',
            'company_description' => $config['company_description'] ?? 'Leading technology company',
            'country' => $config['country'] ?? 'CN',
            'established_year' => $config['established_year'] ?? '2010'
        ];

        // 根据场景调整报告内容
        switch ($scenarioName) {
            case 'large_company':
                $baseReport['employees_min'] = 1000;
                $baseReport['employees_max'] = 5000;
                $baseReport['company_description'] = 'Large multinational corporation';
                break;
            case 'small_company':
                $baseReport['employees_min'] = 10;
                $baseReport['employees_max'] = 50;
                $baseReport['company_description'] = 'Small innovative startup';
                break;
            case 'no_company_info':
                $baseReport['has_company'] = 0;
                $baseReport['company_name'] = '';
                $baseReport['company_description'] = '';
                break;
        }

        return $baseReport;
    }

    /**
     * 生成任务结果
     */
    private function generateTaskResult(array $input = []): array
    {
        return [
            'leads_count' => mt_rand(10, 100),
            'processed_domains' => mt_rand(5, 50),
            'success_rate' => round(mt_rand(70, 95) / 100, 2),
            'quality_distribution' => [
                'high' => mt_rand(10, 30),
                'medium' => mt_rand(20, 40),
                'low' => mt_rand(5, 20)
            ]
        ];
    }

    /**
     * 获取默认成功响应
     */
    private function getDefaultSuccessResponse(): array
    {
        return [
            'code' => 0,
            'message' => 'success',
            'data' => []
        ];
    }

    /**
     * 生成报告ID
     */
    private function generateReportId(): string
    {
        return 'bg_' . date('Ymd') . '_' . mt_rand(1000, 9999);
    }

    /**
     * 生成回调ID
     */
    private function generateCallbackId(): string
    {
        return 'cb_' . time() . '_' . mt_rand(100, 999);
    }

    /**
     * 重置状态
     */
    public function reset(): void
    {
        $this->currentScenario = [];
        $this->customResponses = [];
    }
}
