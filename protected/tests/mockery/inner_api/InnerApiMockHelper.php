<?php

namespace tests\mockery\inner_api;

use Mockery;
use common\library\api\InnerApi;

/**
 * InnerApi Mock 辅助工具类
 * 
 * 提供简单易用的 InnerApi Mock 功能，支持按 module 自定义 call 方法返回结果
 */
class InnerApiMockHelper
{
    /**
     * @var array Mock 响应数据存储
     */
    private static $mockResponses = [];

    /**
     * @var array API 调用日志
     */
    private static $callLog = [];

    /**
     * 设置 Mock 响应数据
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @param mixed $response 响应数据或异常对象
     */
    public static function setMockResponse(string $module, string $interface, $response): void
    {
        $key = self::getResponseKey($module, $interface);
        self::$mockResponses[$key] = $response;
    }

    /**
     * 创建 Mock InnerApi 实例
     * 
     * @param string $module 模块名
     * @return \Mockery\MockInterface
     */
    public static function createMockInnerApi(string $module): \Mockery\MockInterface
    {
        $mockApi = Mockery::mock(InnerApi::class);
        
        // 设置基本方法
        $mockApi->shouldReceive('addHeader')->andReturnSelf();
        $mockApi->shouldReceive('setHttpMethod')->andReturnSelf();
        $mockApi->shouldReceive('setTimeout')->andReturnSelf();
        $mockApi->shouldReceive('setCheckRspJsonCode')->andReturnSelf();
        $mockApi->shouldReceive('setApiErrorHandler')->andReturnSelf();
        $mockApi->shouldReceive('setTimeoutHandler')->andReturnSelf();
        $mockApi->shouldReceive('setServerErrorHandler')->andReturnSelf();
        $mockApi->shouldReceive('getModule')->andReturn($module);
        $mockApi->shouldReceive('getInterface')->andReturn('');
        $mockApi->shouldReceive('getDomain')->andReturn('mock.domain.com');
        
        // 设置 call 方法，根据 module 和 interface 返回预设响应
        $mockApi->shouldReceive('call')->andReturnUsing(
            function ($interface, $params) use ($module) {
                return self::handleApiCall($module, $interface, $params);
            }
        );
        
        return $mockApi;
    }

    /**
     * 处理 API 调用
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @param mixed $params 调用参数
     * @return mixed 响应数据
     * @throws \Exception 如果设置了异常响应
     */
    private static function handleApiCall(string $module, string $interface, $params)
    {
        // 记录调用日志
        self::$callLog[] = [
            'module' => $module,
            'interface' => $interface,
            'params' => $params,
            'timestamp' => microtime(true)
        ];

        $key = self::getResponseKey($module, $interface);

        if (isset(self::$mockResponses[$key])) {
            $response = self::$mockResponses[$key];
            
            // 如果是异常，则抛出
            if ($response instanceof \Exception) {
                throw $response;
            }
            
            return $response;
        }
        
        // 默认返回空结果
        return [];
    }

    /**
     * 获取响应键
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @return string 响应键
     */
    private static function getResponseKey(string $module, string $interface): string
    {
        return "{$module}::{$interface}";
    }

    /**
     * 检查 API 是否被调用
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @return bool 是否被调用
     */
    public static function wasApiCalled(string $module, string $interface): bool
    {
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取 API 调用次数
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @return int 调用次数
     */
    public static function getApiCallCount(string $module, string $interface): int
    {
        $count = 0;
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                $count++;
            }
        }
        return $count;
    }

    /**
     * 获取最后一次 API 调用参数
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @return array|null 调用参数
     */
    public static function getLastApiCallParams(string $module, string $interface): ?array
    {
        $lastCall = null;
        foreach (self::$callLog as $call) {
            if ($call['module'] === $module && $call['interface'] === $interface) {
                $lastCall = $call;
            }
        }
        return $lastCall ? $lastCall['params'] : null;
    }

    /**
     * 获取所有调用日志
     * 
     * @return array 调用日志
     */
    public static function getCallLog(): array
    {
        return self::$callLog;
    }

    /**
     * 获取调用统计
     * 
     * @return array 统计信息
     */
    public static function getCallStatistics(): array
    {
        $statistics = [];
        foreach (self::$callLog as $call) {
            $key = $call['module'] . '::' . $call['interface'];
            if (!isset($statistics[$key])) {
                $statistics[$key] = 0;
            }
            $statistics[$key]++;
        }
        return $statistics;
    }

    /**
     * 清空调用日志
     */
    public static function clearCallLog(): void
    {
        self::$callLog = [];
    }

    /**
     * 清空所有 Mock 数据
     */
    public static function clearAll(): void
    {
        self::$mockResponses = [];
        self::$callLog = [];
    }

    /**
     * 重置状态
     */
    public static function reset(): void
    {
        self::clearAll();
    }

    // === 便捷方法 ===

    /**
     * 设置成功响应
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @param array $data 响应数据
     */
    public static function setSuccessResponse(string $module, string $interface, array $data = []): void
    {
        self::setMockResponse($module, $interface, $data);
    }

    /**
     * 设置错误响应
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @param string $message 错误消息
     * @param int $code 错误代码
     */
    public static function setErrorResponse(string $module, string $interface, string $message = 'API Error', int $code = -1): void
    {
        self::setMockResponse($module, $interface, new \Exception($message, $code));
    }

    /**
     * 设置超时响应
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     */
    public static function setTimeoutResponse(string $module, string $interface): void
    {
        self::setErrorResponse($module, $interface, 'Request timeout', 28);
    }

    /**
     * 设置服务不可用响应
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     */
    public static function setServiceUnavailableResponse(string $module, string $interface): void
    {
        self::setErrorResponse($module, $interface, 'Service temporarily unavailable', 503);
    }

    // === AI Background Check 专用方法 ===

    /**
     * 设置标准化内容成功响应
     * 
     * @param int $stdId 标准化ID
     * @param string $stdName 标准化名称
     * @param bool $isAdopted 是否采用
     * @param float $confidence 置信度
     */
    public static function setStandardizeContentSuccess(
        int $stdId = 123,
        string $stdName = 'Technology',
        bool $isAdopted = true,
        float $confidence = 0.95
    ): void {
        self::setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => $stdId,
            'std_name' => $stdName,
            'is_adopted' => $isAdopted,
            'confidence' => $confidence
        ]);
    }

    /**
     * 设置标准化内容无匹配响应
     * 
     * @param float $confidence 置信度
     */
    public static function setStandardizeContentNoMatch(float $confidence = 0.2): void
    {
        self::setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 0,
            'std_name' => '',
            'is_adopted' => false,
            'confidence' => $confidence
        ]);
    }

    /**
     * 设置 AI 背景调研成功响应
     * 
     * @param string $reportId 报告ID
     * @param array $reportData 报告数据
     */
    public static function setAiBackgroundCheckSuccess(string $reportId = 'bg_001', array $reportData = []): void
    {
        $defaultReport = [
            'company_name' => 'Test Company Ltd',
            'homepage' => 'https://test-company.com',
            'has_company' => 1,
            'has_contacts' => 1,
            'employees_min' => 100,
            'employees_max' => 500,
            'industry_ids' => [1, 2],
            'main_products' => 'Software, Cloud Services',
            'company_description' => 'Leading technology company'
        ];

        self::setMockResponse('ai_background_check', 'aiBackgroundCheck', [
            'report_id' => $reportId,
            'status' => 'completed',
            'report' => array_merge($defaultReport, $reportData),
            'time_used' => 30
        ]);
    }

    // === 断言方法 ===

    /**
     * 断言 API 被调用
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @param string $message 断言消息
     */
    public static function assertApiWasCalled(string $module, string $interface, string $message = ''): void
    {
        $wasCalled = self::wasApiCalled($module, $interface);
        if (!$wasCalled) {
            throw new \PHPUnit\Framework\AssertionFailedError(
                $message ?: "API {$module}::{$interface} was not called"
            );
        }
    }

    /**
     * 断言 API 调用次数
     * 
     * @param string $module 模块名
     * @param string $interface 接口名
     * @param int $expectedCount 期望次数
     * @param string $message 断言消息
     */
    public static function assertApiCallCount(string $module, string $interface, int $expectedCount, string $message = ''): void
    {
        $actualCount = self::getApiCallCount($module, $interface);
        if ($actualCount !== $expectedCount) {
            throw new \PHPUnit\Framework\AssertionFailedError(
                $message ?: "Expected {$expectedCount} calls to {$module}::{$interface}, but got {$actualCount}"
            );
        }
    }
}
