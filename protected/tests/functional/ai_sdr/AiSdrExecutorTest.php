<?php

namespace tests\functional\ai_sdr;

use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\queue_v2\job\AiSdrDigTaskJob;
use tests\functional\ai_sdr\mockery\traits\MockeryAgentTrait;

class AiSdrExecutorTest extends AiSdrTaskTest
{

    use MockeryAgentTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass(); // TODO: Change the autogenerated stub
        static::setUpAiAgentMockery();
    }

    public static function tearDownAfterClass(): void
    {
        static::tearDownAiAgentMockery();
        parent::tearDownAfterClass(); // TODO: Change the autogenerated stub
    }

    public function testDigDraft()
    {
//        $sdrTask = AiSdrTestDataFactory::initTestTask(Constant::TASK_SOURCE_AI_SDR);
        // 通过图谱挖掘潜客，草稿任务会执行打标
        $job = new AiSdrDigTaskJob(static::$clientId, static::$sdrTask->task_id);
        $job->handle();
    }

    private function unlockDig()
    {
        $redis = \RedisService::cache();
        $key = sprintf(Constant::REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY, static::$clientId, static::$sdrTask->task_id);
        $redis->del([$key]);
    }

    public function testDig()
    {

        $this->setQualityAnalysisScenario('medium_quality');

        $this->unlockDig();
        $task = new AiSdrTask(static::$clientId, static::$sdrTask->task_id);
        $service = new AISdrService(static::$clientId, static::$userId);
        $service->processDig($task);

        $this->assertAllExpectedAgentsCalled([
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS
        ]);
    }
}