<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * Author: <PERSON>(h<PERSON>)
 * Date: 2025/3/25
 * Time: 18:20
 */

namespace tests\functional\ai_sdr;

use common\library\account\Client;
use common\library\ai\service\RecommendService;
use common\library\ai_agent\HomepageAiAgent;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\Helper;
use common\library\ai_sdr\jobs\AiSdrLiveSellerProfileJob;
use common\library\ai_sdr\jobs\DeliveryFrontedStageDetailsJob;
use common\library\ai_sdr\MessageService;
use common\library\ai_sdr\profile\ClientProfile;
use common\library\ai_sdr\profile_task\SellerProfileTask;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_record\AiSdrTaskRecordFilter;
use common\library\api\InnerApi;
use common\library\crawler\Crawler;
use common\library\queue_v2\job\AiSdrProductUsageJob;
use common\library\queue_v2\QueueService;
use common\library\statistics\data_adapter\Sql;
use common\models\client\ClientProduct;
use PHPUnit\Framework\TestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use UserMail;
use xiaoman\orm\database\data\JsonbExists;
use xiaoman\orm\database\data\JsonbExistsAll;
use xiaoman\orm\database\data\Range;

class AiSdrProfileGenerateTask extends \FunctionalTestCase
{
    protected array $seedClientsInfo = [
        2639 => ['company_name' => '深圳市格盛力能源有限公司', 'homepage' => 'http://www.gsl-energy.com/', 'product_page' => ''],
        2642 => ['company_name' => '深圳市优可新科技有限公司', 'homepage' => 'yocantech.com', 'product_page' => ''],
        2697 => ['company_name' => '深圳市宏铭达物流有限公司', 'homepage' => 'www.swwlogistics.com', 'product_page' => ''],
        13232 => ['company_name' => '深圳市普菲特精密制品有限公司', 'homepage' => 'www.7-swords.com', 'product_page' => ''],
        23933 => ['company_name' => '深圳沃新智创科技有限公司', 'homepage' => 'smartnewo.com', 'product_page' => ''],
        23936 => ['company_name' => '佛山市中牌机械有限公司', 'homepage' => 'www.joparr.com', 'product_page' => ''],
        27499 => ['company_name' => '肇庆市和佳电子有限公司', 'homepage' => 'fifine.cc', 'product_page' => ''],
        29036 => ['company_name' => '深圳市金仕达箱包有限公司', 'homepage' => 'kingstarbags.com', 'product_page' => ''],
        31704 => ['company_name' => '深圳市科颂科技有限公司', 'homepage' => 'www.coxotech.com', 'product_page' => 'https://www.1688.com/factory/coxotech.html'],
        31793 => ['company_name' => '浙江同富特美刻股份有限公司', 'homepage' => 'everich.com', 'product_page' => ''],
        37170 => ['company_name' => '深圳市春旺新材料股份有限公司', 'homepage' => 'chun-wang.com', 'product_page' => ''],
        38069 => ['company_name' => '深圳玉汝成口腔材料有限公司', 'homepage' => 'yucera.com', 'product_page' => ''],
        64327 => ['company_name' => '深圳市乐福衡器有限公司', 'homepage' => 'lefu.cc', 'product_page' => 'https://www.1688.com/factory/b2b-285534677691af0.html'],
        78403 => ['company_name' => '瑞诺能源（深圳）有限公司', 'homepage' => 'renonpower.com', 'product_page' => ''],
        79739 => ['company_name' => '深圳蔚润科技有限公司', 'homepage' => 'wirentech.com', 'product_page' => ''],
        83761 => ['company_name' => '深圳市飞碟动力科技有限公司', 'homepage' => 'https://ufo-battery.com/#', 'product_page' => ''],
        340598 => ['company_name' => '广州医鹭久歌医疗器械有限公司', 'homepage' => 'https://www.medicalequipment-msl.com', 'product_page' => ''],
        360408 => ['company_name' => '深圳市广联智通科技有限公司', 'homepage' => 'https://www.gl-inet.com/', 'product_page' => ''],
    ];

    protected static $sdrTask;
    protected static $epTask;
    protected static $importTask;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass(); // TODO: Change the autogenerated stub

        self:self::loginClient(AiSdrTestDataFactory::TEST_CLIENT_ID);

        AiSdrTestDataFactory::initClient();

        self::$sdrTask = AiSdrTestDataFactory::initTestTask(Constant::TASK_SOURCE_AI_SDR);
        self::$importTask = AiSdrTestDataFactory::initTestTask(Constant::TASK_SOURCE_IMPORT);
        self::$epTask = AiSdrTestDataFactory::initTestTask(Constant::TASK_SOURCE_CRM_EP);
    }

    public function testInitAiSdrTaskInfo()
    {
        $sdrTask = (new AISdrService(static::$clientId, static::$userId))->getTaskList()[Constant::TASK_SOURCE_AI_SDR];
        $epTask = (new AISdrService(static::$clientId, static::$userId))->getTaskList()[Constant::TASK_SOURCE_CRM_EP];
        $importTask = (new AISdrService(static::$clientId, static::$userId))->getTaskList()[Constant::TASK_SOURCE_IMPORT];

        $this->assertEquals(Constant::TASK_SOURCE_AI_SDR, self::$sdrTask->source);
        $this->assertEquals(self::$sdrTask->task_id, $sdrTask['task_id']);

        $this->assertEquals(Constant::TASK_SOURCE_CRM_EP, self::$epTask->source);
        $this->assertEquals(self::$epTask->task_id, $epTask['task_id']);

        $this->assertEquals(Constant::TASK_SOURCE_IMPORT, self::$importTask->source);
        $this->assertEquals(self::$importTask->task_id, $importTask['task_id']);
    }

    public function testFindHomagePage()
    {
        $clientInfo = new Client(static::$clientId);
        $params = [
            'company_name' => $clientInfo['full_name'],
            'homepage' => RecommendService::getDomain($clientInfo['homepage']),
        ];

        $aiAgent = New HomepageAiAgent(self::$clientId);
        $resp= $aiAgent->process($params);
        $this->echo('resp: ' . $answer = $resp->answer . "\n" . 'except:' . $params['homepage']);
        $this->assertTrue(str_contains($answer, $params['homepage']));
    }

    /**
     * @return void
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     *
     */
    public function testClientProfileGenerate()
    {
        $clientInfo = new Client(static::$clientId);
        $companyName = $clientInfo->full_name ?? '';
        $homepage = Helper::getPossibleHomepage($clientInfo->homepage, $clientInfo->email);
        $task = SellerProfileTask::newTask($clientInfo->client_id, static::$userId, $companyName, $homepage, '', 'CN', static::$sdrTask->task_id);
        $task->generate(true);
    }

    public function testProductUsageRecordGen()
    {
        $job = new AiSdrProductUsageJob(static::$clientId, static::$sdrTask->task_id);
        $job->handle();
    }

    /** ---------------------------------------------------------------------------------------------
     *
    ------------------------------------------------------------------------------------------------ */



    public function testSellerProfile()
    {
        $taskId = Helper::uuid();
        $clientId = 111;
        $userId = 123;
        $companyName = '深圳市小满科技有限公司';
        $homepage = 'https://xiaoman.cn';
        $productPage = 'https://xiaoman.cn';
        $task = SellerProfileTask::newTask($taskId, $clientId, $userId, $companyName, $homepage, $productPage);
        $task->save();

        $task->generate();
    }

    public function testBuyerProfile()
    {
        \User::setLoginUserById(*********);
        $taskId = Helper::uuid();
        $clientId = 111;
        $userId = 123;
        $pages = [
            'https://xiaoman.cn',
            'https://www.xiaoman.cn/zh-cn/',
            'https://baike.baidu.com/item/深圳市小满科技有限公司/13834969'
        ];
        $task = BuyerProfileTask::newTask($taskId, $clientId, $userId, $pages);
        $task->save();
        $task->generate();
    }

    public function testCrawler()
    {
        $crawler = new Crawler();
        $data = $crawler->getHtmlAndParse('yocantech.com', []);
        var_dump($data);
    }

    public function testSearch()
    {
        $googleSvc = new GoogleSearchEngine();
        $payload = [
            'client_id' => 111,
            'task_id' => 222,
            'user_id' => 333,
            'stage' => 'ai_sdr_seller_homepage_crawler'
        ];
        $googleSvc->setPayload($payload);
        $resp = $googleSvc->search("深圳市格盛力能源有限公司 site:1688.com inurl:factory");
        foreach ($resp['result'] as $page) {
            $title = $page['title']??'';
            if (!empty($title)&&str_contains($title, '深圳市格盛力能源有限公司'))
            {
                var_dump($page['url'] ?? '');
                break;
            }
        }
    }

    public function testBaiduSearch()
    {
        $baiduSvc = new BaiduSearchEngine();
        $payload = ['scene' => 'ai_sdr_seller_homepage_crawler'];
        $baiduSvc->setPayload($payload);
        $baiduSvc->setTargetCount(10);
        $urlList = $baiduSvc->search('深圳市小满科技有限公司')['list'] ?? [];
        var_dump($urlList);
    }

    public function testGetqichacha()
    {
        $db = \Yii::app()->xiaoman_oss_db;
        $clientId = 2314;
        $sql = "SELECT * 
                FROM crm_company a 
                LEFT JOIN crm_company_client b ON a.id=b.company_id
                WHERE client_id = {$clientId} AND b.enable_flag = 1
                ORDER BY a.create_time DESC 
                LIMIT 1
                ";
        var_dump($db->createCommand($sql)->queryAll());
    }

    public function testGetCrmProduct()
    {
        \User::setLoginUserById(*********);
        $cn_products = [];
        $en_products = [];
        $products = ClientProduct::findProductsByClientId(333392, 123);
        foreach ($products as $product) {
            $cn_products[] = $product['cn_name'];
            $en_products[] = $product['name'];
        }
        var_dump([
                'cn_products' => array_unique($cn_products),
                'en_products' => array_unique($en_products)
            ]
        );
    }

    public function testQichachaFailed(){
        $inner = new InnerApi('oss_api');
        $inner->setHttpMethod(InnerApi::HTTP_METHOD_GET);
        $params = [
            'client_id' => 364409,
        ];

        var_dump($inner->call('getQichachaInfo', $params));
    }

    public function testGenerateLiveSellerProfile()
    {
        \User::setLoginUserById(*********); // 设置当前登录用户
        $user_id = *********;
        $clientId = 333392;
        $clientModel = \Client::findByClientId($clientId);
        $companyName = $clientModel->full_name ?? '';
        $homepage = Helper::getPossibleHomepage($clientModel->homepage, $clientModel->email);
        $task = SellerProfileTask::newTask($clientId, $user_id, $companyName, $homepage, '', 'CN', 123321);
        //$clientProfile = new ClientProfile($clientId);
        //$task->sync($clientProfile);
        $task->generate(true);
    }

    public function testHomepageAiAgent(){
        \User::setLoginUserById(*********);
        $user_id = *********;
        $client_id = 333392;
        $aiAgent = New HomepageAiAgent($client_id,$user_id);
        $params = ['company_name'=>'深圳市小满科技有限公司'];

        $resp= $aiAgent->process($params);
        var_dump($resp->answer);
        var_dump($resp->recordId);
    }

    public function testSql(){
        $svc = new AISdrService(333392, 111);
        $beginDate = '2025-04-09';
        $endDate = '2025-04-10';
        var_dump($svc->getWorksReport(5217289190, $beginDate, $endDate));
    }

    public function testRemove(){
        $userId = *********;
        \User::setLoginUserById($userId);
        $clientId = 333392;
        $taskId=5223884100;
        $lead_id = [5269784288];


        $svc = new \common\library\ai_sdr\MessageService($clientId,$taskId);
        foreach ($lead_id as $item) {
            $svc->newRemoveLeadsReasons($item, $userId);
        }
    }

    public function testGenerateEmail()
    {
        $client_id= 3445;
        $mailAddressList = array_column(UserMail::findByClient($client_id), 'email_address');
        $possibleHomepage = '';
        foreach ($mailAddressList as $item) {
            $possibleHomepage = Helper::getHomepageByEmail($item);
            if (!empty($possibleHomepage)) {
                break;
            }
        }
        var_dump($possibleHomepage);
    }

    public function testConfirm()
    {
        $clientId= 333392;
        $userId = *********;
        $taskId = 5309265226;
        $originStatus = 4;

        $svc= new AISdrService($clientId,$userId);
        $task = new AISdrTask($clientId, $taskId);
        $data = $svc->getFrontedStageDetails($task->task_id, $originStatus, $task->end_stage);

        var_dump($data);

        //$job = new DeliveryFrontedStageDetailsJob($clientId, $userId, $taskId,$originStatus);
        //$job->handle();

        $job = new \common\library\ai_sdr\jobs\DeliveryFrontedStageDetailsJob($clientId, $userId, $taskId, $originStatus, $task->end_stage);
        $job->handle();
    }

    public function testJob(){
        $userId = *********;
        $clientId = 333392;
        $taskId = 5272162927;
        $clientModel = \Client::findByClientId($clientId);
        $companyName = $clientModel->full_name ?? '';
        $homepage = Helper::getPossibleHomepage($clientModel->homepage, $clientModel->email);
        $sellerTask = SellerProfileTask::newTask($clientId, $userId, $companyName, $homepage, '', $clientModel->country,$taskId);
        #投递卖家画像任务
        $sellerJob = new AiSdrLiveSellerProfileJob($sellerTask->task_id, $clientId, $userId, $taskId);
        QueueService::dispatch($sellerJob);
    }

    public function testRemoveLeads(){
        $clientId = 333392;
        $taskId = 5308385279;
        $leadId = 5308390610;
        $userId = *********;
        \User::setLoginUserById($userId);
        $svc = new MessageService($clientId,$taskId);
        $svc->newRemoveLeadsReasons($leadId, $userId);
    }

    public function testNewFilterTipsMsg(){
        $clientId = 333392;
        $taskId = 5308385279;
        $msgSvc =  new MessageService($clientId,$taskId);
        $msgSvc->newFilterTipNotification('自行车',Constant::AI_SDR_MESSAGE_FILTER_TIPS_TYPE_PRODUCT);
    }


}
