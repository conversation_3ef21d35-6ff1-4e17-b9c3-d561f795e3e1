<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/4/16
 * Time: 上午10:48
 */



class AccountTest extends InternalFunctionalTestCase
{
    static $accountUserId ='********';
    static $account = '<EMAIL>';
    static $oldPwd = '1q2w3e4r';
    static $newPwd = '1q2w3e4rnew';
    static $accountClientId;


    public function testResetPassword()
    {
        $userId = *********;
        $user = \User::getUserObject($userId, 14582);
        $account = new \common\library\account\Account($user->getInfoObject()->email);

//        $account->checkNormal();
        $account->directSetPassword($new_password = '1q2w3e4R',\common\library\account\action\Password::DIRECT_SET_PASSWORD_TYPE_RESET, $user->getUserId(), '', '');
        \common\library\account\service\LoginService::releaseLockAccount($account->account);
        \common\library\account\service\LoginService::resetSession([$userId]);
        $account->directSetPassword($new_password, \common\library\account\Account::DIRECT_SET_PASSWORD_TYPE_UPDATE, User::getLoginUser()->getUserId(), $ip, $device_id);
    }
    public function  testCreate()
    {
        $data = [
            'nickname' => 'ccccccc',
            'family_name' => 'aaaaa',
            'second_name' => '2222',
        ];
        $params = [
            'account' => self::$account,
            'password' => self::$oldPwd,
            'activation' => '1',
            'data' => json_encode($data),
        ];

        $result = $this->callAction('Create', $params);

        $this->echoResult($result);
        $this->responseOk($result);

        $result = json_decode($result, true);
        self::$accountUserId = $result['data']['user_id'];
        self::$accountClientId = $result['data']['client_id'];

    }

    public function testUpdateInfo()
    {
        $data = [
            'nickname' => 'ddddddd',
            'family_name' => 'ddddddd',
            'second_name' => '22ddddddd22',
        ];
        $params = [
            'user_id' => self::$accountUserId,
            'data' => json_encode($data),
        ];

        $result = $this->callAction('UpdateInfo', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testVerifyPassword()
    {
        $params = [
            'account' => self::$account,
            'password' => self::$oldPwd,
        ];

        $result = $this->callAction('VerifyPassword', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testChangePassword()
    {
        $params = [
            'account' => self::$account,
            'password' => self::$newPwd,
        ];

        $result = $this->callAction('ChangePassword', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testFreeze()
    {
        $userId = 1;
        $params = [
            'user_id' => $userId
        ];

        $result = $this->callAction('Freeze', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }


    public function testUnFreeze()
    {
        $params = [
            'user_id' => self::$accountUserId
        ];

        $result = $this->callAction('UnFreeze', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }


    public function testDelete()
    {
        $params = [
            'account' => self::$account
        ];

        $result = $this->callAction('Delete', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testBindMobile()
    {
        $params = [
            'user_id' => self::$accountUserId,
            'mobile'  => '***********',
            'country_code' =>'86'
        ];

        $result = $this->callAction('BindMobile', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testUnbindMobile()
    {
        $params = [
            'user_id' => self::$accountUserId,
        ];

        $result = $this->callAction('UnbindMobile', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }


    public function testUnbindWechat()
    {
        $params = [
            'user_id' => self::$accountUserId,
        ];

        $result = $this->callAction('UnbindWechat', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testOpenSafeLogin()
    {
        $params = [
            'user_id' => '51093',
        ];

        $result = $this->callAction('OpenSafeLogin', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testCloseSafeLogin()
    {
        $params = [
            'user_id' => self::$accountUserId,
        ];

        $result = $this->callAction('CloseSafeLogin', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }

    public function testJoinClient()
    {
        $params = [
            'user_id' => self::$accountUserId,
            'client_id' => 2,//self::$accountClientId,
        ];

        $result = $this->callAction('joinClient', $params);

        $this->echoResult($result);
        $this->responseOk($result);
    }


    /**
     * @test
     */
    public function it_can_get_user_info()
    {
        $email = '<EMAIL>';

        $info = UserInfo::model()->findByAttributes([
            'email' => $email
        ]);

        $this->echo([
            'user_id' => $info['user_id'],
            'client_id' => $info['client_id'],
        ]);
    }

}