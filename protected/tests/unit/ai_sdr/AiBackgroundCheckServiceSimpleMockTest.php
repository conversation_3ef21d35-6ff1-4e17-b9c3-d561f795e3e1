<?php

namespace tests\unit\ai_sdr;

use PHPUnit\Framework\TestCase;
use Mockery;
use common\library\api\InnerApi;
use common\library\ai_sdr\AiBackgroundCheckService;
use common\library\ai_sdr\Constant;

/**
 * AiBackgroundCheckService 简单 Mock 测试类
 * 
 * 使用 Mockery 直接 mock InnerApi 来测试 AiBackgroundCheckService
 */
class AiBackgroundCheckServiceSimpleMockTest extends TestCase
{
    /**
     * @var int 测试客户端ID
     */
    private $clientId = 12345;

    /**
     * @var int 测试用户ID
     */
    private $userId = 67890;

    /**
     * @var Mockery\MockInterface Mock InnerApi 实例
     */
    private $mockInnerApi;

    /**
     * @var AiBackgroundCheckService 测试服务实例
     */
    private $service;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建 Mock InnerApi
        $this->mockInnerApi = Mockery::mock(InnerApi::class);
        
        // 设置基本方法的默认行为
        $this->mockInnerApi->shouldReceive('addHeader')->andReturnSelf();
        
        // 创建可测试的服务子类
        $this->service = new class($this->clientId, $this->userId) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function setMockApi($mockApi) {
                $this->mockApi = $mockApi;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return $this->mockApi;
            }
        };
        
        // 设置 Mock API
        $this->service->setMockApi($this->mockInnerApi);
    }

    /**
     * 清理测试环境
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试标准化内容成功场景
     */
    public function testGetStandardResultSuccess()
    {
        // 设置 Mock 响应
        $expectedResponse = [
            'std_id' => 123,
            'std_name' => 'Technology',
            'is_adopted' => true,
            'confidence' => 0.95,
            'original_content' => 'tech',
            'standardized_content' => 'Technology'
        ];

        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'tech',
                'std_type' => 'industry',
                'threshold' => 0.8
            ])
            ->andReturn($expectedResponse);

        // 调用服务方法
        $result = $this->service->getStandardResult('tech', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(123, $result['std_id']);
        $this->assertEquals('Technology', $result['std_name']);
        $this->assertTrue($result['is_adopted']);
        $this->assertEquals(0.95, $result['confidence']);
    }

    /**
     * 测试产品标准化成功场景
     */
    public function testGetStandardResultForProduct()
    {
        // 设置 Mock 响应
        $expectedResponse = [
            'std_id' => 456,
            'std_name' => 'Software Development',
            'is_adopted' => true,
            'confidence' => 0.90
        ];

        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'software dev',
                'std_type' => 'product',
                'threshold' => 0.7
            ])
            ->andReturn($expectedResponse);

        // 调用服务方法（产品类型）
        $result = $this->service->getStandardResult('software dev', 2, 0.7);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(456, $result['std_id']);
        $this->assertEquals('Software Development', $result['std_name']);
    }

    /**
     * 测试带向量嵌入的标准化
     */
    public function testGetStandardResultWithEmbeddings()
    {
        $embeddings = [0.1, 0.2, 0.3, 0.4, 0.5];
        
        $expectedResponse = [
            'std_id' => 789,
            'std_name' => 'Technology',
            'is_adopted' => true,
            'confidence' => 0.98
        ];

        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'technology',
                'std_type' => 'industry',
                'threshold' => 0.8,
                'vector' => $embeddings
            ])
            ->andReturn($expectedResponse);

        // 调用服务方法
        $result = $this->service->getStandardResult('technology', 1, 0.8, $embeddings);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(789, $result['std_id']);
        $this->assertEquals(0.98, $result['confidence']);
    }

    /**
     * 测试无匹配结果场景
     */
    public function testGetStandardResultNoMatch()
    {
        // 设置无匹配响应
        $expectedResponse = [
            'std_id' => 0,
            'std_name' => '',
            'is_adopted' => false,
            'confidence' => 0.2
        ];

        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->andReturn($expectedResponse);

        // 调用服务方法
        $result = $this->service->getStandardResult('unknown_industry', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(0, $result['std_id']);
        $this->assertFalse($result['is_adopted']);
        $this->assertEquals(0.2, $result['confidence']);
    }

    /**
     * 测试空响应场景
     */
    public function testGetStandardResultEmptyResponse()
    {
        // 设置空响应
        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->andReturn(null);

        // 调用服务方法
        $result = $this->service->getStandardResult('test', 1);

        // 验证返回空数组
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * 测试 API 异常处理
     */
    public function testGetStandardResultApiException()
    {
        // 设置 API 异常
        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->andThrow(new \Exception('Service temporarily unavailable', 503));

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get data, please try again later');

        // 调用服务方法
        $this->service->getStandardResult('test', 1);
    }

    /**
     * 测试网络超时异常
     */
    public function testGetStandardResultTimeout()
    {
        // 设置超时异常
        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->andThrow(new \Exception('Request timeout', 28));

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get data, please try again later');

        // 调用服务方法
        $this->service->getStandardResult('test', 1);
    }

    /**
     * 测试无效内容类型
     */
    public function testGetStandardResultInvalidContentType()
    {
        // 期望抛出异常
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid content type');

        // 调用服务方法（无效的内容类型）
        $this->service->getStandardResult('test', 99);
    }

    /**
     * 测试默认阈值
     */
    public function testGetStandardResultDefaultThreshold()
    {
        $expectedResponse = ['std_id' => 1];

        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'test',
                'std_type' => 'industry',
                'threshold' => Constant::MIN_THRESHOLD
            ])
            ->andReturn($expectedResponse);

        // 调用服务方法（不指定阈值）
        $result = $this->service->getStandardResult('test', 1);

        // 验证结果
        $this->assertIsArray($result);
    }

    /**
     * 测试 Header 设置
     */
    public function testApiHeaderSetting()
    {
        $expectedResponse = ['std_id' => 1];

        // 验证 addHeader 被正确调用
        $this->mockInnerApi
            ->shouldReceive('addHeader')
            ->once()
            ->with(["cookie:clientId={$this->clientId};userId={$this->userId}"]);

        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->andReturn($expectedResponse);

        // 调用服务方法
        $this->service->getStandardResult('test', 1);
    }

    /**
     * 测试多种内容类型映射
     */
    public function testContentTypeMapping()
    {
        $expectedResponse = ['std_id' => 1];

        // 测试行业类型 (1 -> 'industry')
        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('standardizeContent', Mockery::subset([
                'std_type' => 'industry'
            ]))
            ->andReturn($expectedResponse);

        $this->service->getStandardResult('test', 1);

        // 重置 Mock
        $this->mockInnerApi = Mockery::mock(InnerApi::class);
        $this->mockInnerApi->shouldReceive('addHeader')->andReturnSelf();
        $this->service->setMockApi($this->mockInnerApi);

        // 测试产品类型 (2 -> 'product')
        $this->mockInnerApi
            ->shouldReceive('call')
            ->once()
            ->with('standardizeContent', Mockery::subset([
                'std_type' => 'product'
            ]))
            ->andReturn($expectedResponse);

        $this->service->getStandardResult('test', 2);
    }
}
