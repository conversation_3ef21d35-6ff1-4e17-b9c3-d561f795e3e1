<?php

namespace tests\unit\ai_sdr;

use PHPUnit\Framework\TestCase;
use tests\mockery\inner_api\traits\MockInnerApiTrait;
use tests\mockery\inner_api\InnerApiFactoryMockery;
use tests\mockery\inner_api\MockInnerApi;
use common\library\ai_sdr\AiBackgroundCheckService;
use common\library\ai_sdr\Constant;

/**
 * AiBackgroundCheckService Mock 测试类
 * 
 * 测试 AiBackgroundCheckService 与 InnerApi 的交互
 */
class AiBackgroundCheckServiceMockTest extends TestCase
{
    use MockInnerApiTrait;

    /**
     * @var int 测试客户端ID
     */
    private $clientId = 12345;

    /**
     * @var int 测试用户ID
     */
    private $userId = 67890;

    /**
     * @var AiBackgroundCheckService 测试服务实例
     */
    private $service;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // 初始化 InnerApi Mockery
        self::setUpInnerApiMockery();
        
        // 创建服务实例
        $this->service = new AiBackgroundCheckService($this->clientId, $this->userId);
        
        // 重写 innerApiFactory 方法以返回 Mock 对象
        $this->mockInnerApiFactory();
    }

    /**
     * 清理测试环境
     */
    protected function tearDown(): void
    {
        self::tearDownInnerApiMockery();
        parent::tearDown();
    }

    /**
     * Mock innerApiFactory 方法
     */
    private function mockInnerApiFactory(): void
    {
        // 创建一个可测试的服务子类
        $this->service = new class($this->clientId, $this->userId) extends AiBackgroundCheckService {
            protected function innerApiFactory(string $module, string $method = \common\library\api\InnerApi::HTTP_METHOD_POST_JSON): \common\library\api\InnerApi
            {
                return InnerApiFactoryMockery::createMockInnerApi($module);
            }
        };
    }

    /**
     * 测试标准化内容成功场景
     */
    public function testGetStandardResultSuccess()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario([
            'std_id' => 123,
            'std_name' => 'Technology',
            'is_adopted' => true,
            'confidence' => 0.95,
            'original_content' => 'tech',
            'standardized_content' => 'Technology'
        ]);

        // 调用服务方法
        $result = $this->service->getStandardResult('tech', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(123, $result['std_id']);
        $this->assertEquals('Technology', $result['std_name']);
        $this->assertTrue($result['is_adopted']);
        $this->assertEquals(0.95, $result['confidence']);

        // 验证 API 调用
        $this->assertStandardizeContentWasCalled();
        $this->assertStandardizeContentCalledWith([
            'content' => 'tech',
            'std_type' => 'industry',
            'threshold' => 0.8
        ]);
    }

    /**
     * 测试产品标准化成功场景
     */
    public function testGetStandardResultForProduct()
    {
        // 设置产品标准化成功响应
        $this->setProductStandardizationSuccess('Software Development', 456);

        // 调用服务方法（产品类型）
        $result = $this->service->getStandardResult('software dev', 2, 0.7);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(456, $result['std_id']);
        $this->assertEquals('Software Development', $result['std_name']);

        // 验证 API 调用参数
        $this->assertStandardizeContentCalledWith([
            'content' => 'software dev',
            'std_type' => 'product',
            'threshold' => 0.7
        ]);
    }

    /**
     * 测试带向量嵌入的标准化
     */
    public function testGetStandardResultWithEmbeddings()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        $embeddings = [0.1, 0.2, 0.3, 0.4, 0.5];

        // 调用服务方法
        $result = $this->service->getStandardResult('technology', 1, 0.8, $embeddings);

        // 验证 API 调用参数包含向量
        $this->assertStandardizeContentCalledWith([
            'content' => 'technology',
            'std_type' => 'industry',
            'threshold' => 0.8,
            'vector' => $embeddings
        ]);
    }

    /**
     * 测试无匹配结果场景
     */
    public function testGetStandardResultNoMatch()
    {
        // 设置无匹配响应
        $this->setStandardizeContentNoMatchScenario([
            'confidence' => 0.2,
            'original_content' => 'unknown_industry'
        ]);

        // 调用服务方法
        $result = $this->service->getStandardResult('unknown_industry', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(0, $result['std_id']);
        $this->assertEquals('', $result['std_name']);
        $this->assertFalse($result['is_adopted']);
        $this->assertEquals(0.2, $result['confidence']);
    }

    /**
     * 测试空响应场景
     */
    public function testGetStandardResultEmptyResponse()
    {
        // 设置空响应
        $this->setApiResponse('ai_background_check', 'standardizeContent', [
            'code' => 0,
            'message' => 'success',
            'data' => null
        ]);

        // 调用服务方法
        $result = $this->service->getStandardResult('test', 1);

        // 验证返回空数组
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * 测试 API 异常处理
     */
    public function testGetStandardResultApiException()
    {
        // 设置 API 异常
        $this->setApiException('ai_background_check', 'standardizeContent', [
            'class' => \Exception::class,
            'message' => 'Service temporarily unavailable',
            'code' => 503
        ]);

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get data, please try again later');

        // 调用服务方法
        $this->service->getStandardResult('test', 1);
    }

    /**
     * 测试网络超时异常
     */
    public function testGetStandardResultTimeout()
    {
        // 设置超时异常
        $this->setNetworkTimeout();

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get data, please try again later');

        // 调用服务方法
        $this->service->getStandardResult('test', 1);
    }

    /**
     * 测试无效内容类型
     */
    public function testGetStandardResultInvalidContentType()
    {
        // 期望抛出异常
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid content type');

        // 调用服务方法（无效的内容类型）
        $this->service->getStandardResult('test', 99);
    }

    /**
     * 测试默认阈值
     */
    public function testGetStandardResultDefaultThreshold()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        // 调用服务方法（不指定阈值）
        $result = $this->service->getStandardResult('test', 1);

        // 验证使用默认阈值
        $this->assertStandardizeContentCalledWith([
            'content' => 'test',
            'std_type' => 'industry',
            'threshold' => Constant::MIN_THRESHOLD
        ]);
    }

    /**
     * 测试多次调用
     */
    public function testMultipleApiCalls()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        // 多次调用
        $this->service->getStandardResult('tech1', 1);
        $this->service->getStandardResult('tech2', 1);
        $this->service->getStandardResult('product1', 2);

        // 验证调用次数
        $this->assertApiCallCount('ai_background_check', 'standardizeContent', 3);

        // 验证调用统计
        $statistics = $this->getApiCallStatistics();
        $this->assertEquals(3, $statistics['ai_background_check::standardizeContent']);
    }

    /**
     * 测试 API 调用日志
     */
    public function testApiCallLogging()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        // 调用服务方法
        $this->service->getStandardResult('test_content', 1, 0.9);

        // 获取调用日志
        $callLog = $this->getApiCallLog();
        
        // 验证日志记录
        $this->assertCount(1, $callLog);
        $this->assertEquals('ai_background_check', $callLog[0]['module']);
        $this->assertEquals('standardizeContent', $callLog[0]['interface']);
        $this->assertArrayHasKey('timestamp', $callLog[0]);
        
        // 验证参数记录
        $params = $callLog[0]['params'];
        $this->assertEquals('test_content', $params['content']);
        $this->assertEquals('industry', $params['std_type']);
        $this->assertEquals(0.9, $params['threshold']);
    }

    /**
     * 测试清理调用日志
     */
    public function testClearApiCallLog()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        // 调用服务方法
        $this->service->getStandardResult('test', 1);

        // 验证有调用记录
        $this->assertCount(1, $this->getApiCallLog());

        // 清理日志
        $this->clearApiCallLog();

        // 验证日志已清空
        $this->assertCount(0, $this->getApiCallLog());
    }

    /**
     * 测试只调用预期的 API
     */
    public function testOnlyExpectedApisCalled()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        // 调用服务方法
        $this->service->getStandardResult('test', 1);

        // 验证只调用了预期的 API
        $this->assertOnlyExpectedApisCalled(['ai_background_check::standardizeContent']);
    }

    /**
     * 测试服务不可用场景
     */
    public function testServiceUnavailable()
    {
        // 设置服务不可用
        $this->setApiServiceUnavailable();

        // 期望抛出异常
        $this->expectException(\Exception::class);

        // 调用服务方法
        $this->service->getStandardResult('test', 1);
    }

    /**
     * 测试打印调用统计（用于调试）
     */
    public function testPrintApiCallStatistics()
    {
        // 设置成功响应
        $this->setStandardizeContentSuccessScenario();

        // 多次调用
        $this->service->getStandardResult('test1', 1);
        $this->service->getStandardResult('test2', 2);

        // 捕获输出
        ob_start();
        $this->printApiCallStatistics();
        $output = ob_get_clean();

        // 验证输出包含统计信息
        $this->assertStringContains('API Call Statistics', $output);
        $this->assertStringContains('ai_background_check::standardizeContent: 2 calls', $output);
    }
}
