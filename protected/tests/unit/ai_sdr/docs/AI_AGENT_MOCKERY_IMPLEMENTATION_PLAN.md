# AI Agent Mockery框架实施计划

## 📋 项目概述

使用Mockery框架实现对AiAgentFactory::createAgent方法的mock，覆盖SDR测试流程中所有AI Agent的调用。

## 🎯 需要Mock的AI Agent清单

### 1. 核心质量分析Agent
- **SdrLeadQualityAnalysisAgent** (`AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS`)
  - 用途：潜客质量分析
  - 调用场景：SdrDetailExecutor::performAction中的label_lead_quality动作
  - 返回格式：`['answer' => ['lead_quality' => 'high/medium/low', 'confidence' => float, 'reason' => array]]`

- **SdrHutchLeadQualityAgent** (`AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS`)
  - 用途：Hutch线索质量分析
  - 调用场景：非AI_SDR和CRM_EP来源的详情处理
  - 返回格式：同SdrLeadQualityAnalysisAgent

### 2. 营销内容生成Agent
- **SdrEdmWriteAgent** (`AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER`)
  - 用途：EDM邮件内容生成
  - 调用场景：SdrDetailExecutor::performAction中的start_marketing动作
  - 返回格式：`[['subject' => string, 'content' => string, 'round' => int]]`

### 3. 档案生成Agent
- **AiSdrSellerProfileAgent** (`AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION`)
  - 用途：卖家档案生成
  - 调用场景：卖家档案任务处理
  - 返回格式：包含公司信息、产品、行业等结构化数据

- **AiSdrBuyerProfileAgent** (`AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION`)
  - 用途：买家档案生成
  - 调用场景：买家档案任务处理
  - 返回格式：包含买家信息、需求、偏好等数据

### 4. 分析类Agent
- **SdrSellerIndustryAnalyze** (`AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE`)
  - 用途：卖家行业分析
  - 调用场景：行业匹配度分析
  - 返回格式：`[['industry' => string, 'relative' => int]]`

- **HomepageAiAgent** (`AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH`)
  - 用途：主页内容分析
  - 调用场景：公司主页信息提取
  - 返回格式：包含公司信息、联系方式等

- **AiSdrProductCategoryAiAgent** (`AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY`)
  - 用途：产品分类清洗
  - 调用场景：产品分类标准化
  - 返回格式：标准化的产品分类数据

## 🔧 实施步骤

### 第一阶段：创建Mockery基础设施
1. [ ] 创建AiAgentFactoryMockery类
2. [ ] 实现createAgent方法的Mock逻辑
3. [ ] 创建各个Agent的Mock实现类
4. [ ] 建立测试数据工厂

### 第二阶段：实现具体Agent Mock
1. [ ] MockSdrLeadQualityAnalysisAgent
2. [ ] MockSdrEdmWriteAgent  
3. [ ] MockSdrSellerIndustryAnalyze
4. [ ] MockAiSdrSellerProfileAgent
5. [ ] MockAiSdrBuyerProfileAgent
6. [ ] MockHomepageAiAgent
7. [ ] MockAiSdrProductCategoryAiAgent
8. [ ] MockSdrHutchLeadQualityAgent

### 第三阶段：集成测试框架
1. [ ] 修改现有测试基类支持Mockery
2. [ ] 创建Agent Mock管理器
3. [ ] 实现测试场景配置
4. [ ] 添加Mock验证机制

### 第四阶段：测试覆盖
1. [ ] 单元测试覆盖
2. [ ] 功能测试集成
3. [ ] 端到端测试验证
4. [ ] 性能测试确认

## 📁 文件结构规划

```
protected/tests/unit/ai_sdr/mockery/
├── AiAgentFactoryMockery.php          # 主要的Factory Mock类
├── agents/                            # 各个Agent的Mock实现
│   ├── MockSdrLeadQualityAnalysisAgent.php
│   ├── MockSdrEdmWriteAgent.php
│   ├── MockSdrSellerIndustryAnalyze.php
│   ├── MockAiSdrSellerProfileAgent.php
│   ├── MockAiSdrBuyerProfileAgent.php
│   ├── MockHomepageAiAgent.php
│   ├── MockAiSdrProductCategoryAiAgent.php
│   └── MockSdrHutchLeadQualityAgent.php
├── data/                              # 测试数据工厂
│   ├── AgentResponseDataFactory.php
│   └── AgentTestScenarios.php
├── traits/                            # 测试辅助Trait
│   ├── MockeryAgentTrait.php
│   └── AgentAssertionTrait.php
└── README.md                          # 使用说明文档
```

## 🎯 核心设计原则

1. **完全兼容**：Mock实现必须与真实Agent接口完全兼容
2. **场景驱动**：支持不同测试场景的响应配置
3. **可验证性**：提供调用验证和参数检查机制
4. **易用性**：简化测试代码编写，提供便捷的配置方法
5. **可扩展性**：支持新Agent类型的快速添加

## 📊 预期收益

1. **测试速度**：AI Agent调用从秒级降低到毫秒级
2. **测试稳定性**：消除外部AI服务依赖导致的测试不稳定
3. **测试覆盖率**：支持各种边界情况和异常场景测试
4. **开发效率**：开发者可以快速验证业务逻辑
5. **CI/CD友好**：测试可以在任何环境下稳定运行

## 🚀 开始实施

准备开始第一阶段的实施，请确认是否开始创建基础设施代码？
