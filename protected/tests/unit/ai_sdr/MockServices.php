<?php

namespace tests\unit\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\interfaces\RecommendApiInterface;
use common\library\ai_sdr\interfaces\LeadAutoArchiveInterface;
use common\library\ai_sdr\interfaces\QueueServiceInterface;
use common\library\ai_sdr\interfaces\AiAgentFactoryInterface;

require_once __DIR__ . '/MockAgents.php';

/**
 * AI SDR Mock服务集合
 *
 * 提供各种外部服务的Mock实现
 */
class MockServices
{
    /**
     * 创建所有Mock服务
     */
    public static function createAllMockServices(): array
    {
        return [
            'timeService' => self::createMockTimeService(),
            'cacheService' => self::createMockCacheService(),
            'queueService' => self::createMockQueueService(),
            'aiServices' => self::createMockAiServices(),
            'recommendApi' => self::createMockRecommendApi(),
            'leadAutoArchive' => self::createMockLeadAutoArchive()
        ];
    }

    /**
     * 只创建推荐API Mock服务（用于端到端测试）
     */
    public static function createMockRecommendApiOnly(): array
    {
        return [
            'recommendApi' => self::createMockRecommendApi()
        ];
    }

    /**
     * Mock时间服务
     */
    public static function createMockTimeService(string $fixedTime = '2024-01-01 10:00:00'): MockTimeService
    {
        return new MockTimeService($fixedTime);
    }
    
    /**
     * Mock缓存服务
     */
    public static function createMockCacheService(): MockCacheService
    {
        return new MockCacheService();
    }
    
    /**
     * Mock队列服务
     */
    public static function createMockQueueService(): MockQueueService
    {
        return new MockQueueService();
    }
    
    /**
     * Mock AI服务
     */
    public static function createMockAiServices(): MockAiServices
    {
        return new MockAiServices();
    }
    
    /**
     * Mock推荐API服务
     */
    public static function createMockRecommendApi(): MockRecommendApi
    {
        return new MockRecommendApi();
    }
    
    /**
     * Mock线索自动归档服务
     */
    public static function createMockLeadAutoArchive(): MockLeadAutoArchive
    {
        return new MockLeadAutoArchive();
    }
}

/**
 * Mock时间服务
 */
class MockTimeService
{
    private string $fixedTime;
    
    public function __construct(string $fixedTime = '2024-01-01 10:00:00')
    {
        $this->fixedTime = $fixedTime;
    }
    
    public function now(): string
    {
        return $this->fixedTime;
    }
    
    public function timestamp(): int
    {
        return strtotime($this->fixedTime);
    }
    
    public function setTime(string $time): void
    {
        $this->fixedTime = $time;
    }
    
    public function addMinutes(int $minutes): void
    {
        $timestamp = strtotime($this->fixedTime) + ($minutes * 60);
        $this->fixedTime = date('Y-m-d H:i:s', $timestamp);
    }
}

/**
 * Mock缓存服务
 */
class MockCacheService
{
    private array $data = [];
    private array $accessLog = [];
    private array $operations = [];
    
    public function set(string $key, $value, int $ttl = 3600): bool
    {
        $this->accessLog[] = ['operation' => 'set', 'key' => $key, 'ttl' => $ttl];
        $this->operations[] = "SET {$key}";
        $this->data[$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        return true;
    }
    
    public function get(string $key)
    {
        $this->accessLog[] = ['operation' => 'get', 'key' => $key];
        $this->operations[] = "GET {$key}";
        
        if (!isset($this->data[$key])) {
            return null;
        }
        
        if ($this->data[$key]['expires'] < time()) {
            unset($this->data[$key]);
            return null;
        }
        
        return $this->data[$key]['value'];
    }
    
    public function delete(string $key): bool
    {
        $this->accessLog[] = ['operation' => 'delete', 'key' => $key];
        $this->operations[] = "DEL {$key}";
        unset($this->data[$key]);
        return true;
    }
    
    public function setNx(string $key, $value, int $ttl = 3600): bool
    {
        $this->accessLog[] = ['operation' => 'setnx', 'key' => $key, 'ttl' => $ttl];
        $this->operations[] = "SETNX {$key}";
        
        if (isset($this->data[$key]) && $this->data[$key]['expires'] >= time()) {
            return false;
        }
        
        $this->data[$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        return true;
    }
    
    public function wasKeyAccessed(string $key): bool
    {
        foreach ($this->accessLog as $log) {
            if ($log['key'] === $key) {
                return true;
            }
        }
        return false;
    }
    
    public function getAccessCount(string $key): int
    {
        $count = 0;
        foreach ($this->accessLog as $log) {
            if ($log['key'] === $key) {
                $count++;
            }
        }
        return $count;
    }
    
    public function getOperations(): array
    {
        return $this->operations;
    }
    
    public function clear(): void
    {
        $this->data = [];
        $this->accessLog = [];
        $this->operations = [];
    }
}

/**
 * Mock AI服务
 */
class MockAiServices
{
    private $qualityAnalysisResult = null;
    private $qualityAnalysisResults = [];
    private ?\Throwable $qualityAnalysisException = null;
    private $backgroundCheckResult = null;
    private $marketingContentResult = null;
    
    private $qualityAnalysisCalled = false;
    private $backgroundCheckCalled = false;
    private $marketingContentCalled = false;
    
    public function setQualityAnalysisResult(array $result): void
    {
        $this->qualityAnalysisResult = $result;
    }
    
    public function setQualityAnalysisResults(array $results): void
    {
        $this->qualityAnalysisResults = $results;
    }
    
    public function setQualityAnalysisException(\Exception $exception): void
    {
        $this->qualityAnalysisException = $exception;
    }
    
    public function setBackgroundCheckResult(array $result): void
    {
        $this->backgroundCheckResult = $result;
    }
    
    public function setMarketingContentResult(array $result): void
    {
        $this->marketingContentResult = $result;
    }
    
    public function getQualityAnalysisResult(): array
    {
        $this->qualityAnalysisCalled = true;
        
        if ($this->qualityAnalysisException) {
            throw $this->qualityAnalysisException;
        }
        
        if (!empty($this->qualityAnalysisResults)) {
            return array_shift($this->qualityAnalysisResults);
        }
        
        return $this->qualityAnalysisResult ?? [
            'answer' => [
                'lead_quality' => Constant::LEAD_QUALITY_MEDIUM,
                'confidence' => 0.7,
                'reason' => ['Default mock result']
            ]
        ];
    }
    
    public function getBackgroundCheckResult(): array
    {
        $this->backgroundCheckCalled = true;
        return $this->backgroundCheckResult ?? [
            'task_id' => 12345,
            'status' => 'success'
        ];
    }
    
    public function getMarketingContentResult(): array
    {
        $this->marketingContentCalled = true;
        return $this->marketingContentResult ?? [
            [
                'subject' => 'Default Mock Subject',
                'content' => 'Default mock email content',
                'round' => 1
            ]
        ];
    }
    
    public function wasQualityAnalysisCalled(): bool
    {
        return $this->qualityAnalysisCalled;
    }
    
    public function wasBackgroundCheckCalled(): bool
    {
        return $this->backgroundCheckCalled;
    }
    
    public function wasMarketingContentCalled(): bool
    {
        return $this->marketingContentCalled;
    }
    
    public function reset(): void
    {
        $this->qualityAnalysisCalled = false;
        $this->backgroundCheckCalled = false;
        $this->marketingContentCalled = false;
        $this->qualityAnalysisException = null;
    }
}

/**
 * Mock推荐API
 */
class MockRecommendApi implements RecommendApiInterface
{
    private array $responses = [];
    private array $callLog = [];
    private array $matchResults = [];
    private array $emailRatings = [];
    private array $recommendList = [];
    private array $searchResults = [];

    public function setResponse(array $domains, array $response): void
    {
        foreach ($domains as $domain) {
            $this->responses[$domain] = $response;
        }
    }

    public function setMatchResults(array $results): void
    {
        $this->matchResults = $results;
    }

    public function setEmailRatings(array $ratings): void
    {
        $this->emailRatings = $ratings;
    }

    public function setRecommendList(array $list): void
    {
        $this->recommendList = $list;
    }

    public function setSearchResults(array $results): void
    {
        $this->searchResults = $results;
    }

    public function getCompanyProfileByDomains(array $domains): array
    {
        $this->callLog[] = [
            'method' => 'getCompanyProfileByDomains',
            'domains' => $domains,
            'timestamp' => time()
        ];

        $result = [];
        foreach ($domains as $domain) {
            if (isset($this->responses[$domain])) {
                $result[$domain] = $this->responses[$domain];
            } else {
                // 默认响应
                $result[$domain] = AiSdrTestDataFactory::createBuyerProfileData([
                    'company_name' => 'Mock Company for ' . $domain,
                    'public_homepage' => ['https://' . $domain]
                ]);
            }
        }

        return $result;
    }

    public function getMatchCompanyByProfile(
        $matchType,
        $industryIds = [],
        $industryProducts = [],
        $beforePortraitIds = null,
        $excludeDomains = [],
        $pageSize = 100
    ): array {
        $this->callLog[] = [
            'method' => 'getMatchCompanyByProfile',
            'matchType' => $matchType,
            'industryIds' => $industryIds,
            'industryProducts' => $industryProducts,
            'beforePortraitIds' => $beforePortraitIds,
            'excludeDomains' => $excludeDomains,
            'pageSize' => $pageSize,
            'timestamp' => time()
        ];

        return !empty($this->matchResults) ? $this->matchResults : [
            'data' => [
                [
                    'company_name' => 'Mock Match Company 1',
                    'domain' => 'mock1.com',
                    'industry' => 'Technology',
                    'match_score' => 0.85
                ],
                [
                    'company_name' => 'Mock Match Company 2',
                    'domain' => 'mock2.com',
                    'industry' => 'Manufacturing',
                    'match_score' => 0.78
                ]
            ],
            'total' => 2
        ];
    }

    public function getMailQualityRating(array $emails): array
    {
        $this->callLog[] = [
            'method' => 'getMailQualityRating',
            'emails' => $emails,
            'timestamp' => time()
        ];

        $result = [];
        foreach ($emails as $email) {
            if (isset($this->emailRatings[$email])) {
                $result[$email] = $this->emailRatings[$email];
            } else {
                // 默认评级
                $result[$email] = \common\library\auto_market\Constant::MAIL_LEVEL_GENERAL;
            }
        }

        return $result;
    }

    public function getRecommendList(): array
    {
        $this->callLog[] = [
            'method' => 'getRecommendList',
            'timestamp' => time()
        ];

        return $this->recommendList ?: [
            'data' => [
                [
                    'company_name' => 'Mock Recommend Company 1',
                    'domain' => 'recommend1.com'
                ]
            ]
        ];
    }

    public function getRecommendListBySearchTag(
        array $tagList,
        array $type = [],
        array $keywords = [],
        array $sort = [],
        $hasCompany = null
    ): array {
        $this->callLog[] = [
            'method' => 'getRecommendListBySearchTag',
            'tagList' => $tagList,
            'type' => $type,
            'keywords' => $keywords,
            'sort' => $sort,
            'hasCompany' => $hasCompany,
            'timestamp' => time()
        ];

        return $this->searchResults ?: [
            'data' => [
                [
                    'company_name' => 'Mock Search Result 1',
                    'domain' => 'search1.com',
                    'tags' => $tagList
                ]
            ]
        ];
    }

    public function getCallLog(): array
    {
        return $this->callLog;
    }

    public function wasMethodCalled(string $method): bool
    {
        foreach ($this->callLog as $call) {
            if ($call['method'] === $method) {
                return true;
            }
        }
        return false;
    }

    public function getCallCount(string $method): int
    {
        $count = 0;
        foreach ($this->callLog as $call) {
            if ($call['method'] === $method) {
                $count++;
            }
        }
        return $count;
    }

    public function clear(): void
    {
        $this->callLog = [];
        $this->responses = [];
        $this->matchResults = [];
        $this->emailRatings = [];
        $this->recommendList = [];
        $this->searchResults = [];
    }
}

/**
 * Mock服务工厂类
 *
 * 提供便捷的Mock服务创建方法
 */
class MockServiceFactory
{
    /**
     * 创建Mock推荐API
     */
    public static function createMockRecommendApi(): MockRecommendApi
    {
        return new MockRecommendApi();
    }

    /**
     * 创建Mock线索自动归档
     */
    public static function createMockLeadAutoArchive(): MockLeadAutoArchive
    {
        return new MockLeadAutoArchive();
    }

    /**
     * 创建Mock队列服务
     */
    public static function createMockQueueService(): MockQueueService
    {
        return new MockQueueService();
    }

    /**
     * 创建Mock AI Agent工厂
     */
    public static function createMockAiAgentFactory(?MockAiServices $mockAiServices = null): MockAiAgentFactory
    {
        return new MockAiAgentFactory($mockAiServices);
    }

    /**
     * 创建完整的Mock服务集合
     */
    public static function createMockServiceSet(): array
    {
        $mockAiServices = new MockAiServices();

        return [
            'recommendApi' => new MockRecommendApi(),
            'leadAutoArchive' => new MockLeadAutoArchive(),
            'queueService' => new MockQueueService(),
            'aiAgentFactory' => new MockAiAgentFactory($mockAiServices),
            'aiServices' => $mockAiServices,
            'cacheService' => new MockCacheService()
        ];
    }
}
