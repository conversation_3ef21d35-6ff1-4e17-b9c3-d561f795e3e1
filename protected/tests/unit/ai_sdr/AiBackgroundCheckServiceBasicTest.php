<?php

namespace tests\unit\ai_sdr;

use PHPUnit\Framework\TestCase;
use Mockery;
use common\library\api\InnerApi;
use common\library\ai_sdr\AiBackgroundCheckService;

/**
 * AiBackgroundCheckService 基础测试
 * 
 * 验证基本的 Mock 功能
 */
class AiBackgroundCheckServiceBasicTest extends TestCase
{
    private $clientId = 12345;
    private $userId = 67890;

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试基本的 Mock 功能
     */
    public function testBasicMockFunctionality()
    {
        // 创建 Mock InnerApi
        $mockApi = Mockery::mock(InnerApi::class);

        // 设置所有可能被调用的方法
        $mockApi->shouldReceive('addHeader')->once();
        $mockApi->shouldReceive('getModule')->andReturn('ai_background_check');
        $mockApi->shouldReceive('call')
            ->once()
            ->with('standardizeContent', Mockery::type('array'))
            ->andReturn([
                'std_id' => 123,
                'std_name' => 'Technology',
                'is_adopted' => true
            ]);

        // 创建测试服务
        $service = new class($this->clientId, $this->userId, $mockApi) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function __construct($clientId, $userId, $mockApi) {
                parent::__construct($clientId, $userId);
                $this->mockApi = $mockApi;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return $this->mockApi;
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                return $api->call($interface, $params);
            }
        };

        // 调用方法
        $result = $service->getStandardResult('tech', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(123, $result['std_id']);
        $this->assertEquals('Technology', $result['std_name']);
        $this->assertTrue($result['is_adopted']);
    }

    /**
     * 测试异常处理
     */
    public function testExceptionHandling()
    {
        // 创建 Mock InnerApi
        $mockApi = Mockery::mock(InnerApi::class);
        
        // 设置期望 - 抛出异常
        $mockApi->shouldReceive('addHeader')->once();
        $mockApi->shouldReceive('call')
            ->once()
            ->andThrow(new \Exception('API Error'));

        // 创建测试服务
        $service = new class($this->clientId, $this->userId, $mockApi) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function __construct($clientId, $userId, $mockApi) {
                parent::__construct($clientId, $userId);
                $this->mockApi = $mockApi;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return $this->mockApi;
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                try {
                    return $api->call($interface, $params);
                } catch (\Exception $exception) {
                    throw new \Exception('Failed to get data, please try again later', $exception->getCode());
                }
            }
        };

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to get data, please try again later');

        // 调用方法
        $service->getStandardResult('tech', 1, 0.8);
    }

    /**
     * 测试参数验证
     */
    public function testParameterValidation()
    {
        // 创建 Mock InnerApi
        $mockApi = Mockery::mock(InnerApi::class);
        
        // 设置期望 - 验证参数
        $mockApi->shouldReceive('addHeader')
            ->once()
            ->with(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
            
        $mockApi->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'technology',
                'std_type' => 'industry',
                'threshold' => 0.9
            ])
            ->andReturn(['std_id' => 1]);

        // 创建测试服务
        $service = new class($this->clientId, $this->userId, $mockApi) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function __construct($clientId, $userId, $mockApi) {
                parent::__construct($clientId, $userId);
                $this->mockApi = $mockApi;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return $this->mockApi;
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                return $api->call($interface, $params);
            }
        };

        // 调用方法
        $result = $service->getStandardResult('technology', 1, 0.9);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['std_id']);
    }

    /**
     * 测试带向量的调用
     */
    public function testWithEmbeddings()
    {
        // 创建 Mock InnerApi
        $mockApi = Mockery::mock(InnerApi::class);
        
        $embeddings = [0.1, 0.2, 0.3];
        
        // 设置期望
        $mockApi->shouldReceive('addHeader')->once();
        $mockApi->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'tech',
                'std_type' => 'industry',
                'threshold' => 0.8,
                'vector' => $embeddings
            ])
            ->andReturn(['std_id' => 2]);

        // 创建测试服务
        $service = new class($this->clientId, $this->userId, $mockApi) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function __construct($clientId, $userId, $mockApi) {
                parent::__construct($clientId, $userId);
                $this->mockApi = $mockApi;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return $this->mockApi;
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                return $api->call($interface, $params);
            }
        };

        // 调用方法
        $result = $service->getStandardResult('tech', 1, 0.8, $embeddings);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(2, $result['std_id']);
    }

    /**
     * 测试产品类型
     */
    public function testProductType()
    {
        // 创建 Mock InnerApi
        $mockApi = Mockery::mock(InnerApi::class);
        
        // 设置期望
        $mockApi->shouldReceive('addHeader')->once();
        $mockApi->shouldReceive('call')
            ->once()
            ->with('standardizeContent', [
                'content' => 'software',
                'std_type' => 'product',
                'threshold' => 0.7
            ])
            ->andReturn(['std_id' => 3]);

        // 创建测试服务
        $service = new class($this->clientId, $this->userId, $mockApi) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function __construct($clientId, $userId, $mockApi) {
                parent::__construct($clientId, $userId);
                $this->mockApi = $mockApi;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return $this->mockApi;
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                return $api->call($interface, $params);
            }
        };

        // 调用方法（产品类型 = 2）
        $result = $service->getStandardResult('software', 2, 0.7);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(3, $result['std_id']);
    }

    /**
     * 测试无效内容类型
     */
    public function testInvalidContentType()
    {
        // 创建 Mock InnerApi（不会被调用）
        $mockApi = Mockery::mock(InnerApi::class);

        // 创建测试服务
        $service = new class($this->clientId, $this->userId, $mockApi) extends AiBackgroundCheckService {
            private $mockApi;
            
            public function __construct($clientId, $userId, $mockApi) {
                parent::__construct($clientId, $userId);
                $this->mockApi = $mockApi;
            }
        };

        // 期望抛出异常
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid content type');

        // 调用方法（无效类型 = 99）
        $service->getStandardResult('test', 99);
    }
}
