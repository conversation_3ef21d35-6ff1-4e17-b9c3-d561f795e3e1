<?php

namespace tests\unit\ai_sdr;

use common\library\account\Client;
use common\library\ai_sdr\Constant;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecord;

/**
 * AI SDR 测试数据工厂
 * 
 * 提供创建各种测试数据的便捷方法
 */
class AiSdrTestDataFactory
{
    private static $sequence = 1;

    const TEST_CLIENT_ID = 424159;
    const TEST_USER_ID = *********;

    public static function initClient()
    {
        $client = new Client(self::TEST_CLIENT_ID);
        $clientInfo = self::seedClientData();
        $client->full_name = $clientInfo['full_name'];
        $client->homepage = $clientInfo['homepage'];
        $client->update(['full_name', 'homepage']);
    }

    public static function seedClientData()
    {
        return [
            'client_id' => self::TEST_CLIENT_ID,
            'user_id' => self::TEST_USER_ID,
            'full_name' => '深圳市优可新科技有限公司',
            'homepage' => 'https://www.yocan.com',
        ];
    }
    
    /**
     * 创建任务数据
     */
    public static function createTaskData(array $overrides = []): array
    {
        $defaults = [
            'client_id' => self::TEST_CLIENT_ID,
            'user_id' => self::TEST_USER_ID,
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'current_stage' => Constant::AI_SDR_STAGE_DIG,
            'end_stage' => Constant::AI_SDR_STAGE_MARKETING,
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            'email' => 'test' . self::$sequence . '@example.com',
            'tags' => [1, 2], // bigint数组，使用整数值
            'enable_flag' => 1,
            'stat_total' => 0,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        self::$sequence++;
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建任务详情数据
     */
    public static function createTaskDetailData(int $taskId, array $overrides = []): array
    {
        $defaults = [
            'task_id' => $taskId,
            'lead_id' => rand(1000000, 9999999),
            'user_id' => 888888,
            'source' => Constant::TASK_SOURCE_AI_SDR,
            'stage' => Constant::AI_SDR_STAGE_DIG,
            'status' => Constant::DETAIL_STATUS_ADD,
            'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN,
            'product_ids' => ['Product A', 'Product B'],
            'company_types' => ['Manufacturer'],
            'public_homepage' => ['https://example' . self::$sequence . '.com'],
            'enable_flag' => 1,
            'usage_record_id' => 0,
            'stage_dig_time' => '1970-01-01 00:00:01',
            'stage_reachable_time' => '1970-01-01 00:00:01',
            'stage_marketing_time' => '1970-01-01 00:00:01',
            'stage_effective_time' => '1970-01-01 00:00:01',
            'stage_highvalue_time' => '1970-01-01 00:00:01',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        self::$sequence++;
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建任务记录数据
     */
    public static function createTaskRecordData(int $taskId, int $detailId, array $overrides = []): array
    {
        $defaults = [
            'task_id' => $taskId,
            'detail_id' => $detailId,
            'lead_id' => rand(1000000, 9999999),
            'type' => Constant::RECORD_TYPE_ADD_LEAD,
            'data' => [
                'action' => 'test_action',
                'result' => 'success',
                'timestamp' => time()
            ],
            'estimate_time' => date('Y-m-d H:i:s'),
            'executed_time' => null,
            'refer_type' => 0,
            'refer_id' => 0,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建客户档案数据
     */
    public static function createClientProfileData(array $overrides = []): array
    {
        $defaults = [
            'company_name' => 'Test Client Company ' . self::$sequence,
            'main_products' => ['Product A', 'Product B', 'Product C'],
            'company_description' => 'This is a test client company for AI SDR testing',
            'target_market' => ['US', 'EU', 'Asia'],
            'company_type' => ['Manufacturer', 'Exporter'],
            'employees' => '100-500',
            'annual_revenue' => '$10M-$50M',
            'industry' => 'Technology',
            'website' => 'https://testclient' . self::$sequence . '.com'
        ];
        
        self::$sequence++;
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建买家档案数据
     */
    public static function createBuyerProfileData(array $overrides = []): array
    {
        $defaults = [
            'company_name' => 'Test Buyer Company ' . self::$sequence,
            'main_products' => ['Product X', 'Product Y'],
            'company_type' => ['Distributor', 'Retailer'],
            'public_homepage' => ['https://testbuyer' . self::$sequence . '.com'],
            'country' => 'US',
            'employees_min' => 10,
            'employees_max' => 100,
            'industry' => 'Retail',
            'annual_revenue' => '$1M-$10M',
            'contact_email' => 'contact@testbuyer' . self::$sequence . '.com'
        ];
        
        self::$sequence++;
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建AI质量分析结果数据
     */
    public static function createQualityAnalysisResultData(array $overrides = []): array
    {
        $defaults = [
            'answer' => [
                'lead_quality' => Constant::LEAD_QUALITY_HIGH,
                'confidence' => 0.85,
                'reason' => ['High quality customer', 'Good product match'],
                'score' => 85,
                'analysis' => [
                    'company_size' => 'medium',
                    'market_fit' => 'excellent',
                    'contact_quality' => 'high'
                ]
            ],
            'usage' => [
                'prompt_tokens' => 150,
                'completion_tokens' => 50,
                'total_tokens' => 200
            ],
            'model' => 'gpt-4',
            'created' => time()
        ];
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建背景调研结果数据
     */
    public static function createBackgroundCheckResultData(array $overrides = []): array
    {
        $defaults = [
            'task_id' => 12345,
            'status' => 'completed',
            'report' => [
                'company_name' => 'Test Company',
                'country' => 'US',
                'homepage' => 'https://testcompany.com',
                'employees_min' => 50,
                'employees_max' => 200,
                'main_products' => ['Product A', 'Product B'],
                'company_description' => 'A test company for background check',
                'key_employees' => [
                    [
                        'name' => 'John Doe',
                        'title' => 'CEO',
                        'email' => '<EMAIL>'
                    ]
                ]
            ],
            'confidence' => 0.9,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建营销内容生成结果数据
     */
    public static function createMarketingContentResultData(array $overrides = []): array
    {
        $defaults = [
            [
                'subject' => 'Test Email Subject 1',
                'content' => 'This is test email content for round 1',
                'round' => 1,
                'tone' => 'professional',
                'language' => 'en'
            ],
            [
                'subject' => 'Test Email Subject 2',
                'content' => 'This is test email content for round 2',
                'round' => 2,
                'tone' => 'friendly',
                'language' => 'en'
            ]
        ];
        
        if (!empty($overrides)) {
            // 如果有覆盖数据，合并到第一个邮件内容中
            $defaults[0] = array_merge($defaults[0], $overrides);
        }
        
        return $defaults;
    }
    
    /**
     * 创建推荐API响应数据
     */
    public static function createRecommendApiResponseData(array $overrides = []): array
    {
        $defaults = [
            'example.com' => [
                'company_name' => 'Example Company',
                'main_products' => ['Product 1', 'Product 2'],
                'company_type' => ['Manufacturer'],
                'country' => 'US',
                'employees_min' => 50,
                'employees_max' => 200,
                'homepage' => 'https://example.com',
                'contact_email' => '<EMAIL>'
            ]
        ];
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 创建线索数据
     */
    public static function createLeadData(array $overrides = []): array
    {
        $defaults = [
            'lead_id' => rand(1000000, 9999999),
            'company_name' => 'Test Lead Company ' . self::$sequence,
            'contact_name' => 'Test Contact ' . self::$sequence,
            'email' => 'contact' . self::$sequence . '@testlead.com',
            'phone' => '******-' . str_pad(self::$sequence, 4, '0', STR_PAD_LEFT),
            'website' => 'https://testlead' . self::$sequence . '.com',
            'country' => 'US',
            'industry' => 'Technology',
            'company_size' => '50-200',
            'image_list' => [],
            'company_hash_id' => 'hash_' . md5('test_' . self::$sequence),
            'main_customer_email' => 'main' . self::$sequence . '@testlead.com'
        ];
        
        self::$sequence++;
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * 重置序列号
     */
    public static function resetSequence(): void
    {
        self::$sequence = 1;
    }
    
    /**
     * 获取当前序列号
     */
    public static function getSequence(): int
    {
        return self::$sequence;
    }
    
    /**
     * 批量创建任务数据
     */
    public static function createMultipleTasksData(int $count, array $baseOverrides = []): array
    {
        $tasks = [];
        for ($i = 0; $i < $count; $i++) {
            $overrides = array_merge($baseOverrides, [
                'email' => 'batch_test_' . $i . '@example.com'
            ]);
            $tasks[] = self::createTaskData($overrides);
        }
        return $tasks;
    }
    
    /**
     * 批量创建任务详情数据
     */
    public static function createMultipleTaskDetailsData(int $taskId, int $count, array $baseOverrides = []): array
    {
        $details = [];
        for ($i = 0; $i < $count; $i++) {
            $overrides = array_merge($baseOverrides, [
                'lead_id' => rand(1000000, 9999999)
            ]);
            $details[] = self::createTaskDetailData($taskId, $overrides);
        }
        return $details;
    }
}
