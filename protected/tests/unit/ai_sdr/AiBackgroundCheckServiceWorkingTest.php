<?php

namespace tests\unit\ai_sdr;

use PHPUnit\Framework\TestCase;
use Mockery;
use common\library\api\InnerApi;
use common\library\ai_sdr\AiBackgroundCheckService;
use common\library\ai_sdr\Constant;

/**
 * AiBackgroundCheckService 工作测试类
 * 
 * 使用简单的 Mock 策略，按 module 自定义 call 方法返回结果
 */
class AiBackgroundCheckServiceWorkingTest extends TestCase
{
    private $clientId = 12345;
    private $userId = 67890;
    private $mockResponses = [];

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 设置 Mock 响应数据
     */
    private function setMockResponse(string $module, string $interface, $response): void
    {
        $this->mockResponses["{$module}::{$interface}"] = $response;
    }

    /**
     * 创建可测试的服务实例
     */
    private function createTestService(): AiBackgroundCheckService
    {
        $mockResponses = &$this->mockResponses;
        
        return new class($this->clientId, $this->userId, $mockResponses) extends AiBackgroundCheckService {
            private $mockResponses;
            
            public function __construct($clientId, $userId, &$mockResponses) {
                parent::__construct($clientId, $userId);
                $this->mockResponses = &$mockResponses;
            }
            
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                $mockApi = Mockery::mock(InnerApi::class);
                $mockResponses = &$this->mockResponses;
                
                // 设置基本方法
                $mockApi->shouldReceive('addHeader')->andReturnSelf();
                
                // 设置 call 方法，根据 module 和 interface 返回预设响应
                $mockApi->shouldReceive('call')->andReturnUsing(
                    function ($interface, $params) use ($module, $mockResponses) {
                        $key = "{$module}::{$interface}";
                        
                        if (isset($mockResponses[$key])) {
                            $response = $mockResponses[$key];
                            
                            // 如果是异常，则抛出
                            if ($response instanceof \Exception) {
                                throw $response;
                            }
                            
                            return $response;
                        }
                        
                        // 默认返回空结果
                        return [];
                    }
                );
                
                return $mockApi;
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                try {
                    return $api->call($interface, $params);
                } catch (\Exception $exception) {
                    \LogUtil::info("ai-backgroundCheck API Error: {$exception->getMessage()}", [
                        'client_id' => $this->clientId,
                        'user_id' => $this->userId,
                        'params' => $params,
                        'interface' => $interface,
                    ]);
                    throw new \Exception(\Yii::t("common", "Failed to get data, please try again later"), $exception->getCode());
                }
            }
        };
    }

    /**
     * 测试行业标准化成功场景
     */
    public function testIndustryStandardizationSuccess()
    {
        // 设置成功响应
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 123,
            'std_name' => 'Technology',
            'is_adopted' => true,
            'confidence' => 0.95,
            'original_content' => 'tech',
            'standardized_content' => 'Technology'
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('tech', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(123, $result['std_id']);
        $this->assertEquals('Technology', $result['std_name']);
        $this->assertTrue($result['is_adopted']);
        $this->assertEquals(0.95, $result['confidence']);
    }

    /**
     * 测试产品标准化成功场景
     */
    public function testProductStandardizationSuccess()
    {
        // 设置产品标准化响应
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 456,
            'std_name' => 'Software Development',
            'is_adopted' => true,
            'confidence' => 0.90
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('software dev', 2, 0.7);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(456, $result['std_id']);
        $this->assertEquals('Software Development', $result['std_name']);
        $this->assertTrue($result['is_adopted']);
    }

    /**
     * 测试带向量嵌入的标准化
     */
    public function testStandardizationWithEmbeddings()
    {
        $embeddings = [0.1, 0.2, 0.3, 0.4, 0.5];
        
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 789,
            'std_name' => 'Technology',
            'is_adopted' => true,
            'confidence' => 0.98
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('technology', 1, 0.8, $embeddings);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(789, $result['std_id']);
        $this->assertEquals(0.98, $result['confidence']);
    }

    /**
     * 测试无匹配结果场景
     */
    public function testNoMatchResult()
    {
        // 设置无匹配响应
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 0,
            'std_name' => '',
            'is_adopted' => false,
            'confidence' => 0.2
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('unknown_industry', 1, 0.8);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(0, $result['std_id']);
        $this->assertFalse($result['is_adopted']);
        $this->assertEquals(0.2, $result['confidence']);
    }

    /**
     * 测试空响应场景
     */
    public function testEmptyResponse()
    {
        // 设置空响应
        $this->setMockResponse('ai_background_check', 'standardizeContent', null);

        $service = $this->createTestService();
        $result = $service->getStandardResult('test', 1);

        // 验证返回空数组
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * 测试 API 异常处理
     */
    public function testApiException()
    {
        // 设置异常响应
        $this->setMockResponse('ai_background_check', 'standardizeContent', 
            new \Exception('Service temporarily unavailable', 503));

        $service = $this->createTestService();

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('获取数据失败,请稍后重试');

        $service->getStandardResult('test', 1);
    }

    /**
     * 测试网络超时异常
     */
    public function testTimeoutException()
    {
        // 设置超时异常
        $this->setMockResponse('ai_background_check', 'standardizeContent', 
            new \Exception('Request timeout', 28));

        $service = $this->createTestService();

        // 期望抛出异常
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('获取数据失败,请稍后重试');

        $service->getStandardResult('test', 1);
    }

    /**
     * 测试无效内容类型
     */
    public function testInvalidContentType()
    {
        $service = $this->createTestService();

        // 期望抛出异常
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid content type');

        // 调用方法（无效类型 = 99）
        $service->getStandardResult('test', 99);
    }

    /**
     * 测试默认阈值
     */
    public function testDefaultThreshold()
    {
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 1,
            'threshold_used' => Constant::MIN_THRESHOLD
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('test', 1); // 不指定阈值

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['std_id']);
    }

    /**
     * 测试内容类型映射
     */
    public function testContentTypeMapping()
    {
        // 测试行业类型映射
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_type' => 'industry',
            'input_type' => 1
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('tech', 1);
        $this->assertEquals('industry', $result['std_type']);

        // 重置响应，测试产品类型映射
        $this->mockResponses = [];
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'std_type' => 'product',
            'input_type' => 2
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('software', 2);
        $this->assertEquals('product', $result['std_type']);
    }

    /**
     * 测试多个不同模块的响应
     */
    public function testMultipleModuleResponses()
    {
        // 设置不同模块的响应
        $this->setMockResponse('ai_background_check', 'standardizeContent', [
            'module' => 'ai_background_check',
            'result' => 'success'
        ]);

        $this->setMockResponse('other_module', 'otherInterface', [
            'module' => 'other_module',
            'result' => 'other_success'
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('test', 1);

        // 验证只返回对应模块的响应
        $this->assertEquals('ai_background_check', $result['module']);
        $this->assertEquals('success', $result['result']);
    }

    /**
     * 测试复杂的响应数据结构
     */
    public function testComplexResponseStructure()
    {
        $complexResponse = [
            'std_id' => 999,
            'std_name' => 'Advanced Technology',
            'is_adopted' => true,
            'confidence' => 0.99,
            'metadata' => [
                'source' => 'ai_analysis',
                'version' => '2.0',
                'tags' => ['tech', 'innovation', 'ai']
            ],
            'alternatives' => [
                ['id' => 1000, 'name' => 'Tech Innovation', 'confidence' => 0.85],
                ['id' => 1001, 'name' => 'Digital Technology', 'confidence' => 0.80]
            ]
        ];

        $this->setMockResponse('ai_background_check', 'standardizeContent', $complexResponse);

        $service = $this->createTestService();
        $result = $service->getStandardResult('advanced tech', 1);

        // 验证复杂结构
        $this->assertEquals(999, $result['std_id']);
        $this->assertEquals('Advanced Technology', $result['std_name']);
        $this->assertArrayHasKey('metadata', $result);
        $this->assertArrayHasKey('alternatives', $result);
        $this->assertEquals('ai_analysis', $result['metadata']['source']);
        $this->assertCount(2, $result['alternatives']);
    }
}
