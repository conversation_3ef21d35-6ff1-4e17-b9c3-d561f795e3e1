<?php

namespace tests\unit\ai_sdr;

use PHPUnit\Framework\TestCase;
use tests\mockery\inner_api\InnerApiMockHelper;
use common\library\api\InnerApi;
use common\library\ai_sdr\AiBackgroundCheckService;

/**
 * 使用 InnerApiMockHelper 的 AiBackgroundCheckService 测试
 * 
 * 演示如何使用 InnerApiMockHelper 进行简单的 Mock 测试
 */
class AiBackgroundCheckServiceHelperTest extends TestCase
{
    private $clientId = 12345;
    private $userId = 67890;

    protected function setUp(): void
    {
        parent::setUp();
        InnerApiMockHelper::reset();
    }

    protected function tearDown(): void
    {
        InnerApiMockHelper::clearAll();
        \Mockery::close();
        parent::tearDown();
    }

    /**
     * 创建可测试的服务实例
     */
    private function createTestService(): AiBackgroundCheckService
    {
        return new class($this->clientId, $this->userId) extends AiBackgroundCheckService {
            protected function innerApiFactory(string $module, string $method = InnerApi::HTTP_METHOD_POST_JSON): InnerApi
            {
                return InnerApiMockHelper::createMockInnerApi($module);
            }
            
            protected function innerCall(InnerApi $api, string $interface, $params, string $cacheKey = null, callable $processResult = null)
            {
                $api->addHeader(["cookie:clientId={$this->clientId};userId={$this->userId}"]);
                try {
                    return $api->call($interface, $params);
                } catch (\Exception $exception) {
                    throw new \Exception(\Yii::t("common", "Failed to get data, please try again later"), $exception->getCode());
                }
            }
        };
    }

    /**
     * 测试使用便捷方法设置成功响应
     */
    public function testStandardizeContentSuccessWithHelper()
    {
        // 使用便捷方法设置响应
        InnerApiMockHelper::setStandardizeContentSuccess(456, 'Software Development', true, 0.92);

        $service = $this->createTestService();
        $result = $service->getStandardResult('software', 2, 0.8);

        // 验证结果
        $this->assertEquals(456, $result['std_id']);
        $this->assertEquals('Software Development', $result['std_name']);
        $this->assertTrue($result['is_adopted']);
        $this->assertEquals(0.92, $result['confidence']);

        // 使用辅助工具验证调用
        InnerApiMockHelper::assertApiWasCalled('ai_background_check', 'standardizeContent');
        InnerApiMockHelper::assertApiCallCount('ai_background_check', 'standardizeContent', 1);
    }

    /**
     * 测试无匹配场景
     */
    public function testStandardizeContentNoMatchWithHelper()
    {
        // 设置无匹配响应
        InnerApiMockHelper::setStandardizeContentNoMatch(0.15);

        $service = $this->createTestService();
        $result = $service->getStandardResult('unknown', 1);

        // 验证结果
        $this->assertEquals(0, $result['std_id']);
        $this->assertFalse($result['is_adopted']);
        $this->assertEquals(0.15, $result['confidence']);
    }

    /**
     * 测试超时异常
     */
    public function testTimeoutExceptionWithHelper()
    {
        // 设置超时响应
        InnerApiMockHelper::setTimeoutResponse('ai_background_check', 'standardizeContent');

        $service = $this->createTestService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('获取数据失败,请稍后重试');

        $service->getStandardResult('test', 1);
    }

    /**
     * 测试服务不可用异常
     */
    public function testServiceUnavailableWithHelper()
    {
        // 设置服务不可用响应
        InnerApiMockHelper::setServiceUnavailableResponse('ai_background_check', 'standardizeContent');

        $service = $this->createTestService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('获取数据失败,请稍后重试');

        $service->getStandardResult('test', 1);
    }

    /**
     * 测试自定义响应
     */
    public function testCustomResponseWithHelper()
    {
        // 设置自定义响应
        InnerApiMockHelper::setMockResponse('ai_background_check', 'standardizeContent', [
            'std_id' => 999,
            'std_name' => 'Custom Technology',
            'is_adopted' => true,
            'confidence' => 0.99,
            'custom_field' => 'custom_value',
            'metadata' => [
                'source' => 'custom_analysis',
                'tags' => ['custom', 'test']
            ]
        ]);

        $service = $this->createTestService();
        $result = $service->getStandardResult('custom tech', 1);

        // 验证自定义字段
        $this->assertEquals(999, $result['std_id']);
        $this->assertEquals('Custom Technology', $result['std_name']);
        $this->assertEquals('custom_value', $result['custom_field']);
        $this->assertArrayHasKey('metadata', $result);
        $this->assertEquals('custom_analysis', $result['metadata']['source']);
    }

    /**
     * 测试多次调用和统计
     */
    public function testMultipleCallsAndStatistics()
    {
        // 设置响应
        InnerApiMockHelper::setStandardizeContentSuccess();

        $service = $this->createTestService();

        // 多次调用
        $service->getStandardResult('tech1', 1);
        $service->getStandardResult('tech2', 1);
        $service->getStandardResult('product1', 2);

        // 验证调用次数
        InnerApiMockHelper::assertApiCallCount('ai_background_check', 'standardizeContent', 3);

        // 获取统计信息
        $statistics = InnerApiMockHelper::getCallStatistics();
        $this->assertEquals(3, $statistics['ai_background_check::standardizeContent']);

        // 获取调用日志
        $callLog = InnerApiMockHelper::getCallLog();
        $this->assertCount(3, $callLog);

        // 验证最后一次调用参数
        $lastParams = InnerApiMockHelper::getLastApiCallParams('ai_background_check', 'standardizeContent');
        $this->assertEquals('product1', $lastParams['content']);
        $this->assertEquals('product', $lastParams['std_type']);
    }

    /**
     * 测试 AI 背景调研成功场景
     */
    public function testAiBackgroundCheckSuccessWithHelper()
    {
        // 设置 AI 背景调研成功响应
        InnerApiMockHelper::setAiBackgroundCheckSuccess('bg_test_001', [
            'company_name' => 'Helper Test Company',
            'employees_min' => 50,
            'employees_max' => 200
        ]);

        // 注意：这里我们需要创建一个能调用 aiBackgroundCheck 接口的服务
        // 由于 AiBackgroundCheckService 只有 standardizeContent 方法，
        // 这里只是演示如何设置响应，实际使用时需要相应的服务方法

        // 验证响应已设置
        $this->assertTrue(true); // 占位断言
    }

    /**
     * 测试清理功能
     */
    public function testClearFunctionality()
    {
        // 设置一些响应和调用
        InnerApiMockHelper::setStandardizeContentSuccess();
        
        $service = $this->createTestService();
        $service->getStandardResult('test', 1);

        // 验证有数据
        $this->assertCount(1, InnerApiMockHelper::getCallLog());
        $this->assertTrue(InnerApiMockHelper::wasApiCalled('ai_background_check', 'standardizeContent'));

        // 清理调用日志
        InnerApiMockHelper::clearCallLog();
        $this->assertCount(0, InnerApiMockHelper::getCallLog());
        $this->assertFalse(InnerApiMockHelper::wasApiCalled('ai_background_check', 'standardizeContent'));

        // 重新调用
        $service->getStandardResult('test2', 1);
        $this->assertCount(1, InnerApiMockHelper::getCallLog());

        // 完全清理
        InnerApiMockHelper::clearAll();
        $this->assertCount(0, InnerApiMockHelper::getCallLog());
    }

    /**
     * 测试错误响应
     */
    public function testErrorResponseWithHelper()
    {
        // 设置自定义错误响应
        InnerApiMockHelper::setErrorResponse(
            'ai_background_check', 
            'standardizeContent', 
            'Custom API Error', 
            500
        );

        $service = $this->createTestService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('获取数据失败,请稍后重试');

        $service->getStandardResult('test', 1);
    }

    /**
     * 测试多模块支持
     */
    public function testMultipleModuleSupport()
    {
        // 设置不同模块的响应
        InnerApiMockHelper::setMockResponse('module1', 'interface1', ['result' => 'module1_response']);
        InnerApiMockHelper::setMockResponse('module2', 'interface2', ['result' => 'module2_response']);
        InnerApiMockHelper::setMockResponse('ai_background_check', 'standardizeContent', ['std_id' => 123]);

        // 创建不同模块的 Mock API
        $mockApi1 = InnerApiMockHelper::createMockInnerApi('module1');
        $mockApi2 = InnerApiMockHelper::createMockInnerApi('module2');
        $mockApi3 = InnerApiMockHelper::createMockInnerApi('ai_background_check');

        // 模拟调用
        $result1 = $mockApi1->call('interface1', []);
        $result2 = $mockApi2->call('interface2', []);
        $result3 = $mockApi3->call('standardizeContent', []);

        // 验证结果
        $this->assertEquals('module1_response', $result1['result']);
        $this->assertEquals('module2_response', $result2['result']);
        $this->assertEquals(123, $result3['std_id']);

        // 验证调用统计
        $statistics = InnerApiMockHelper::getCallStatistics();
        $this->assertEquals(1, $statistics['module1::interface1']);
        $this->assertEquals(1, $statistics['module2::interface2']);
        $this->assertEquals(1, $statistics['ai_background_check::standardizeContent']);
    }
}
