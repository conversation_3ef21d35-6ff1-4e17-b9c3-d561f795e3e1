<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/3/27
 * Time: 下午5:18
 */


class UserInfoFormatterTest extends ProjectTestCase
{

    public function testProperties()
    {
        $f = new \common\library\account\UserInfoFormatter();
        $oldAttr = $f->getProperties();
        var_dump($oldAttr);
        $f->setProperties($oldAttr);
        $newAttr = $f->getProperties();
        $this->assertArraySubset($oldAttr, $newAttr);
        var_dump($f->getProperties());
    }

}