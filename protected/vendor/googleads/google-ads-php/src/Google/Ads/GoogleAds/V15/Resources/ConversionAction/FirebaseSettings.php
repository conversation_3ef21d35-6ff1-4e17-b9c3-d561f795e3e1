<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/resources/conversion_action.proto

namespace Google\Ads\GoogleAds\V15\Resources\ConversionAction;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Settings related to a Firebase conversion action.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.resources.ConversionAction.FirebaseSettings</code>
 */
class FirebaseSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Output only. The event name of a Firebase conversion.
     *
     * Generated from protobuf field <code>optional string event_name = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $event_name = null;
    /**
     * Output only. The Firebase project ID of the conversion.
     *
     * Generated from protobuf field <code>optional string project_id = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $project_id = null;
    /**
     * Output only. The GA property ID of the conversion.
     *
     * Generated from protobuf field <code>int64 property_id = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $property_id = 0;
    /**
     * Output only. The GA property name of the conversion.
     *
     * Generated from protobuf field <code>string property_name = 6 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $property_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $event_name
     *           Output only. The event name of a Firebase conversion.
     *     @type string $project_id
     *           Output only. The Firebase project ID of the conversion.
     *     @type int|string $property_id
     *           Output only. The GA property ID of the conversion.
     *     @type string $property_name
     *           Output only. The GA property name of the conversion.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Resources\ConversionAction::initOnce();
        parent::__construct($data);
    }

    /**
     * Output only. The event name of a Firebase conversion.
     *
     * Generated from protobuf field <code>optional string event_name = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getEventName()
    {
        return isset($this->event_name) ? $this->event_name : '';
    }

    public function hasEventName()
    {
        return isset($this->event_name);
    }

    public function clearEventName()
    {
        unset($this->event_name);
    }

    /**
     * Output only. The event name of a Firebase conversion.
     *
     * Generated from protobuf field <code>optional string event_name = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setEventName($var)
    {
        GPBUtil::checkString($var, True);
        $this->event_name = $var;

        return $this;
    }

    /**
     * Output only. The Firebase project ID of the conversion.
     *
     * Generated from protobuf field <code>optional string project_id = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getProjectId()
    {
        return isset($this->project_id) ? $this->project_id : '';
    }

    public function hasProjectId()
    {
        return isset($this->project_id);
    }

    public function clearProjectId()
    {
        unset($this->project_id);
    }

    /**
     * Output only. The Firebase project ID of the conversion.
     *
     * Generated from protobuf field <code>optional string project_id = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setProjectId($var)
    {
        GPBUtil::checkString($var, True);
        $this->project_id = $var;

        return $this;
    }

    /**
     * Output only. The GA property ID of the conversion.
     *
     * Generated from protobuf field <code>int64 property_id = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getPropertyId()
    {
        return $this->property_id;
    }

    /**
     * Output only. The GA property ID of the conversion.
     *
     * Generated from protobuf field <code>int64 property_id = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setPropertyId($var)
    {
        GPBUtil::checkInt64($var);
        $this->property_id = $var;

        return $this;
    }

    /**
     * Output only. The GA property name of the conversion.
     *
     * Generated from protobuf field <code>string property_name = 6 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getPropertyName()
    {
        return $this->property_name;
    }

    /**
     * Output only. The GA property name of the conversion.
     *
     * Generated from protobuf field <code>string property_name = 6 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setPropertyName($var)
    {
        GPBUtil::checkString($var, True);
        $this->property_name = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(FirebaseSettings::class, \Google\Ads\GoogleAds\V15\Resources\ConversionAction_FirebaseSettings::class);

