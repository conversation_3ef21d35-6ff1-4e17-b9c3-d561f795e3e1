<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/asset_group_primary_status.proto

namespace Google\Ads\GoogleAds\V16\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible asset group primary status.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.enums.AssetGroupPrimaryStatusEnum</code>
 */
class AssetGroupPrimaryStatusEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Enums\AssetGroupPrimaryStatus::initOnce();
        parent::__construct($data);
    }

}

