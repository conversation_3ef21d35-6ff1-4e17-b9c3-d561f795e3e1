<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/ad_group_primary_status.proto

namespace Google\Ads\GoogleAds\V17\Enums\AdGroupPrimaryStatusEnum;

use UnexpectedValueException;

/**
 * The possible statuses of an AdGroup.
 *
 * Protobuf type <code>google.ads.googleads.v17.enums.AdGroupPrimaryStatusEnum.AdGroupPrimaryStatus</code>
 */
class AdGroupPrimaryStatus
{
    /**
     * Not specified.
     *
     * Generated from protobuf enum <code>UNSPECIFIED = 0;</code>
     */
    const UNSPECIFIED = 0;
    /**
     * Used for return value only. Represents value unknown in this version.
     *
     * Generated from protobuf enum <code>UNKNOWN = 1;</code>
     */
    const UNKNOWN = 1;
    /**
     * The ad group is eligible to serve.
     *
     * Generated from protobuf enum <code>ELIGIBLE = 2;</code>
     */
    const ELIGIBLE = 2;
    /**
     * The ad group is paused.
     *
     * Generated from protobuf enum <code>PAUSED = 3;</code>
     */
    const PAUSED = 3;
    /**
     * The ad group is removed.
     *
     * Generated from protobuf enum <code>REMOVED = 4;</code>
     */
    const REMOVED = 4;
    /**
     * The ad group may serve in the future.
     *
     * Generated from protobuf enum <code>PENDING = 5;</code>
     */
    const PENDING = 5;
    /**
     * The ad group is not eligible to serve.
     *
     * Generated from protobuf enum <code>NOT_ELIGIBLE = 6;</code>
     */
    const NOT_ELIGIBLE = 6;
    /**
     * The ad group has limited servability.
     *
     * Generated from protobuf enum <code>LIMITED = 7;</code>
     */
    const LIMITED = 7;

    private static $valueToName = [
        self::UNSPECIFIED => 'UNSPECIFIED',
        self::UNKNOWN => 'UNKNOWN',
        self::ELIGIBLE => 'ELIGIBLE',
        self::PAUSED => 'PAUSED',
        self::REMOVED => 'REMOVED',
        self::PENDING => 'PENDING',
        self::NOT_ELIGIBLE => 'NOT_ELIGIBLE',
        self::LIMITED => 'LIMITED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AdGroupPrimaryStatus::class, \Google\Ads\GoogleAds\V17\Enums\AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus::class);

