<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/errors/media_bundle_error.proto

namespace Google\Ads\GoogleAds\V15\Errors;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible media bundle errors.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.errors.MediaBundleErrorEnum</code>
 */
class MediaBundleErrorEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Errors\MediaBundleError::initOnce();
        parent::__construct($data);
    }

}

