<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/offline_user_data_job_match_rate_range.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Enums;

class OfflineUserDataJobMatchRateRange
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Kgoogle/ads/googleads/v16/enums/offline_user_data_job_match_rate_range.protogoogle.ads.googleads.v16.enums"�
$OfflineUserDataJobMatchRateRangeEnum"�
 OfflineUserDataJobMatchRateRange
UNSPECIFIED 
UNKNOWN
MATCH_RANGE_LESS_THAN_20
MATCH_RANGE_20_TO_30
MATCH_RANGE_31_TO_40
MATCH_RANGE_41_TO_50
MATCH_RANGE_51_TO_60
MATCH_RANGE_61_TO_70
MATCH_RANGE_71_TO_80
MATCH_RANGE_81_TO_90	
MATCH_RANGE_91_TO_100
B�
"com.google.ads.googleads.v16.enumsB%OfflineUserDataJobMatchRateRangeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

