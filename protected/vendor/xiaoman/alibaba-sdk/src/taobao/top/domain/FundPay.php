<?php
namespace xiaoman\AlibabaSdk\taobao\top\domain;

/**
 * 支付信息列表(包含预付款和尾款)
 * <AUTHOR> create
 */
class FundPay
{

    /**
     * 支付金额
     **/
    public $pay_amount;

    /**
     * 支付方式(TT, CREDIT_CARD, E_CHECKING, OTHER, LC, BOLETO;)
     **/
    public $pay_method;

    /**
     * 支付状态:<br /> UNPAY //未支付<br /> PAYING,    //买家支付中，主要是包含验证的过程<br /> PAID,      //买家支付完成，对应批发是支付成功<br /> CAPTURED,  //请款完成，到这步就代表资金会到账，对应批发是审核成功<br /> RELATING,  //关联中，挂帐就会进入此状态<br /> FULFILLED, //已完成，代表这个支付单已经收齐（自动收齐或者手动收齐）<br /> FAILED,    //支付失败，可以重新进行支付<br /> CANCELED,  //支付撤销，可以重新进行支付 <br />     CLOSED;    //支付单被关闭，终态)
     **/
    public $pay_status;

    /**
     * 支付阶段(ADVANCE//预付款, BALANCE//尾款)
     **/
    public $pay_step;

    /**
     * 支付时间
     **/
    public $pay_time;

    /**
     * 到账金额
     **/
    public $receive_amount;

    /**
     * 到账时间
     **/
    public $receive_time;

    /**
     * 应付金额
     **/
    public $should_pay_amount;
}

?>
