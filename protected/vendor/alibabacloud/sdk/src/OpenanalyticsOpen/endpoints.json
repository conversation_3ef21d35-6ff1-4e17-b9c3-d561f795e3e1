{"endpoint_map": {"cn-beijing": "openanalytics.cn-beijing.aliyuncs.com", "cn-zhangjiakou": "openanalytics.cn-zhangjiakou.aliyuncs.com", "cn-hangzhou": "openanalytics.cn-hangzhou.aliyuncs.com", "cn-shanghai": "openanalytics.cn-shanghai.aliyuncs.com", "cn-shenzhen": "openanalytics.cn-shenzhen.aliyuncs.com", "cn-hongkong": "openanalytics.cn-hongkong.aliyuncs.com", "ap-southeast-1": "openanalytics.ap-southeast-1.aliyuncs.com", "ap-southeast-2": "datalakeanalytics.ap-southeast-2.aliyuncs.com", "ap-southeast-3": "openanalytics.ap-southeast-3.aliyuncs.com", "ap-northeast-1": "datalakeanalytics.ap-northeast-1.aliyuncs.com", "eu-west-1": "openanalytics.eu-west-1.aliyuncs.com", "us-west-1": "openanalytics.us-west-1.aliyuncs.com", "us-east-1": "datalakeanalytics.us-east-1.aliyuncs.com", "eu-central-1": "datalakeanalytics.eu-central-1.aliyuncs.com", "ap-south-1": "openanalytics.ap-south-1.aliyuncs.com", "ap-northeast-2-pop": "openanalytics.ap-northeast-1.aliyuncs.com", "ap-southeast-5": "openanalytics.ap-southeast-5.aliyuncs.com", "cn-beijing-finance-1": "openanalytics.aliyuncs.com", "cn-beijing-finance-pop": "openanalytics.aliyuncs.com", "cn-beijing-gov-1": "openanalytics.aliyuncs.com", "cn-beijing-nu16-b01": "openanalytics.aliyuncs.com", "cn-chengdu": "openanalytics.aliyuncs.com", "cn-edge-1": "openanalytics.aliyuncs.com", "cn-fujian": "openanalytics.aliyuncs.com", "cn-haidian-cm12-c01": "openanalytics.aliyuncs.com", "cn-hangzhou-bj-b01": "openanalytics.aliyuncs.com", "cn-hangzhou-finance": "openanalytics.aliyuncs.com", "cn-hangzhou-internal-prod-1": "openanalytics.aliyuncs.com", "cn-hangzhou-internal-test-1": "openanalytics.aliyuncs.com", "cn-hangzhou-internal-test-2": "openanalytics.aliyuncs.com", "cn-hangzhou-internal-test-3": "openanalytics.aliyuncs.com", "cn-hangzhou-test-306": "openanalytics.aliyuncs.com", "cn-hongkong-finance-pop": "openanalytics.aliyuncs.com", "cn-huhehaote": "openanalytics.cn-huhehaote.aliyuncs.com", "cn-north-2-gov-1": "openanalytics.aliyuncs.com", "cn-qingdao": "openanalytics.cn-qingdao.aliyuncs.com", "cn-qingdao-nebula": "openanalytics.aliyuncs.com", "cn-shanghai-et15-b01": "openanalytics.aliyuncs.com", "cn-shanghai-et2-b01": "openanalytics.aliyuncs.com", "cn-shanghai-finance-1": "openanalytics.aliyuncs.com", "cn-shanghai-inner": "openanalytics.aliyuncs.com", "cn-shanghai-internal-test-1": "openanalytics.aliyuncs.com", "cn-shenzhen-finance-1": "openanalytics.aliyuncs.com", "cn-shenzhen-inner": "openanalytics.aliyuncs.com", "cn-shenzhen-st4-d01": "openanalytics.aliyuncs.com", "cn-shenzhen-su18-b01": "openanalytics.aliyuncs.com", "cn-wuhan": "openanalytics.aliyuncs.com", "cn-yushanfang": "openanalytics.aliyuncs.com", "cn-zhangbei-na61-b01": "openanalytics.aliyuncs.com", "cn-zhangjiakou-na62-a01": "openanalytics.aliyuncs.com", "cn-zhengzhou-nebula-1": "openanalytics.aliyuncs.com", "eu-west-1-oxs": "openanalytics.ap-northeast-1.aliyuncs.com", "me-east-1": "openanalytics.me-east-1.aliyuncs.com", "rus-west-1-pop": "openanalytics.ap-northeast-1.aliyuncs.com"}, "endpoint_regional": "central", "standard": [], "regions": ["cn-beijing", "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "cn-hangzhou", "cn-shanghai", "cn-shenzhen", "cn-hongkong", "ap-southeast-1", "ap-southeast-2", "ap-southeast-3", "ap-northeast-1", "eu-west-1", "us-west-1", "us-east-1", "eu-central-1", "ap-south-1", "ap-northeast-2-pop", "ap-southeast-5", "cn-beijing-finance-1", "cn-beijing-finance-pop", "cn-beijing-gov-1", "cn-beijing-nu16-b01", "cn-chengdu", "cn-edge-1", "cn-fujian", "cn-haidian-cm12-c01", "cn-hangzhou-bj-b01", "cn-hangzhou-finance", "cn-hangzhou-internal-prod-1", "cn-hangzhou-internal-test-1", "cn-hangzhou-internal-test-2", "cn-hangzhou-internal-test-3", "cn-hangzhou-test-306", "cn-hongkong-finance-pop", "cn-huh<PERSON><PERSON><PERSON>", "cn-north-2-gov-1", "cn-qingdao", "cn-qingdao-nebula", "cn-shanghai-et15-b01", "cn-shanghai-et2-b01", "cn-shanghai-finance-1", "cn-shanghai-inner", "cn-shanghai-internal-test-1", "cn-shenzhen-finance-1", "cn-shenzhen-inner", "cn-shenzhen-st4-d01", "cn-shenzhen-su18-b01", "cn-wuhan", "cn-yush<PERSON>fang", "cn-zhangbei-na61-b01", "cn-zhangjiakou-na62-a01", "cn-zhengzhou-nebula-1", "eu-west-1-oxs", "me-east-1", "rus-west-1-pop"], "endpoint_health": []}