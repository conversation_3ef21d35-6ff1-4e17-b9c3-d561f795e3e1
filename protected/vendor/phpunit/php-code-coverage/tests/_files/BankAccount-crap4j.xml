<?xml version="1.0" encoding="UTF-8"?>
<crap_result>
  <project>BankAccount</project>
  <timestamp>%s</timestamp>
  <stats>
    <name>Method Crap Stats</name>
    <methodCount>4</methodCount>
    <crapMethodCount>0</crapMethodCount>
    <crapLoad>0</crapLoad>
    <totalCrap>9</totalCrap>
    <crapMethodPercent>0</crapMethodPercent>
  </stats>
  <methods>
    <method>
      <package>global</package>
      <className>BankAccount</className>
      <methodName>getBalance</methodName>
      <methodSignature>getBalance()</methodSignature>
      <fullMethod>getBalance()</fullMethod>
      <crap>1</crap>
      <complexity>1</complexity>
      <coverage>100</coverage>
      <crapLoad>0</crapLoad>
    </method>
    <method>
      <package>global</package>
      <className>BankAccount</className>
      <methodName>setBalance</methodName>
      <methodSignature>setBalance($balance)</methodSignature>
      <fullMethod>setBalance($balance)</fullMethod>
      <crap>6</crap>
      <complexity>2</complexity>
      <coverage>0</coverage>
      <crapLoad>0</crapLoad>
    </method>
    <method>
      <package>global</package>
      <className>BankAccount</className>
      <methodName>depositMoney</methodName>
      <methodSignature>depositMoney($balance)</methodSignature>
      <fullMethod>depositMoney($balance)</fullMethod>
      <crap>1</crap>
      <complexity>1</complexity>
      <coverage>100</coverage>
      <crapLoad>0</crapLoad>
    </method>
    <method>
      <package>global</package>
      <className>BankAccount</className>
      <methodName>withdrawMoney</methodName>
      <methodSignature>withdrawMoney($balance)</methodSignature>
      <fullMethod>withdrawMoney($balance)</fullMethod>
      <crap>1</crap>
      <complexity>1</complexity>
      <coverage>100</coverage>
      <crapLoad>0</crapLoad>
    </method>
  </methods>
</crap_result>
