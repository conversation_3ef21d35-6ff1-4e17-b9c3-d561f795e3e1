{"name": "hyperf/utils", "description": "A tools package that could help developer solved the problem quickly.", "license": "MIT", "keywords": ["php", "swoole", "hyperf", "utils"], "homepage": "https://hyperf.io", "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "require": {"php": ">=7.2", "doctrine/inflector": "^2.0", "hyperf/contract": "~2.1.0", "hyperf/engine": "^1.1"}, "require-dev": {"symfony/var-dumper": "^5.0", "symfony/property-access": "^5.0", "symfony/serializer": "^5.0", "malukenho/docheader": "^0.1.6", "mockery/mockery": "^1.0", "phpunit/phpunit": "^9.4", "friendsofphp/php-cs-fixer": "^2.9"}, "suggest": {"ext-swoole": "Required to use methods related to swoole (>=4.5).", "symfony/var-dumper": "Required to use the dd function (^5.0).", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0)", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0)", "hyperf/di": "Required to use ExceptionNormalizer"}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Utils\\": "src/"}}, "autoload-dev": {"psr-4": {"HyperfTest\\Utils\\": "tests/"}}, "config": {"sort-packages": true}, "extra": {"hyperf": {"config": "Hyperf\\Utils\\ConfigProvider"}, "branch-alias": {"dev-master": "2.1-dev"}}, "bin": [], "scripts": {"cs-fix": "php-cs-fixer fix $1", "test": "phpunit --colors=always"}}