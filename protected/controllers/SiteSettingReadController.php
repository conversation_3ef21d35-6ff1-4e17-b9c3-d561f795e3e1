<?php

class SiteSettingReadController extends Controller
{
    /**
     * 获取设置的询盘通知emails
     * @param $site_id
     * @param $key
     */
    public function actionNoticeEmailInfo( $key,$site_id = null)
    {
        if(empty($site_id)) {
            $site_id = 0;
        }
        $this->validate(
            [
                'site_id' => 'numeric',
                'key' => 'required|string',
            ]
        );

        if(!in_array($key, array_keys(\common\library\cms\setting\PageSettingService::KEY_PROCESS_MAP))) {
            throw new \RuntimeException(\Yii::t('common','Unsupported type'));
        }

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $data = \common\library\cms\setting\PageSettingService::getAllEmailNotices($clientId, $key);

        $this->success($data);
    }
}