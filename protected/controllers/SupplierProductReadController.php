<?php

use common\library\APIConstant;
use common\library\custom_field\CustomFieldReadActions;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\supplier\product\export\SupplierProductExportExecutor;
use common\library\supplier\product\SupplierProduct;
use common\library\supplier\product\SupplierProductApi;
use common\library\supplier\product\SupplierProductFilter;
use xiaoman\orm\database\data\Equal;
use xiaoman\orm\database\data\In;
use common\library\export_v2\ExportReadAction;
use common\library\import\ImportReadActions;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/3/19
 * Time: 4:50 PM
 */

class SupplierProductReadController extends Controller
{
    use CustomFieldReadActions;
    use ExportReadAction;
    use ImportReadActions;


    public function initImportRead()
    {
        $this->importType = Constants::TYPE_SUPPLIER_PRODUCT;
    }

    public function initExportRead()
    {
        $this->exportType = \Constants::TYPE_SUPPLIER_PRODUCT;
        $this->exportScene = \Constants::TYPE_SUPPLIER_PRODUCT;
        $this->exportPrivilege = PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_PRODUCT_EXPORT;
        $this->exportExecutor = SupplierProductExportExecutor::class;
    }

    protected function beforeAction($action)
    {
        $this->initCustomFieldService(Constants::TYPE_SUPPLIER_PRODUCT);
        return parent::beforeAction($action);
    }

    public function actionExportFieldList()
    {
        $user = User::getLoginUser();
        $api  = new SupplierProductApi($user->getClientId(), $user->getUserId());
        $data = $api->exportFieldList();
        return $this->success($data);
    }

    public function actionInfo($relation_id, $skip_privilege = 0)
    {

        $supplierProduct = new SupplierProduct($this->getLoginUserClientId(), $relation_id);
        if(!$supplierProduct->hasViewAccess($skip_privilege)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_SUPPLIER_NOT_PRIVILEGE_VIEW);
        }
        $supplierProduct->getFormatter()->infoSetting();
        $data = $supplierProduct->getAttributes();
        return $this->success($data);
    }

    public function actionProductList(
        $supplier_id = null,
        $supplier_no = null,
        $supplier_name = null,
        $supplier_keyword = null,
        array $users = [],
        $product_no_keyword = null,
        $product_name_keyword = null,
        $product_cn_name_keyword = null,
        $product_model_keyword = null,
        $product_attributes = null,
        $supplier_product_no = null,
        $supplier_product_name = null,
        array $product_id = [],
        array $sku_id = [],
        array $relation_id = [],
        $update_time_start = '',
        $update_time_end = '',
        $expire_date_start = '',
        $expire_date_end = '',
        array $expire_status = [],
        $show_relation_sku_id = false,
        $import_id = null,
        $page = 1,
        $page_size = 10,
        $sort_field = 'create_time',
        $sort_type = 'desc',
        $scene = APIConstant::SCENE_LIST
    )
    {
        $this->validate([
            'product_id' => 'array',
            'product_id.*' => 'numeric|not_empty',
            'sku_id' => 'array',
            'sku_id.*' => 'numeric|not_empty',
            'page' => 'integer',
            'page_size' => 'integer',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:asc,desc',
        ]);

        $user = User::getLoginUser();

        if (!Helper::hasPermission($user->getClientId(), $user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), \ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
        $params['user_id'] = [$user->getUserId()];

        $data = SupplierProductApi::productList($user->getUserId(), $params);
        return $this->success($data);
    }

    //show_relation_sku_id 存在supplier_id情况下前端刷新过滤使用
    public function actionSupplierList(
        $product_id,
        array $sku_id = [],
        array $supplier_id = [],
        $product_no_keyword = '',
        $show_relation_sku_id = false,
        $show_relation_supplier_id = false,
        $page = 1,
        $page_size = 10,
        $sort_field = 'create_time',
        $sort_type = 'desc',
        $scene = 'supplier_list'
    ){
        $this->validate([
            'sku_id' => 'array',
            'sku_id.*' => 'numeric|not_empty',
            'supplier_id' => 'array',
            'supplier_id.*' => 'numeric|not_empty',
            'page' => 'integer|min:1',
            'page_size' => 'integer:min:1',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:asc,desc',
        ]);

        $user = User::getLoginUser();

        if(!Helper::hasPermission($user->getClientId(),$user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW))
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter) {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
        $data = SupplierProductApi::productList($user->getUserId(), $params);

        return $this->success($data);
//        if (!empty($product_no_keyword))
//        {
//            $searchSkuIds = (new \common\library\product_v2\sku\SkuAPI($user->getClientId()))->search(['product_no_keyword' => $product_no_keyword]);
//            //前端指定sku下搜索
//            if (!empty($sku_id))
//            {
//                $sku_id = array_intersect($sku_id, $searchSkuIds);
//            } else {
//                $sku_id = $searchSkuIds;
//            }
//
//            if (empty($sku_id))
//                return $this->success(['list' => [], 'count' => 0]);
//        }
//
//        $filter = new SupplierProductFilter($user->getClientId());
//        $filter->product_id = $product_id;
//        $filter->enable_flag = 1;
//        if (!empty($sku_id))
//            $filter->sku_id = new In($sku_id);
//        if (!empty($supplier_id))
//            $filter->supplier_id = new In($supplier_id);
//        $filter->order($sort_field,$sort_type);
//        $filter->order('relation_id');
//        $filter->limit($page_size, $page);
//        $productList = $filter->find();
//        $productList->getFormatter()->supplierListSetting();
//        $data = $productList->getListAttributes();
//        $privilegeFieldStats = Helper::getPrivilegeFieldStats($this->getLoginUserClientId(),
//            $this->getLoginUserId(),
//            PrivilegeConstants::FUNCTIONAL_PURCHASE_SUPPLIER_PRODUCT);


    }

    /**
     * @param $product_id
     * @return false|string
     * @throws \xiaoman\orm\exception\QueryException
     * 产品已关联供应商选择器
     */
    public function actionProductSupplierSelector($product_id)
    {
        $user = User::getLoginUser();

        if(!Helper::hasPermission($user->getClientId(),$user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW))
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $filter = new SupplierProductFilter($user->getClientId());
        $filter->product_id = new Equal($product_id);
        $filter->enable_flag = new Equal(1);
        $filter->order('create_time','desc');
        $filter->limit(1000);
        $productList = $filter->find();
        $productList->getFormatter()->supplierSelectorSetting();
        $data = $productList->getListAttributes();

        $data = array_values(array_column($data,null, 'supplier_id'));
        return $this->success([
            'list' => $data,
            'count' => count($data)
        ]);
    }
}
