<?php

namespace common\models\prometheus;
use common\components\PrometheusModel;

/**
 * @property int id
 * @property int app_id
 * @property int event_id
 * @property int git_project_id
 * @property int git_commit_flag
 * @property string git_commit_id
 * @property string git_commit_message
 * @property string git_commit_author
 * @property string git_commit_time
 * @property int tapd_id
 * @property int tapd_object_type
 * @property int tapd_workspace_id
 * @property string record_comment
 * @property string record_attribution_type
 * @property string create_time
 * @property string update_time
 */
class ReleaseEventDiffRecordModel extends PrometheusModel
{
    public function tableName(): string
    {
        return 'tbl_release_event_diff_record';
    }
}