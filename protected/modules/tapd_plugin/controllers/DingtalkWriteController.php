<?php
/**
 * Created by PhpStorm.
 * User: young
 * Email: <<EMAIL>>
 * Date: 2024-05-23
 * Time: 13:35
 */

use common\modules\prometheus\library\dingtalk\DingtalkService;
use common\modules\prometheus\library\tapd\tapd_service_relation\dingtalk\TapdDingtalkGroup;
use common\modules\prometheus\library\tapd\tapd_service_relation\dingtalk\TapdDingtalkGroupList;
use common\modules\prometheus\library\tapd\TapdConstant;

class DingtalkWriteController extends TapdPluginController
{
    public function filters()
    {
        return [
            'tapdEmailCheck' => 'tapdEmailCheck',
        ];
    }

    /**
     * 创建钉钉群
     * @param $type
     * @param $tapd_id
     * @param $workspace_id
     * @return false|string
     * @throws Exception
     */
    public function actionCreateGroup(
        $type,
        $tapd_id,
        $workspace_id = TapdConstant::WORKSPACE_ID[TapdConstant::WORKSPACE_LME]
    )
    {
        $this->validate([
            'type'         => 'required|string|:' . implode(',', TapdConstant::TAPD_ENTITY_TYPE),
            'tapd_id'      => 'required|integer',
            'workspace_id' => 'required|integer',
        ]);

        $groupList = new TapdDingtalkGroupList();
        $groupList->setTapdId($tapd_id);
        $groupList->setTapdType($type);
        $groupList->setTapdWorkspaceId($workspace_id);
        $groupList->setEnableFlag(1);
        $group = $groupList->findOne();

        if (empty($group)) {
            switch ($type) {
                case TapdConstant::TAPD_TYPE_RELEASE:
                    $group = DingtalkService::createReleaseGroup($tapd_id, $type, $workspace_id);
            		break;
            	default:
            		break;
            }
        } else {
            $group = new TapdDingtalkGroup($group['relation_id']);
        }

        $ret = [
            'group_id' => $group->dingtalk_group_id,
            'title'    => $group->dingtalk_group_title,
            'url'      => $group->dingtalk_group_url,
        ];

        return $this->success($ret);
    }

    /**
     * 设置创建的发布计划钉钉群群主
     * @param $email
     * @return false|string
     */
    public function actionSetReleaseGroupOwner($email)
    {
        $ret = DingtalkService::setReleaseGroupOwner($email);

        return $this->success($ret);
    }
}