<?php

use common\library\account\service\WeComService;
use common\library\wecom\oauth\WecomAccountService;

class WeComReadController extends AssistantController
{

    public function filters()
    {
        return CMap::mergeArray(parent::filters(),array(
            'read'           => 'read',
            'login' => 'login - GetOauth2Url,GenerateLoginUrl,Home,Index,CallBackLogin',
        ));
    }

    /**
     * 根据code 返回 企微用户信息
     * @param string $code
     * @return false|string|void
     */
    public function actionGetUserInfoByCode(string $code)
    {
        $user = User::getLoginUser();
        $userId = empty($user)?0:$user->getUserId();
        $clientId = empty($user)?0:$user->getClientId();

        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId,$userId);
        $data = $wecomAccountService->getWecomUserInfo($code);

        $this->success($data);
    }

    /**
     * 构造第三方应用oauth2链接
     * https://open.weixin.qq.com/connect/oauth2/authorize?appid=环境suiteid&response_type=code&scope=snsapi_base&state=STATE&redirect_uri=encode(环境对应销售助手首页url)#wechat_redirect
     * @param string $redirect_uri   不要转urlencode，后端转  原始url
     */
    public function actionGetOauth2Url(string $redirect_uri){
        $user = User::getLoginUser();
        $userId = empty($user)?0:$user->getUserId();
        $clientId = empty($user)?0:$user->getClientId();
        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId,$userId);
        $data = $wecomAccountService->getOauth2Url($redirect_uri);

        $this->success($data);
    }

    /**
     * 企微生成免登url
     */
    public function actionGenerateLoginUrl(string $origin , string $code = '')
    {
        LogUtil::info("[GenerateLoginUrl] origin: $origin, code: $code" );

        $this->validate([
            'origin'  => 'required|in:' . implode(',', WeComService::WECOM_ORIGIN_LIST),
            'code'    => 'required|string',
        ]);

        $service = new \common\library\account\service\WeComService();
        $service->setOrigin($origin);
        $service->setCode($code);
        $result = $service->generateLoginUrl();

        $this->success($result);

    }


    /**
     * 前端JS签名
     */
    public function actionJsConfig(string $url = ''){

        $this->validate([
            'url' => 'required|string',
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId,$userId);
        $data = $wecomAccountService->getJsConfig($url);

        $this->success($data);
    }

    /**
     *   获取外部联系人详情
     */
    public function actionExternalUserInfo($open_user_id){
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId,$userId);
        $data = $wecomAccountService->getExternalUserInfo($open_user_id);

        $this->success($data);
    }




    /**
     *   获取企微用户绑定相关信息
     */
    public function actionWecomInfo(){
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $wecomOpenUserId = $user->getLoginData()['customData']['wecom_open_userid'] ?? '';

        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId,$userId);
        $data = $wecomAccountService->getWecomInfo($wecomOpenUserId);

        $this->success($data);
    }

    /**
     * homepage  绑定的相关信息
     * 有登录态 不用传参 code
     */
    public function actionHome(string $code = ''){

        $user = User::getLoginUser();

        $clientId = empty($user)?0:$user->getClientId();
        $userId = empty($user)?0:$user->getUserId();

        if(empty($code) && empty($userId)){
            throw  new RuntimeException('未登录或者code未传参');
        }

        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId,$userId);
        if(!empty($code)){
            $wecomAccountService->setCode($code);
            $data = $wecomAccountService->getHomeInfoBycode();
            $this->success($data);
        }

        $data = $wecomAccountService->getHomeInfoByUserId();
        $this->success($data);

    }

    public function actionRecommendCustomers($open_user_id) {
        $user = User::getLoginUser();

        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $wecomService = new \common\library\wecom\WeComCustomerService($clientId, $userId);
        $companyInfo = $wecomService->getCustomerInfo($open_user_id);
        return $this->success($companyInfo);
    }

    /**
     * 会话存档状态信息
     * @throws Exception
     */
    public function actionMessageSyncInfo(string $external_user_id='' )
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        $messageService = new \common\library\wecom\chat\WecomMessageService($clientId,$userId);
        $data = $messageService->getMessageSyncInfo($external_user_id);
        $this->success($data);
    }

    /**
     * 登录优化  联调
     */
    public function actionIndex(string $redirect_uri = ''): void
    {
        if (empty($redirect_uri)) {
            $callBackUrl = \Yii::app()->params['wecom']['call_back_url'];
            $redirect_uri = $callBackUrl . '/api/assistant/WeComRead/CallBackLogin';
        }
        $ua = new UserAgent();
        $weComService = new WeComService();
        if (!($ua->is_browser() || $ua->is_mobile())) {
            $this->redirect($weComService->redirect404Url());
        }
        $user = User::getLoginUser();
        $userId = empty($user) ? 0 : $user->getUserId();
        $clientId = empty($user) ? 0 : $user->getClientId();
        if ($userId > 0 && $clientId > 0) {
            $weComOpenUserId = $user->getLoginData()['customData']['wecom_open_userid'] ?? '';
            if (empty($weComOpenUserId)) {
                $this->redirect($weComService->redirectLoginExceptionUrl('login exception'));
            }
            $weComAccountService = new WecomAccountService(0, 0);
            $weComAccount = $weComAccountService->getWeComAccountByWeComOpenUserId($weComOpenUserId);
            if (isset($weComAccount['errorCode'])) {
                $this->redirect($weComService->redirectBindExceptionUrl($weComAccount));
            }
            if ($userId != $weComAccount['user_id']) {
                $userId = $weComAccount['user_id'];
                $weComOpenUserId = $weComAccount['wecom_open_userid'];
                try {
                    //用户登陆
                    $weComService->loginByUserId($userId, $weComOpenUserId);
                } catch (Exception $e) {
                    $this->redirect($weComService->redirectLoginExceptionUrl($e->getMessage()));
                }

            }
            if ($ua->is_mobile()) {
                $this->redirect($weComService->redirectMobileUrl($weComOpenUserId, $weComAccount['corp_id']));
            } else {
                $this->redirect($weComService->redirectDeskTopUrl($weComOpenUserId, $weComAccount['corp_id']));
            }

        }
        $weComAccountService = new \common\library\wecom\oauth\WecomAccountService($clientId, $userId);
        $data = $weComAccountService->getOauth2Url($redirect_uri);
        $this->redirect($data['oauth_url']);
    }

    /**
     * 回调登录
     */
    public function actionCallBackLogin(string $code = ''): void
    {
        $this->validate([
            'code' => 'required|string',
        ]);

        $ua = new UserAgent();
        $weComService = new WeComService();
        if (!($ua->is_browser() || $ua->is_mobile())) {
            $this->redirect($weComService->redirect404Url());
        }
        $origin = $ua->is_browser() ? WeComService::ORIGIN_WECOM_PC : WeComService::ORIGIN_WECOM_APP;
        LogUtil::info("[actionCallBackLogin] origin: $origin, code: $code");
        //获取用户配置
        $weComAccountService = new WecomAccountService(0, 0);
        $weComAccount = $weComAccountService->getWeComAccount($code);
        if (isset($weComAccount['errorCode'])) {
            $this->redirect($weComService->redirectBindExceptionUrl($weComAccount));
        }
        $userId = $weComAccount['user_id'];
        $clientId = $weComAccount['client_id'];
        $weComOpenUserId = $weComAccount['wecom_open_userid'];
        $corpId = $weComAccount['corp_id'];
        try {
            //用户登陆
            $weComService->loginByUserId($userId, $weComOpenUserId);
        } catch (Exception $e) {
            $this->redirect($weComService->redirectLoginExceptionUrl($e->getMessage()));
        }

        if ($ua->is_mobile()) {
            $this->redirect($weComService->redirectMobileUrl($weComOpenUserId, $corpId));
        } else {
            $this->redirect($weComService->redirectDeskTopUrl($weComOpenUserId, $corpId));
        }
    }

    public function actionSearchChatDataMsg(string $corp_id, string $start_time, string $end_time,
                                            string $keyword, string $cursor = '', int $pageSize = 100,
                                            string $open_userid = '', string $external_userid = '')
    {
        $this->validate([
            'corp_id' => 'required|string|not_empty',
            'keyword' => 'required|string|not_empty|min:2',
        ]);
        $service = new \common\library\wecom\chat\chat_data\WecomChatDataEnhancerService();
        $data = $service->searchMsg($corp_id, $keyword, $start_time, $end_time, $cursor, $pageSize, $open_userid, $external_userid);
        $this->success($data);
    }

}
