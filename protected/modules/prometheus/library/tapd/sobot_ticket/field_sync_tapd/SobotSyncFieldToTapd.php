<?php

namespace common\modules\prometheus\library\tapd\sobot_ticket\field_sync_tapd;

use common\modules\prometheus\library\sobot\sobot_ticket_bugs\SobotTicketBugsList;
use common\modules\prometheus\library\sobot\SobotConstant;
use common\modules\prometheus\library\tapd\TapdClient;

class SobotSyncFieldToTapd
{
    protected $ticketInfo;

    protected $tapdWorkspaceId = '47237250';
    protected $tapdTicketCode = 'custom_field_11';
    // shop-工单规模
    protected $sobotShopsTicketScale;
    protected $tapdShopsTicketScale = 'custom_field_23';
    // shop-工单类型 && 补充说明
    protected $sobotShopsTicketType;
    protected $tapdShopsTicketType = 'custom_field_26';
    //shop-服务商字段
    protected $tapdShopCustomer = 'custom_field_25';
    protected $sobotShopCustomer;
    protected $webhookDataArr;

    public function __construct(array $ticketInfo)
    {
        $this->ticketInfo = $ticketInfo;
    }

    protected function init()
    {
        $externalFieldMap = array_column($this->ticketInfo['extend_fields_list'], null, 'fieldid');
        $productCategory = $externalFieldMap[SobotConstant::EXTEND_FIELD_PRODUCT_CATEGORY_ID]['field_text']??'';
        if (!str_starts_with($productCategory, 'OKKI Shops')) {
            throw new \RuntimeException(__CLASS__ . "type is not shops,sobot_ticket_code:{$this->ticketInfo['ticket_code']}");
        }
        $this->webhookDataArr = [$this->sobotShopsTicketScale, $this->sobotShopsTicketType,$this->sobotShopCustomer] = $this->buildParamByExtendFileds($externalFieldMap);

    }

    protected function buildParamByExtendFileds($externalFieldMap)
    {
        $sobotShopsTicketScale = $externalFieldMap[SobotConstant::EXTEND_FIELD_SHOP_TICKET_SCALE_ID]['field_text'] ?? '';
        $sobotShopsTicketType = $externalFieldMap[SobotConstant::EXTEND_FIELD_SHOP_TICKET_TYPE_ID]['field_text'] ?? '';
        $sobotShopCustomer = $externalFieldMap[SobotConstant::EXTEND_FIELD_SHOP_CUSTOMER_ID]['field_value']??'';
        return [$sobotShopsTicketScale, $sobotShopsTicketType, $sobotShopCustomer];
    }
    public function syncFieldToTapd()
    {
        $this->init();
        $tapdClient = TapdClient::getInstance();
        $storyInfo = $tapdClient->getStories(['workspace_id' => $this->tapdWorkspaceId, "{$this->tapdTicketCode}" => $this->ticketInfo['ticket_code']]);

        if (empty($storyInfo))
        {
            throw new \RuntimeException(" sobot_ticket_code:{$this->ticketInfo['ticket_code']} story has not been created in tapd");
        }
        $tapdStoryList = array_column($storyInfo, 'Story');
        foreach ($tapdStoryList as $tapdStory) {
            $tapdDataArr = [$tapdStory[$this->tapdShopsTicketScale],$tapdStory[$this->tapdShopsTicketType],$tapdStory[$this->tapdShopCustomer]];
            if (empty(array_diff($this->webhookDataArr,$tapdDataArr))) {
                continue;
            }
            $param = [
                'current_user' => 'AUTO',
                'workspace_id' => $this->tapdWorkspaceId,
                'id' => $tapdStory['id'],
                $this->tapdShopCustomer => $this->sobotShopCustomer,
                $this->tapdShopsTicketScale => $this->sobotShopsTicketScale,
                $this->tapdShopsTicketType => str_replace(',','/',$this->sobotShopsTicketType)
            ];
            $tapdClient->postStories($param);
        }
    }
}
