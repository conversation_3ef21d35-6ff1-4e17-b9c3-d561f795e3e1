<?php
/**
 * 负责人配置表模型
 */
namespace common\modules\prometheus\library\responsible;

use common\components\PrometheusModel;

/**
 * @property int $id 自增主键
 * @property string $type 类型
 * @property string $base 配置项
 * @property string $refer 关联信息
 * @property string $responsibles 负责人(JSON格式)
 * @property int $enable_flag 是否启用
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 */
class ResponsibleConfigsModel extends PrometheusModel {
    /**
     * 返回表名
     * @return string
     */
    public function tableName()
    {
        return 'tbl_responsible_configs';
    }
    
    /**
     * 定义验证规则
     * @return array
     */
    public function rules()
    {
        return array(
            array('type', 'required'),
            array('base, refer', 'length', 'max' => 256),
            array('responsibles', 'safe'),
            array('enable_flag', 'numerical', 'integerOnly' => true),
        );
    }
    
    /**
     * 获取属性标签
     * @return array
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'type' => '类型',
            'base' => '配置项',
            'refer' => '关联信息',
            'responsibles' => '负责人',
            'enable_flag' => '是否启用',
            'create_time' => '创建时间',
            'update_time' => '更新时间',
        );
    }
    
    /**
     * 创建负责人配置
     *
     * @param array $data 配置数据，包含 type, base, refer, responsibles 等字段
     * @return array 包含状态和数据/错误信息的数组
     */
    public static function createConfig($data)
    {
        $model = new self();
        
        // 设置模型属性
        if (isset($data['type'])) {
            $model->type = $data['type'];
        }
        
        if (isset($data['base'])) {
            $model->base = $data['base'];
        }
        
        if (isset($data['refer'])) {
            $model->refer = $data['refer'];
        }
        
        if (isset($data['responsibles'])) {
            // 如果传入的是数组，转换为JSON字符串
            if (is_array($data['responsibles'])) {
                $model->responsibles = json_encode($data['responsibles']);
            } else {
                $model->responsibles = $data['responsibles'];
            }
        }
        
        if (isset($data['enable_flag'])) {
            $model->enable_flag = $data['enable_flag'] ? 1 : 0;
        } else {
            $model->enable_flag = 1; // 默认启用
        }
        
        // 保存模型
        if ($model->save()) {
            return [
                'status' => true,
                'data' => $model->attributes,
                'message' => '创建成功'
            ];
        } else {
            return [
                'status' => false,
                'errors' => $model->errors,
                'message' => '创建失败'
            ];
        }
    }

    /**
     * 更新负责人配置
     *
     * @param int $id 配置ID
     * @param array $data 要更新的数据
     * @return array 包含状态和数据/错误信息的数组
     */
    public static function updateConfig($id, $data)
    {
        // 输入验证
        if (empty($id) || !is_numeric($id)) {
            return [
                'status' => false,
                'message' => '无效的配置ID'
            ];
        }

        // 查找记录
        $model = new self();
        $connection = $model->getDbConnection();
        $tableName = $model->tableName();

        $sql = "SELECT * FROM {$tableName} WHERE id = :id";
        $command = $connection->createCommand($sql);
        $command->bindParam(':id', $id);
        $record = $command->queryRow();

        if (!$record) {
            return [
                'status' => false,
                'message' => '配置不存在'
            ];
        }

        // 准备更新数据
        $updateData = [];

        if (isset($data['base'])) {
            $updateData['base'] = $data['base'];
        }

        if (isset($data['refer'])) {
            $updateData['refer'] = $data['refer'];
        }

        if (isset($data['responsibles'])) {
            // 如果传入的是数组，转换为JSON字符串
            if (is_array($data['responsibles'])) {
                $updateData['responsibles'] = json_encode($data['responsibles']);
            } else {
                $updateData['responsibles'] = $data['responsibles'];
            }
        } else {
            $updateData['responsibles'] = null;
        }

        if (isset($data['enable_flag'])) {
            $updateData['enable_flag'] = $data['enable_flag'] ? 1 : 0;
        }

        // 如果没有要更新的数据，直接返回
        if (empty($updateData)) {
            return [
                'status' => true,
                'message' => '没有数据需要更新',
                'data' => $record
            ];
        }

        // 执行更新
        $connection->createCommand()->update($tableName, $updateData, 'id=:id', [':id' => $id]);

        return [];
    }

    public static function findByTypeBase($type, $base)
    {
        $connection = \Yii::app()->prometheus_db;
        $tableName = 'tbl_responsible_configs';
        
        $sql = "SELECT * FROM {$tableName} WHERE type = :type AND base = :base AND enable_flag = 1 LIMIT 1";
        
        $command = $connection->createCommand($sql);
        $command->bindParam(':type', $type);
        $command->bindParam(':base', $base);

        $result = $command->queryRow();
        
        if ($result) {
            // 解析JSON格式的负责人字段
            if (isset($result['responsibles'])) {
                $result['responsibles'] = json_decode($result['responsibles'], true);
            }
            $formatter = new ResponsibleConfigFormatter();
            $formatter->setData($result);
            $formatter->buildMapData();
            return [
                'status' => true,
                'data' => $formatter->format($result)
            ];
        }
        
        return [
            'status' => false,
            'message' => '未找到匹配的记录'
        ];
    }
}

